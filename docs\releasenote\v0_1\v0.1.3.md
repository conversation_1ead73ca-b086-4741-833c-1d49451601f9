# リリースノート v0.1.3

## 今回のリリースに含まれる Pull Request
## What's Changed
* update release note  by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/631
* update release note by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/632
* add release note v0.1.1 to v0.1.2 and update prompt for release note by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/629
* image generation of reference images by @snakajima in https://github.com/receptron/mulmocast-cli/pull/633
* add types to agents by @isamu in https://github.com/receptron/mulmocast-cli/pull/634
* Settings and env to config by @isamu in https://github.com/receptron/mulmocast-cli/pull/635
* add lint rule by @isamu in https://github.com/receptron/mulmocast-cli/pull/636
* fix fix_shorthand by @isamu in https://github.com/receptron/mulmocast-cli/pull/637
* Fix reassign by @isamu in https://github.com/receptron/mulmocast-cli/pull/638
* add sonarjs by @isamu in https://github.com/receptron/mulmocast-cli/pull/639
* fix no-nested-conditional by @isamu in https://github.com/receptron/mulmocast-cli/pull/640
* fix no-ignored-exceptions by @isamu in https://github.com/receptron/mulmocast-cli/pull/641
* Html template and multi-character template by @snakajima in https://github.com/receptron/mulmocast-cli/pull/628
* fix sonarjs/no-nested-conditional by @isamu in https://github.com/receptron/mulmocast-cli/pull/642
* fix no-shadow by @isamu in https://github.com/receptron/mulmocast-cli/pull/643
* girl and cat script: by @snakajima in https://github.com/receptron/mulmocast-cli/pull/644
* refactor image action by @isamu in https://github.com/receptron/mulmocast-cli/pull/645
* fix: remove noisy logs by @isamu in https://github.com/receptron/mulmocast-cli/pull/646
* a simple movie sample by @snakajima in https://github.com/receptron/mulmocast-cli/pull/648
* Refactor image action2 by @isamu in https://github.com/receptron/mulmocast-cli/pull/647
* Refactor image ref by @isamu in https://github.com/receptron/mulmocast-cli/pull/649
* Image update by @isamu in https://github.com/receptron/mulmocast-cli/pull/650
* update by @isamu in https://github.com/receptron/mulmocast-cli/pull/652
* update public image api by @isamu in https://github.com/receptron/mulmocast-cli/pull/651

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/v0.1.2...v0.1.3

--- 以下、Generated by Claude Code --- 

## Pull Request Summaries / PRサマリー

### PR #633: image generation of reference images - @snakajima (https://github.com/receptron/mulmocast-cli/pull/633)
- **English**: Implemented reference image generation functionality that allows creating character reference images directly from prompts. Added `generateReferenceImage` function with negative index (-1 based) session management for beat handling. Created test case `scripts/test/test_image_refs.json` and updated schema to support prompt-based image generation with `imagePrompt` type in `imageParams`. This enables consistent character generation across multi-scene content.
- **日本語**: プロンプトから直接キャラクターリファレンス画像を生成する機能を実装しました。ビート処理用の負のインデックス（-1ベース）セッション管理を持つ`generateReferenceImage`関数を追加。テストケース`scripts/test/test_image_refs.json`を作成し、`imageParams`内の`imagePrompt`タイプでプロンプトベースの画像生成をサポートするようスキーマを更新しました。マルチシーンコンテンツでの一貫したキャラクター生成を可能にします。

### PR #634: add types to agents - @isamu (https://github.com/receptron/mulmocast-cli/pull/634)
- **English**: Enhanced type safety by adding explicit type definitions to agent functions for image and TTS (text-to-speech) processing. Updated function signatures to include optional configuration fields like `baseURL` and improved environment variable references. Modified OpenAI image agent, Google TTS agent, and OpenAI TTS agent with proper type annotations and validation.
- **日本語**: 画像およびTTS（テキスト音声変換）処理のエージェント関数に明示的な型定義を追加して型安全性を強化しました。`baseURL`などのオプション設定フィールドを含めるよう関数シグネチャを更新し、環境変数の参照を改善しました。OpenAI画像エージェント、Google TTSエージェント、OpenAI TTSエージェントを適切な型注釈とバリデーションで修正しました。

### PR #635: Settings and env to config - @isamu (https://github.com/receptron/mulmocast-cli/pull/635)
- **English**: Refactored configuration management by improving `settings2GraphAIConfig` utility to accept both settings and environment variables. Added new `deepClean` function to sanitize configuration objects and updated setting processing in audio and image actions. Included comprehensive tests for new utility functions to ensure proper integration of user settings and environment variables.
- **日本語**: 設定と環境変数の両方を受け入れるよう`settings2GraphAIConfig`ユーティリティを改良し、設定管理をリファクタリングしました。設定オブジェクトを浄化する新しい`deepClean`関数を追加し、オーディオと画像アクションでの設定処理を更新しました。ユーザー設定と環境変数の適切な統合を確保するため、新しいユーティリティ関数の包括的なテストを含めました。

### PR #628: Html template and multi-character template - @snakajima (https://github.com/receptron/mulmocast-cli/pull/628)
- **English**: Added new templates for HTML-based presentations and multi-character storytelling. Introduced business presentation JSON template and multi-character story template in `assets/templates/` and `scripts/templates/` directories. Modified reference type validation to allow arbitrary strings for increased flexibility and reduced LLM generation failures. Templates support `htmlPrompt`-based presentation generation and multi-character narratives using generated image references.
- **日本語**: HTMLベースのプレゼンテーションとマルチキャラクターストーリーテリング用の新しいテンプレートを追加しました。`assets/templates/`と`scripts/templates/`ディレクトリにビジネスプレゼンテーション用JSONテンプレートとマルチキャラクターストーリーテンプレートを導入しました。柔軟性を高めLLM生成失敗を減らすため、任意の文字列を許可するようリファレンスタイプバリデーションを変更しました。テンプレートは`htmlPrompt`ベースのプレゼンテーション生成と生成された画像リファレンスを使用したマルチキャラクターナラティブをサポートします。

### PR #644: girl and cat script: - @snakajima (https://github.com/receptron/mulmocast-cli/pull/644)
- **English**: Added multimedia script example `scripts/snakajima/girl_and_cat.json` featuring "The Girl Who Listened" story with Studio Ghibli-style image generation. Defined characters Mio (girl) and black cat with consistent character prompts across 8 story beats. Demonstrates complete narrative creation with character definitions and story progression for multimedia content generation.
- **日本語**: スタジオジブリスタイルの画像生成を使用した「耳を澄ます少女」の物語を特徴とするマルチメディアスクリプト例`scripts/snakajima/girl_and_cat.json`を追加しました。Mio（少女）と黒猫のキャラクターを8つのストーリービートにわたって一貫したキャラクタープロンプトで定義しました。マルチメディアコンテンツ生成のためのキャラクター定義とストーリー進行を持つ完全なナラティブ作成を実証しています。

### PR #648: a simple movie sample - @snakajima (https://github.com/receptron/mulmocast-cli/pull/648)
- **English**: Added Japanese short animation movie sample `scripts/snakajima/replicate_movie_sample.json` titled "Moments of Childhood Joy". Configured for Studio Ghibli-style short film generation using Replicate provider with Japanese narration. Contains 2 beats covering meal scene and skating scene, demonstrating movie generation workflow for Japanese content.
- **日本語**: 「Moments of Childhood Joy」というタイトルの日本語短編アニメーション映画サンプル`scripts/snakajima/replicate_movie_sample.json`を追加しました。日本語ナレーションでReplicateプロバイダーを使用したスタジオジブリスタイルの短編映画生成に設定されています。食事シーンとスケートシーンをカバーする2つのビートを含み、日本語コンテンツの映画生成ワークフローを実証しています。

### PR #651: update public image api - @isamu (https://github.com/receptron/mulmocast-cli/pull/651)
- **English**: Refactored image generation functions to use single object parameter with named fields instead of multiple positional parameters. Updated function signatures across multiple files for improved code clarity and maintainability. Implemented proper destructuring with fallback handling and updated test cases to match new function signatures. Added public API documentation for better developer experience.
- **日本語**: 複数の位置パラメータの代わりに名前付きフィールドを持つ単一オブジェクトパラメータを使用するよう画像生成関数をリファクタリングしました。コードの明確性とメンテナンス性の向上のため、複数ファイルで関数シグネチャを更新しました。適切な分割代入とフォールバック処理を実装し、新しい関数シグネチャに合わせてテストケースを更新しました。より良い開発者エクスペリエンスのためパブリックAPIドキュメンテーションを追加しました。

---

## Developer Release Notes (English)

### New Features

- **Reference Image Generation** (#633): Added functionality to generate character reference images directly from prompts for consistent character creation across multi-scene content ([sample](scripts/test/test_image_refs.json))
- **HTML Templates and Multi-Character Support** (#628): Introduced templates for HTML-based presentations and multi-character storytelling ([templates](assets/templates/))

### Technical Improvements

- **Enhanced Type Safety** (#634): Added explicit type definitions to agent functions for image and TTS processing with improved configuration support
- **Configuration Management** (#635): Refactored settings management to integrate both user settings and environment variables with new `deepClean` utility function
- **Image API Refactoring** (#651): Updated image generation functions to use single object parameter with named fields for improved code clarity
- **Code Quality Enhancements** (#636-#643, #645-#647, #649-#650): Applied SonarJS linting rules, fixed code quality issues, and refactored image processing components
- **Logging Improvements** (#646): Removed noisy console output during processing

### Samples & Templates

- **Story Examples** (#644): Added "The Girl Who Listened" multimedia script with Studio Ghibli-style character definitions ([sample](scripts/snakajima/girl_and_cat.json))
- **Movie Sample** (#648): Added Japanese short animation movie sample demonstrating Replicate provider usage ([sample](scripts/snakajima/replicate_movie_sample.json))

### Documentation & Maintenance

- **Release Note Updates** (#629, #631, #632): Updated release note structure and documentation processes

---

## Developer Release Notes (Japanese)

### 新機能

- **リファレンス画像生成** (#633): マルチシーンコンテンツでの一貫したキャラクター作成のため、プロンプトから直接キャラクターリファレンス画像を生成する機能を追加 ([サンプル](scripts/test/test_image_refs.json))
- **HTMLテンプレートとマルチキャラクター対応** (#628): HTMLベースのプレゼンテーションとマルチキャラクターストーリーテリング用テンプレートを導入 ([テンプレート](assets/templates/))

### 技術的改善

- **型安全性の強化** (#634): 画像およびTTS処理のエージェント関数に明示的な型定義を追加し、設定サポートを改善
- **設定管理** (#635): ユーザー設定と環境変数を統合し、新しい`deepClean`ユーティリティ関数で設定管理をリファクタリング
- **画像APIリファクタリング** (#651): コードの明確性向上のため、名前付きフィールドを持つ単一オブジェクトパラメータを使用するよう画像生成関数を更新
- **コード品質の向上** (#636-#643, #645-#647, #649-#650): SonarJSリントルールの適用、コード品質問題の修正、画像処理コンポーネントのリファクタリング
- **ログ改善** (#646): 処理中のノイズの多いコンソール出力を削除

### サンプルとテンプレート

- **ストーリー例** (#644): スタジオジブリスタイルのキャラクター定義を持つ「耳を澄ます少女」マルチメディアスクリプトを追加 ([サンプル](scripts/snakajima/girl_and_cat.json))
- **映画サンプル** (#648): Replicateプロバイダー使用を実証する日本語短編アニメーション映画サンプルを追加 ([サンプル](scripts/snakajima/replicate_movie_sample.json))

### ドキュメントとメンテナンス

- **リリースノート更新** (#629, #631, #632): リリースノート構造とドキュメンテーションプロセスを更新

---

## Creator Release Notes (English)

### New Features

**Reference Images for Character Consistency**
- Generate reference images directly from text prompts to maintain consistent character appearance across multiple scenes
- Supports multi-scene storytelling with unified character design
- Test your character references with the provided sample ([sample](scripts/test/test_image_refs.json))

**HTML-Based Presentations**
- Create business presentations and slide decks using HTML templates
- Multi-character story templates for complex narratives
- Enhanced template flexibility for creative projects ([templates](assets/templates/))

### Creative Samples

**New Story Examples**
- "The Girl Who Listened" - A complete multimedia story with Studio Ghibli-style visuals ([sample](scripts/snakajima/girl_and_cat.json))
- Japanese short animation movie sample with Replicate provider integration ([sample](scripts/snakajima/replicate_movie_sample.json))

### System Improvements

- Improved processing reliability and reduced console noise during content generation
- Enhanced configuration management for better integration with different AI providers
- Updated core dependencies for better performance and stability

This release focuses on character consistency, template expansion, and improved creative workflows for multimedia content creation.

---

## Creator Release Notes (Japanese)

### 新機能

**キャラクター一貫性のためのリファレンス画像**
- テキストプロンプトから直接リファレンス画像を生成し、複数シーンでのキャラクター外観の一貫性を維持
- 統一されたキャラクターデザインでのマルチシーンストーリーテリングをサポート
- 提供されたサンプルでキャラクターリファレンスをテスト ([サンプル](scripts/test/test_image_refs.json))

**HTMLベースのプレゼンテーション**
- HTMLテンプレートを使用したビジネスプレゼンテーションとスライドデッキの作成
- 複雑なナラティブ用のマルチキャラクターストーリーテンプレート
- クリエイティブプロジェクト用の強化されたテンプレート柔軟性 ([テンプレート](assets/templates/))

### クリエイティブサンプル

**新しいストーリー例**
- 「耳を澄ます少女」- スタジオジブリスタイルのビジュアルを持つ完全なマルチメディアストーリー ([サンプル](scripts/snakajima/girl_and_cat.json))
- Replicateプロバイダー統合を含む日本語短編アニメーション映画サンプル ([サンプル](scripts/snakajima/replicate_movie_sample.json))

### システム改善

- コンテンツ生成中の処理信頼性の向上とコンソールノイズの削減
- 異なるAIプロバイダーとのより良い統合のための設定管理の強化
- より良いパフォーマンスと安定性のためのコア依存関係の更新

このリリースは、マルチメディアコンテンツ作成のためのキャラクター一貫性、テンプレート拡張、改善されたクリエイティブワークフローに焦点を当てています。

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.1.2.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-13
チェック者: Claude Code