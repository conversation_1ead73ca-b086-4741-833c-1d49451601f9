{"$mulmocast": {"version": "1.1", "credit": "closing"}, "lang": "en", "title": "Testing OpenAI's new Image Generation", "description": "Hello", "imageParams": {"model": "gpt-image-1", "style": "<style>Japanese animation with soft watercolor backgrounds, characters with simple rounded faces, large expressive eyes, small nose and mouth, soft jawlines, minimalist facial features, pastel color palette, detailed natural environments, whimsical magical elements, hand-drawn aesthetic, gentle lighting, flowing movement in hair and clothing, nostalgic countryside scenery with fantasy elements."}, "speechParams": {"speakers": {"Host": {"voiceId": "shimmer", "displayName": {"en": "Host"}}}}, "beats": [{"speaker": "Host", "text": "How are you?", "imagePrompt": "A witch in Harajuku", "imageParams": {"style": "Ukiyoe-style"}}]}