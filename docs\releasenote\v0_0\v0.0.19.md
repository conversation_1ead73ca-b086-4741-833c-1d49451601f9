# プロンプト
0.0.19 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.19

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
要約の文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は v0.0.16 を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## 今回のリリースに含まれる Pull Request
## What's Changed
* Credit test by @isamu in https://github.com/receptron/mulmocast-cli/pull/524
* fix MultiLingual length by @isamu in https://github.com/receptron/mulmocast-cli/pull/525
* bgmVolume, audioVolume by @snakajima in https://github.com/receptron/mulmocast-cli/pull/526
* MulmoStudioContextMethods by @isamu in https://github.com/receptron/mulmocast-cli/pull/527
* Add slideout_left transition by @snakajima in https://github.com/receptron/mulmocast-cli/pull/528
* More spillover by @snakajima in https://github.com/receptron/mulmocast-cli/pull/530
* music video example by @snakajima in https://github.com/receptron/mulmocast-cli/pull/532
* Refactor group by @isamu in https://github.com/receptron/mulmocast-cli/pull/533
* silentIds required amount by @snakajima in https://github.com/receptron/mulmocast-cli/pull/534
* add test_slideout_left_no_audio.json by @isamu in https://github.com/receptron/mulmocast-cli/pull/535
* audit fix by @isamu in https://github.com/receptron/mulmocast-cli/pull/537
* add test by @isamu in https://github.com/receptron/mulmocast-cli/pull/536
* fix movie only beat case by @snakajima in https://github.com/receptron/mulmocast-cli/pull/539
* update doc by @isamu in https://github.com/receptron/mulmocast-cli/pull/538
* Get caption image path by @isamu in https://github.com/receptron/mulmocast-cli/pull/542
* Image plugin by @isamu in https://github.com/receptron/mulmocast-cli/pull/540
* defaultOpenAIImageModel by @isamu in https://github.com/receptron/mulmocast-cli/pull/543
* docs: correct heading hierarchy in documentation image.md by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/541
* Add exports setting by @kawamataryo in https://github.com/receptron/mulmocast-cli/pull/546

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.18...0.0.19

## Pull Request Summaries (バイリンガル)

### PR #524: Credit test
- **English**: Added comprehensive test coverage for credit sequence functionality in videos without audio tracks. This test case (`test_no_audio_with_credit.json`) ensures that credit sequences render properly even when there's no accompanying audio, which is crucial for certain presentation styles where credits need to appear in silence. The test validates timing, rendering, and proper video generation in the absence of audio streams, catching edge cases that might otherwise go unnoticed in standard audio-accompanied videos. This strengthens the reliability of the credit feature across all use cases.
- **日本語**: 音声トラックのない動画でのクレジットシーケンス機能の包括的なテストカバレッジを追加しました。このテストケース（`test_no_audio_with_credit.json`）は、音声がない場合でもクレジットシーケンスが適切にレンダリングされることを保証します。これは、クレジットが無音で表示される必要がある特定のプレゼンテーションスタイルにとって重要です。このテストは、音声ストリームがない場合のタイミング、レンダリング、適切な動画生成を検証し、標準的な音声付き動画では見逃される可能性のあるエッジケースをキャッチします。これにより、すべてのユースケースでクレジット機能の信頼性が強化されます。

### PR #525: fix MultiLingual length
- **English**: Resolved a critical data structure mismatch issue where multilingual arrays could have different lengths than the beats array, causing runtime errors and data inconsistencies. The fix ensures that when beats are added or removed, corresponding empty multilingual text objects are automatically created or adjusted to maintain array parity. This prevents index out of bounds errors and ensures that translation data always aligns properly with the beat structure. The implementation carefully handles edge cases where beats might be dynamically added during processing, maintaining data integrity throughout the entire pipeline.
- **日本語**: 多言語配列がビート配列と異なる長さを持つ可能性があり、実行時エラーとデータの不整合を引き起こす重大なデータ構造の不一致問題を解決しました。この修正により、ビートが追加または削除されると、対応する空の多言語テキストオブジェクトが自動的に作成または調整され、配列の同等性が維持されます。これにより、インデックスの範囲外エラーが防止され、翻訳データが常にビート構造と適切に整合することが保証されます。この実装は、処理中にビートが動的に追加される可能性のあるエッジケースを慎重に処理し、パイプライン全体でデータの整合性を維持します。

### PR #526: bgmVolume, audioVolume
- **English**: Introduced sophisticated audio mixing capabilities through independent volume controls for background music and voice narration. The new `audioParams` properties `bgmVolume` (default: 0.2) and `audioVolume` (default: 1.0) enable precise audio balance customization, addressing a common creator need for adjusting the relative levels of music and speech. Additionally, this PR includes a new `image_prompt_only_template.json` template specifically designed for image-only presentations without text, expanding creative possibilities for visual storytelling. The volume controls use logarithmic scaling for perceptually linear volume adjustments, ensuring professional-quality audio mixing.
- **日本語**: バックグラウンドミュージックと音声ナレーションの独立したボリュームコントロールを通じて洗練された音声ミキシング機能を導入しました。新しい`audioParams`プロパティの`bgmVolume`（デフォルト: 0.2）と`audioVolume`（デフォルト: 1.0）により、音楽とスピーチの相対的なレベルを調整するクリエイターの一般的なニーズに対応し、正確な音声バランスのカスタマイズが可能になります。さらに、このPRには、テキストなしの画像のみのプレゼンテーション専用に設計された新しい`image_prompt_only_template.json`テンプレートが含まれており、ビジュアルストーリーテリングの創造的な可能性を拡大します。ボリュームコントロールは知覚的に線形なボリューム調整のための対数スケーリングを使用し、プロフェッショナル品質の音声ミキシングを保証します。

### PR #527: MulmoStudioContextMethods
- **English**: Comprehensive refactoring that centralizes all context access patterns through the `MulmoStudioContextMethods` module, replacing scattered direct property access throughout the codebase. This architectural improvement introduces methods like `getFileName()`, `getOutDirPath()`, and others, creating a consistent API for accessing context data. The refactoring enhances code maintainability by providing a single point of modification for context access logic, improves type safety through properly typed method signatures, and makes the codebase more resilient to future structural changes. This pattern also facilitates easier testing and mocking of context-dependent operations.
- **日本語**: コードベース全体に散在する直接的なプロパティアクセスを`MulmoStudioContextMethods`モジュールを通じて置き換え、すべてのコンテキストアクセスパターンを集約化する包括的なリファクタリングです。このアーキテクチャの改善により、`getFileName()`、`getOutDirPath()`などのメソッドが導入され、コンテキストデータにアクセスするための一貫したAPIが作成されます。このリファクタリングは、コンテキストアクセスロジックの単一の変更ポイントを提供することでコードの保守性を向上させ、適切に型付けされたメソッドシグネチャを通じて型安全性を改善し、将来の構造変更に対してコードベースをより堅牢にします。このパターンはまた、コンテキスト依存操作のテストとモッキングを容易にします。

### PR #528: Add slideout_left transition
- **English**: Implemented a new visual transition effect "slideout_left" that provides creators with more dynamic presentation options beyond the standard fade transition. This transition animates slides exiting to the left side of the screen, creating a sense of forward motion and progression. The implementation required careful coordination with the existing transition system, ensuring smooth animation timing and proper handling of both images and videos. The transition uses CSS transforms for hardware-accelerated performance and includes easing functions for natural-looking motion. This addition is part of a broader effort to give creators more creative control over visual storytelling.
- **日本語**: 標準的なフェードトランジションを超えてクリエイターにより動的なプレゼンテーションオプションを提供する新しいビジュアルトランジション効果「slideout_left」を実装しました。このトランジションは、スライドが画面の左側に退出するアニメーションを行い、前進と進行の感覚を作り出します。この実装には、既存のトランジションシステムとの慎重な調整が必要で、スムーズなアニメーションタイミングと画像と動画の両方の適切な処理を保証します。このトランジションは、ハードウェアアクセラレーションされたパフォーマンスのためにCSSトランスフォームを使用し、自然な見た目の動きのためのイージング関数を含みます。この追加は、クリエイターにビジュアルストーリーテリングに対するより多くの創造的コントロールを与えるより広範な取り組みの一部です。

### PR #530: More spillover
- **English**: Enhanced the audio spillover feature introduced in v0.0.17 with more sophisticated time distribution algorithms. When multiple beats share audio without specified durations, the system now intelligently distributes the available time evenly across all beats, with a minimum duration of 1 second per beat to ensure visibility. The enhancement handles complex scenarios where some beats have specified durations while others don't, calculating the remaining time and distributing it fairly among unspecified beats. This improvement is particularly valuable for music video creation where visual changes need to sync with a single continuous audio track while maintaining flexible timing for each visual element.
- **日本語**: v0.0.17で導入された音声スピルオーバー機能をより洗練された時間配分アルゴリズムで強化しました。複数のビートが指定された期間なしで音声を共有する場合、システムは利用可能な時間をすべてのビートに均等に配分し、視認性を確保するためにビートあたり最小1秒の期間を設定します。この強化により、一部のビートに指定された期間があり、他のビートにはない複雑なシナリオを処理し、残り時間を計算して未指定のビート間で公平に配分します。この改善は、各ビジュアル要素の柔軟なタイミングを維持しながら、ビジュアルの変化を単一の連続した音声トラックと同期させる必要があるミュージックビデオ作成に特に有用です。

### PR #532: music video example
- **English**: Added a comprehensive music video sample (`digital_democracy.json`) that demonstrates advanced audio-visual synchronization techniques. The example showcases how to create a music video by setting `bgmVolume` to 0 (disabling background music), specifying the main music track in the first beat's audio property, and using automatic time distribution across beats without explicit text or duration specifications. This sample serves as both a test case and a learning resource for creators wanting to produce music videos where visuals change in rhythm with the music while maintaining the continuous audio playback introduced by the spillover feature.
- **日本語**: 高度な音声ビジュアル同期技術を示す包括的なミュージックビデオサンプル（`digital_democracy.json`）を追加しました。この例は、`bgmVolume`を0に設定（バックグラウンドミュージックを無効化）し、最初のビートの音声プロパティにメイン音楽トラックを指定し、明示的なテキストや期間指定なしでビート間の自動時間配分を使用してミュージックビデオを作成する方法を示しています。このサンプルは、スピルオーバー機能によって導入された連続的な音声再生を維持しながら、音楽のリズムに合わせてビジュアルが変化するミュージックビデオを制作したいクリエイターのためのテストケースと学習リソースの両方として機能します。

### PR #533: Refactor group
- **English**: Performed strategic code refactoring by extracting core duration calculation logic into reusable functions `getMediaDurations()` and `getGroupBeatDurations()`. This refactoring significantly improves code readability and maintainability by separating complex duration calculations from the main processing flow. The extracted functions encapsulate the intricate logic for handling grouped beats, spillover calculations, and duration distribution, making the codebase more modular and testable. Additionally, variable names were improved for clarity (e.g., changing ambiguous names to descriptive ones), and comprehensive comments were added to explain the non-trivial aspects of duration calculations, particularly around spillover handling.
- **日本語**: コアの期間計算ロジックを再利用可能な関数`getMediaDurations()`と`getGroupBeatDurations()`に抽出する戦略的なコードリファクタリングを実行しました。このリファクタリングは、メイン処理フローから複雑な期間計算を分離することで、コードの可読性と保守性を大幅に改善します。抽出された関数は、グループ化されたビート、スピルオーバー計算、期間配分を処理する複雑なロジックをカプセル化し、コードベースをよりモジュラーでテスト可能にします。さらに、変数名は明確性のために改善され（例：曖昧な名前を説明的な名前に変更）、特にスピルオーバー処理に関する期間計算の自明でない側面を説明する包括的なコメントが追加されました。

### PR #534: silentIds required amount
- **English**: Optimized the silent audio generation process to create only the exact amount of silent audio files needed, rather than generating a fixed large number. This optimization reduces disk I/O operations, speeds up processing time, and decreases temporary storage requirements. The implementation intelligently calculates the required number of silent segments based on the actual beat structure and spillover patterns, preventing unnecessary file generation. This is particularly beneficial for projects with many beats but few silent segments, where the previous approach would waste resources generating unused silent audio files.
- **日本語**: 固定された大量の数を生成するのではなく、必要な正確な量の無音音声ファイルのみを作成するように無音音声生成プロセスを最適化しました。この最適化により、ディスクI/O操作が削減され、処理時間が短縮され、一時的なストレージ要件が減少します。この実装は、実際のビート構造とスピルオーバーパターンに基づいて必要な無音セグメントの数をインテリジェントに計算し、不要なファイル生成を防ぎます。これは、多くのビートがあるが無音セグメントが少ないプロジェクトに特に有益で、以前のアプローチでは使用されない無音音声ファイルを生成してリソースを無駄にしていました。

### PR #535: add test_slideout_left_no_audio.json
- **English**: Added a specific test case to validate the new slideout_left transition in scenarios without audio. This test ensures that the visual transition timing and rendering work correctly when there's no audio track to synchronize with, which is a common use case for image-based presentations. The test validates proper transition duration calculation, smooth animation execution, and correct final frame rendering. This addition to the test suite helps maintain the reliability of the new transition feature across different content types and ensures regression prevention in future updates.
- **日本語**: 音声なしのシナリオで新しいslideout_leftトランジションを検証する特定のテストケースを追加しました。このテストは、同期する音声トラックがない場合でもビジュアルトランジションのタイミングとレンダリングが正しく機能することを保証します。これは画像ベースのプレゼンテーションの一般的なユースケースです。このテストは、適切なトランジション期間計算、スムーズなアニメーション実行、正しい最終フレームレンダリングを検証します。テストスイートへのこの追加は、異なるコンテンツタイプにわたる新しいトランジション機能の信頼性を維持し、将来の更新でのリグレッション防止を保証します。

### PR #536: add test
- **English**: Expanded the release test suite by adding `digital_democracy.json` and `test_spillover.json` to ensure comprehensive validation of new features before releases. These tests cover complex scenarios including music video generation with spillover audio and advanced time distribution patterns. The addition of these tests to the release validation process helps catch integration issues that might not be apparent in unit tests, ensuring that the complete end-to-end workflow functions correctly. This strengthens the overall quality assurance process and reduces the likelihood of releasing builds with broken features.
- **日本語**: リリース前の新機能の包括的な検証を保証するため、`digital_democracy.json`と`test_spillover.json`を追加してリリーステストスイートを拡張しました。これらのテストは、スピルオーバー音声を使用したミュージックビデオ生成と高度な時間配分パターンを含む複雑なシナリオをカバーしています。リリース検証プロセスへのこれらのテストの追加は、単体テストでは明らかにならない可能性のある統合問題をキャッチし、完全なエンドツーエンドのワークフローが正しく機能することを保証します。これにより、全体的な品質保証プロセスが強化され、機能が壊れたビルドをリリースする可能性が減少します。

### PR #537: audit fix
- **English**: Addressed security vulnerabilities identified by npm audit in the `brace-expansion` and `minimatch` packages. These packages are transitive dependencies used in file pattern matching throughout the system. The updates patch known security issues including ReDoS (Regular Expression Denial of Service) vulnerabilities that could potentially be exploited to cause performance degradation. While these vulnerabilities were low risk in the context of MulmoCast (which processes trusted local files), maintaining up-to-date dependencies is a security best practice that prevents potential future exploits and ensures compliance with security auditing tools.
- **日本語**: npmの監査によって`brace-expansion`と`minimatch`パッケージで特定されたセキュリティ脆弱性に対処しました。これらのパッケージは、システム全体でファイルパターンマッチングに使用される推移的な依存関係です。更新により、パフォーマンスの低下を引き起こす可能性のあるReDoS（正規表現サービス拒否）脆弱性を含む既知のセキュリティ問題にパッチが適用されます。これらの脆弱性は、信頼できるローカルファイルを処理するMulmoCastのコンテキストでは低リスクでしたが、最新の依存関係を維持することは、将来の潜在的な悪用を防ぎ、セキュリティ監査ツールへの準拠を保証するセキュリティのベストプラクティスです。

### PR #538: update doc
- **English**: Significantly enhanced the `image.md` documentation with comprehensive examples demonstrating the interplay between `imagePrompt`, `moviePrompt`, and their various combinations. The documentation now includes detailed explanations of how these prompts interact, when each is used, and how they affect the final output. Additionally, a new `fake_data` npm script was added to facilitate testing with synthetic data, making it easier for developers to experiment with different prompt combinations without needing real content. The enhanced documentation serves as both a reference guide and a tutorial, helping users understand the sophisticated image generation capabilities of MulmoCast.
- **日本語**: `imagePrompt`、`moviePrompt`、およびそれらの様々な組み合わせの相互作用を示す包括的な例で`image.md`ドキュメントを大幅に強化しました。ドキュメントには、これらのプロンプトがどのように相互作用するか、それぞれがいつ使用されるか、最終的な出力にどのように影響するかの詳細な説明が含まれるようになりました。さらに、合成データでのテストを容易にする新しい`fake_data` npmスクリプトが追加され、開発者が実際のコンテンツを必要とせずに異なるプロンプトの組み合わせを実験しやすくなりました。強化されたドキュメントは、リファレンスガイドとチュートリアルの両方として機能し、ユーザーがMulmoCastの洗練された画像生成機能を理解するのに役立ちます。

### PR #539: fix movie only beat case
- **English**: Fixed a subtle but important bug where beats containing only movie content (no text or images) would have incorrect duration calculations, leading to synchronization issues. This bug was discovered when background music would end prematurely in the `test_no_audio` scenario. The fix ensures that movie-only beats properly calculate their duration based on the video file length, maintaining correct timing throughout the presentation. This is particularly important for presentations that mix different content types, ensuring that transitions and audio synchronization remain accurate regardless of the beat content type.
- **日本語**: 動画コンテンツのみを含む（テキストや画像なし）ビートが不正確な期間計算を持ち、同期の問題につながる微妙だが重要なバグを修正しました。このバグは、`test_no_audio`シナリオでバックグラウンドミュージックが早期に終了することで発見されました。この修正により、動画のみのビートがビデオファイルの長さに基づいて適切に期間を計算し、プレゼンテーション全体で正しいタイミングを維持することが保証されます。これは、異なるコンテンツタイプを混在させるプレゼンテーションにとって特に重要で、ビートコンテンツタイプに関係なくトランジションと音声同期が正確であることを保証します。

### PR #540: Image plugin
- **English**: Implemented a sophisticated plugin architecture for image processing by separating concerns into two distinct agents: `preprocessor` (handles metadata extraction and validation) and `imagePlugin` (performs actual image generation/processing). This architectural change enables more flexible image processing pipelines, allows third-party plugins to be integrated more easily, and improves testability by separating data flow from execution. The new path API provides plugins with standardized access to file system operations while maintaining security boundaries. This foundation enables future enhancements like custom image filters, AI-powered image enhancement, or integration with external image processing services.
- **日本語**: 画像処理のための洗練されたプラグインアーキテクチャを実装し、関心事を2つの異なるエージェントに分離しました：`preprocessor`（メタデータの抽出と検証を処理）と`imagePlugin`（実際の画像生成/処理を実行）。このアーキテクチャの変更により、より柔軟な画像処理パイプラインが可能になり、サードパーティプラグインをより簡単に統合でき、データフローと実行を分離することでテスト可能性が向上します。新しいパスAPIは、セキュリティ境界を維持しながら、プラグインにファイルシステム操作への標準化されたアクセスを提供します。この基盤により、カスタム画像フィルター、AI駆動の画像強化、外部画像処理サービスとの統合などの将来の機能強化が可能になります。

### PR #541: docs: correct heading hierarchy in documentation image.md
- **English**: Restructured the `image.md` documentation to follow proper markdown heading hierarchy, improving document navigation and accessibility. The key addition is a comprehensive table that clearly maps all possible combinations of beat properties (`instruction`, `image`, `imagePrompt`, `moviePrompt`) to their resulting behavior. This table serves as a quick reference for understanding MulmoCast's complex image generation rules, eliminating ambiguity about which properties take precedence in different scenarios. The improved structure also makes the documentation more compatible with documentation generation tools and improves SEO for online documentation portals.
- **日本語**: 適切なマークダウン見出し階層に従うように`image.md`ドキュメントを再構築し、ドキュメントのナビゲーションとアクセシビリティを改善しました。主な追加は、ビートプロパティ（`instruction`、`image`、`imagePrompt`、`moviePrompt`）のすべての可能な組み合わせを、それらの結果となる動作に明確にマッピングする包括的な表です。この表は、MulmoCastの複雑な画像生成ルールを理解するためのクイックリファレンスとして機能し、異なるシナリオでどのプロパティが優先されるかについての曖昧さを排除します。改善された構造はまた、ドキュメント生成ツールとの互換性を高め、オンラインドキュメントポータルのSEOを改善します。

### PR #542: Get caption image path
- **English**: Introduced the `getCaptionImagePath()` utility function to centralize and standardize caption image path generation logic. Previously, caption image paths were constructed ad-hoc in multiple places, leading to potential inconsistencies and maintenance challenges. This new function ensures that all caption images follow a consistent naming convention and directory structure, making it easier to locate, manage, and clean up caption-related assets. The centralized approach also facilitates future enhancements like caption image caching, different caption styles, or alternative storage locations for caption assets.
- **日本語**: キャプション画像パス生成ロジックを集約化し標準化するための`getCaptionImagePath()`ユーティリティ関数を導入しました。以前は、キャプション画像パスが複数の場所でアドホックに構築されており、潜在的な不整合とメンテナンスの課題につながっていました。この新しい関数は、すべてのキャプション画像が一貫した命名規則とディレクトリ構造に従うことを保証し、キャプション関連のアセットの検索、管理、クリーンアップを容易にします。集約化されたアプローチはまた、キャプション画像のキャッシング、異なるキャプションスタイル、キャプションアセットの代替ストレージ場所などの将来の機能強化を促進します。

### PR #543: defaultOpenAIImageModel
- **English**: Added configuration flexibility for OpenAI image model selection through the `DEFAULT_OPENAI_IMAGE_MODEL` environment variable. This enhancement allows users to specify alternative OpenAI image models (such as different DALL-E versions or future models) without modifying code. The implementation includes proper fallback to "dall-e-3" when the environment variable is not set, ensuring backward compatibility. This feature is particularly valuable for users who have access to newer or specialized OpenAI image models, want to test different models for quality comparison, or need to use specific models for compliance or cost reasons.
- **日本語**: `DEFAULT_OPENAI_IMAGE_MODEL`環境変数を通じてOpenAI画像モデル選択の設定柔軟性を追加しました。この機能強化により、ユーザーはコードを変更することなく代替のOpenAI画像モデル（異なるDALL-Eバージョンや将来のモデルなど）を指定できます。実装には、環境変数が設定されていない場合の「dall-e-3」への適切なフォールバックが含まれており、後方互換性が保証されます。この機能は、新しいまたは特殊なOpenAI画像モデルにアクセスできるユーザー、品質比較のために異なるモデルをテストしたいユーザー、またはコンプライアンスやコスト上の理由で特定のモデルを使用する必要があるユーザーにとって特に価値があります。

### PR #546: Add exports setting
- **English**: Resolved a critical browser compatibility issue by implementing proper module exports configuration in `package.json`. The issue arose because browser environments don't have Node.js globals like `process`, causing runtime errors when importing MulmoCast schemas. The solution involved creating a separate browser-compatible entry point that excludes Node.js-specific dependencies while maintaining full schema functionality. This fix is crucial for enabling web-based tools and applications to use MulmoCast schemas for validation, opening possibilities for browser-based MulmoCast editors, validators, and preview tools. The implementation carefully preserves all existing Node.js functionality while adding browser support.
- **日本語**: `package.json`に適切なモジュールエクスポート設定を実装することで、重要なブラウザ互換性の問題を解決しました。この問題は、ブラウザ環境に`process`のようなNode.jsグローバルがないため、MulmoCastスキーマをインポートする際に実行時エラーが発生することから生じました。解決策は、完全なスキーマ機能を維持しながらNode.js固有の依存関係を除外する別のブラウザ互換エントリポイントを作成することでした。この修正は、ウェブベースのツールやアプリケーションが検証のためにMulmoCastスキーマを使用できるようにするために重要で、ブラウザベースのMulmoCastエディター、バリデーター、プレビューツールの可能性を開きます。実装は、ブラウザサポートを追加しながら、既存のすべてのNode.js機能を慎重に保持します。

## Release Notes – Developer-Focused (English)

MulmoCast CLI v0.0.19 is a feature-rich release that significantly enhances audio control, introduces new visual transitions, and improves the overall developer experience through better architecture and comprehensive documentation:

### Major New Features:
- **Advanced Audio Mixing Controls**: Independent volume controls for background music (`bgmVolume`, default: 0.2) and voice narration (`audioVolume`, default: 1.0), enabling professional-quality audio balance in presentations. The logarithmic scaling ensures perceptually linear volume adjustments.
- **Slideout Left Transition**: New dynamic visual transition where slides exit to the left side of the screen, providing creators with more storytelling options beyond standard fade effects. Hardware-accelerated CSS transforms ensure smooth performance.
- **Enhanced Spillover Algorithm**: Sophisticated time distribution for audio spillover scenarios. When durations aren't specified, the system intelligently distributes available time evenly across beats with a 1-second minimum, perfect for music video creation.
- **Music Video Workflow**: Dedicated support for creating music videos with automatic visual synchronization to a single audio track. Set `bgmVolume` to 0 and specify music in the first beat for seamless music video production.

### Architecture & Plugin System:
- **Image Plugin Architecture**: Split image processing into separate `preprocessor` and `imagePlugin` agents, enabling flexible third-party plugin integration and improved testability. The new path API provides standardized file system access.
- **Context Methods Refactoring**: Centralized all context access through `MulmoStudioContextMethods` module, replacing scattered property access. New methods include `getFileName()`, `getCaption()`, and `getOutDirPath()`.
- **Duration Calculation Functions**: Extracted `getMediaDurations()` and `getGroupBeatDurations()` for better code organization and reusability.
- **Caption Path Utility**: New `getCaptionImagePath()` function centralizes caption image path generation logic, ensuring consistency across the system.

### Bug Fixes & Improvements:
- **Multilingual Array Sync**: Fixed critical issue where multilingual arrays could have different lengths than beats array, now automatically maintains parity.
- **Movie-Only Beat Timing**: Resolved duration calculation bug for beats containing only video content, ensuring proper BGM synchronization.
- **Browser Compatibility**: Fixed "process is not defined" errors by implementing proper exports configuration with separate browser entry point.
- **Security Updates**: Patched ReDoS vulnerabilities in `brace-expansion` and `minimatch` dependencies.
- **Silent Audio Optimization**: Generates only required silent audio files based on actual beat structure, reducing I/O overhead.

### Developer Experience:
- **Comprehensive Documentation**: Enhanced `image.md` with detailed examples of `imagePrompt`/`moviePrompt` interactions and added complete image generation rules table.
- **Configurable Image Models**: Support for `DEFAULT_OPENAI_IMAGE_MODEL` environment variable to use different DALL-E versions or future models.
- **Expanded Test Coverage**: New test cases for credit sequences, slideout transitions, spillover scenarios, and music video generation.
- **Development Tools**: Added `fake_data` npm script for testing with synthetic data.

### New Templates & Features:
- **Image-Only Template**: New `image_prompt_only_template.json` for visual presentations without text
- **Improved Error Visibility**: Better error messages and validation in test environments

### Breaking Changes:
- None - all changes maintain backward compatibility

This release significantly improves the creative capabilities of MulmoCast while strengthening its technical foundation through better architecture and comprehensive testing.

## リリースノート – 開発者向け (日本語)

MulmoCast CLI v0.0.19は、音声コントロールの大幅な強化、新しいビジュアルトランジションの導入、より良いアーキテクチャと包括的なドキュメントによる開発者体験の向上を実現する機能豊富なリリースです：

### 主要な新機能:
- **高度な音声ミキシングコントロール**: BGM（`bgmVolume`、デフォルト: 0.2）と音声ナレーション（`audioVolume`、デフォルト: 1.0）の独立したボリュームコントロールにより、プレゼンテーションでプロフェッショナル品質の音声バランスを実現。対数スケーリングにより知覚的に線形なボリューム調整を保証。
- **スライドアウト左トランジション**: スライドが画面の左側に退出する新しい動的ビジュアルトランジション。標準的なフェード効果を超えたストーリーテリングオプションをクリエイターに提供。ハードウェアアクセラレーションされたCSSトランスフォームによりスムーズなパフォーマンスを保証。
- **強化されたスピルオーバーアルゴリズム**: 音声スピルオーバーシナリオのための洗練された時間配分。期間が指定されていない場合、システムは利用可能な時間を最小1秒でビート間に均等に配分。ミュージックビデオ作成に最適。
- **ミュージックビデオワークフロー**: 単一の音声トラックへの自動ビジュアル同期によるミュージックビデオ作成の専用サポート。`bgmVolume`を0に設定し、最初のビートで音楽を指定することでシームレスなミュージックビデオ制作が可能。

### アーキテクチャ・プラグインシステム:
- **画像プラグインアーキテクチャ**: 画像処理を`preprocessor`と`imagePlugin`エージェントに分離し、柔軟なサードパーティプラグイン統合とテスト可能性の向上を実現。新しいパスAPIが標準化されたファイルシステムアクセスを提供。
- **コンテキストメソッドのリファクタリング**: 散在するプロパティアクセスを`MulmoStudioContextMethods`モジュールを通じて集約化。新しいメソッドには`getFileName()`、`getCaption()`、`getOutDirPath()`を含む。
- **期間計算関数**: より良いコード構成と再利用性のために`getMediaDurations()`と`getGroupBeatDurations()`を抽出。
- **キャプションパスユーティリティ**: 新しい`getCaptionImagePath()`関数がキャプション画像パス生成ロジックを集約化し、システム全体の一貫性を保証。

### バグ修正・改善:
- **多言語配列の同期**: 多言語配列がビート配列と異なる長さを持つ可能性がある重大な問題を修正。現在は自動的に同等性を維持。
- **動画のみのビートタイミング**: 動画コンテンツのみを含むビートの期間計算バグを解決し、適切なBGM同期を保証。
- **ブラウザ互換性**: 別のブラウザエントリポイントを持つ適切なexports設定を実装することで「process is not defined」エラーを修正。
- **セキュリティ更新**: `brace-expansion`と`minimatch`依存関係のReDoS脆弱性にパッチを適用。
- **無音音声の最適化**: 実際のビート構造に基づいて必要な無音音声ファイルのみを生成し、I/Oオーバーヘッドを削減。

### 開発者体験:
- **包括的なドキュメント**: `imagePrompt`/`moviePrompt`の相互作用の詳細な例で`image.md`を強化し、完全な画像生成ルール表を追加。
- **設定可能な画像モデル**: 異なるDALL-Eバージョンや将来のモデルを使用するための`DEFAULT_OPENAI_IMAGE_MODEL`環境変数のサポート。
- **拡張されたテストカバレッジ**: クレジットシーケンス、スライドアウトトランジション、スピルオーバーシナリオ、ミュージックビデオ生成の新しいテストケース。
- **開発ツール**: 合成データでのテスト用の`fake_data` npmスクリプトを追加。

### 新しいテンプレート・機能:
- **画像のみのテンプレート**: テキストなしのビジュアルプレゼンテーション用の新しい`image_prompt_only_template.json`
- **改善されたエラー可視性**: テスト環境でのより良いエラーメッセージと検証

### 破壊的変更:
- なし - すべての変更は後方互換性を維持

このリリースは、より良いアーキテクチャと包括的なテストを通じて技術的基盤を強化しながら、MulmoCastの創造的な機能を大幅に向上させます。

## Release Notes – Creator-Focused (English)

MulmoCast CLI v0.0.19 brings exciting new creative tools and improvements that give you more control over your multimedia presentations:

### Audio Mastery: Perfect Your Sound Mix
- **Independent Volume Controls**: Fine-tune your audio with separate controls for background music and narration. Set the perfect balance where your voice comes through clearly while music sets the mood.
- **Music Video Creation**: Transform your presentations into music videos! Disable the default BGM and sync all your visuals to a single music track - perfect for creating engaging promotional content or artistic projects.

### Visual Effects: New Ways to Transition
- **Slideout Left Effect**: Your slides can now exit stage left! This new transition adds dynamic movement to your presentations, creating a sense of progression and flow that keeps viewers engaged.
- **Smooth Timing**: When you don't specify exact durations, MulmoCast now intelligently distributes time across your beats, ensuring each visual gets its moment to shine.

### Templates & Workflows:
- **Image-Only Presentations**: New template for creating purely visual stories - no text required. Perfect for photo essays, visual portfolios, or letting your images speak for themselves.
- **Enhanced Spillover**: Continue your narration seamlessly across multiple visuals. Start telling your story and let the images change while your voice carries the narrative forward.

### What We Fixed:
- **Multilingual Sync**: All language versions of your presentation now stay perfectly synchronized
- **Video Timing**: Presentations with video-only segments now maintain correct timing throughout
- **Web Compatibility**: If you're using web-based tools, they'll now work smoothly with MulmoCast

### Better Documentation:
- Clearer examples showing how to combine different image and video prompts
- Comprehensive table explaining exactly how MulmoCast generates images based on your settings
- More sample files to learn from and build upon

### Pro Tips:
- Use `bgmVolume: 0` in your script to create music videos
- Combine the new slideout transition with spillover audio for documentary-style presentations
- Check out the new `digital_democracy.json` example for music video inspiration

This release empowers you to create more sophisticated and engaging presentations with finer control over every aspect of your audio-visual experience.

## リリースノート – クリエイター向け (日本語)

MulmoCast CLI v0.0.19は、マルチメディアプレゼンテーションをより細かくコントロールできる、エキサイティングな新しいクリエイティブツールと改善をもたらします：

### 音声の極み：完璧なサウンドミックスを
- **独立したボリュームコントロール**: BGMとナレーションに別々のコントロールで音声を微調整。音楽が雰囲気を演出しながら、あなたの声がクリアに聞こえる完璧なバランスを設定できます。
- **ミュージックビデオ作成**: プレゼンテーションをミュージックビデオに変換！デフォルトのBGMを無効にして、すべてのビジュアルを単一の音楽トラックに同期 - 魅力的なプロモーションコンテンツやアーティスティックなプロジェクトの作成に最適。

### ビジュアルエフェクト：新しいトランジション方法
- **スライドアウト左エフェクト**: スライドが左に退場できるようになりました！この新しいトランジションは、プレゼンテーションに動的な動きを加え、視聴者を引き付ける進行とフローの感覚を作り出します。
- **スムーズなタイミング**: 正確な期間を指定しない場合、MulmoCastは時間をビート間でインテリジェントに配分し、各ビジュアルが輝く瞬間を確保します。

### テンプレート・ワークフロー:
- **画像のみのプレゼンテーション**: 純粋にビジュアルなストーリーを作成するための新しいテンプレート - テキストは不要。フォトエッセイ、ビジュアルポートフォリオ、または画像に語らせるのに最適。
- **強化されたスピルオーバー**: 複数のビジュアルにわたってナレーションをシームレスに続行。ストーリーを語り始め、あなたの声が物語を前に進める間に画像を変化させましょう。

### 修正内容:
- **多言語同期**: プレゼンテーションのすべての言語バージョンが完全に同期を保つように
- **ビデオタイミング**: ビデオのみのセグメントを含むプレゼンテーションが全体を通して正しいタイミングを維持
- **ウェブ互換性**: ウェブベースのツールを使用している場合、MulmoCastでスムーズに動作するように

### より良いドキュメント:
- 異なる画像とビデオプロンプトを組み合わせる方法を示すより明確な例
- 設定に基づいてMulmoCastがどのように画像を生成するかを正確に説明する包括的な表
- 学習と構築のためのより多くのサンプルファイル

### プロのヒント:
- スクリプトで`bgmVolume: 0`を使用してミュージックビデオを作成
- 新しいスライドアウトトランジションとスピルオーバー音声を組み合わせてドキュメンタリースタイルのプレゼンテーションを
- ミュージックビデオのインスピレーションのために新しい`digital_democracy.json`の例をチェック

このリリースは、音声ビジュアル体験のあらゆる側面をより細かくコントロールして、より洗練された魅力的なプレゼンテーションを作成する力を与えます。