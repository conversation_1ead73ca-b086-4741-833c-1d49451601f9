{"$mulmocast": {"version": "1.1"}, "movieParams": {"provider": "replicate"}, "audioParams": {"bgm": {"kind": "url", "url": "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/olympic001.mp3"}, "suppressSpeech": true}, "captionParams": {"lang": "en"}, "lang": "en", "beats": [{"id": "veo-2", "text": "google/veo-2", "duration": 5, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "google/veo-2"}}, {"id": "pixverse-v4.5", "text": "pixverse/pixverse-v4.5", "duration": 5, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "pixverse/pixverse-v4.5"}}, {"id": "video-01", "text": "minimax/video-01", "duration": 6, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "minimax/video-01"}}, {"id": "seedance-1-lite", "text": "bytedance/seedance-1-lite", "duration": 5, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "bytedance/seedance-1-lite"}}, {"id": "kling-v1.6-pro", "text": "kwaivgi/kling-v1.6-pro", "duration": 5, "imagePrompt": "televised footage of a cat is ready to dive into a swimming pool at the olympics, from a 10m high diving board. No text.", "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "kwaivgi/kling-v1.6-pro"}}, {"id": "kling-v2.1-master", "text": "kwaivgi/kling-v2.1-master", "duration": 5, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "kwaivgi/kling-v2.1-master"}}, {"id": "seedance-1-pro", "text": "bytedance/seedance-1-pro", "duration": 5, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "bytedance/seedance-1-pro"}}, {"id": "hailuo-02", "text": "minimax/hailuo-02", "duration": 6, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "minimax/hailuo-02"}}, {"id": "veo-3-fast", "text": "google/veo-3-fast", "duration": 8, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "google/veo-3-fast"}}, {"id": "veo-3", "text": "google/veo-3", "duration": 8, "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "movieParams": {"model": "google/veo-3"}}, {"id": "credits", "duration": 10, "image": {"type": "textSlide", "slide": {"title": "Credits", "bullets": ["Producer: @snaka<PERSON> (on X and GitHub)", "Prompt: televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "Tool: MulmoCast (AI-native multi-modal presentation tool, https://github.com/receptron/mulmocast-cli)", "Script: https://github.com/receptron/mulmocast-cli/blob/main/scripts/snakajima/olympics.json", "Models: google/veo-2, pixverse/pixverse-v4.5, minimax/video-01, bytedance/seedance-1-lite, kwaivgi/kling-v1.6-pro, kwaivgi/kling-v2.1-master, bytedance/seedance-1-pro, minimax/hailuo-02, google/veo-3-fast, google/veo-3"]}}}]}