# リリースノート v1.1.5

## 今回のリリースに含まれる Pull Request

--- 以下、Generated by Claude Code ---

## What's Changed

1. PR #741: Image Quality - @snakajima
2. PR #742: lang suffix for audio file - @snakajima
3. PR #744: use getAudioArtifactFilePath from both audio and movie - @snakajima
4. PR #743: Language specific speakers - @snakajima
5. PR #745: export test script - @isamu

Full Changelog: https://github.com/receptron/mulmocast-cli/compare/1.1.4...1.1.5

## Pull Request Summaries

### PR #741: Image Quality - @snakajima (https://github.com/receptron/mulmocast-cli/pull/741)
- **English**: Added quality parameter support for OpenAI image generation. Implemented `quality` field in image parameters schema supporting "low", "medium", "high", and "auto" values. Updated OpenAI image agent to pass quality parameter to API calls for models other than gpt-image-1. Added test beats in test_images.json demonstrating low and medium quality image generation. Modified image generation pipeline to pass quality parameter from beat-level imageParams to the OpenAI agent. Removed debug console.log statement from file cache filter.
- **日本語**: OpenAI画像生成のクオリティパラメータサポートを追加しました。"low"、"medium"、"high"、"auto"値をサポートする画像パラメータスキーマに`quality`フィールドを実装しました。gpt-image-1以外のモデルでAPI呼び出しにクオリティパラメータを渡すようにOpenAI画像エージェントを更新しました。低品質と中品質の画像生成を実証するテストビートをtest_images.jsonに追加しました。ビートレベルのimageParamsからOpenAIエージェントにクオリティパラメータを渡すように画像生成パイプラインを変更しました。ファイルキャッシュフィルターからデバッグconsole.log文を削除しました。

### PR #742: lang suffix for audio file - @snakajima (https://github.com/receptron/mulmocast-cli/pull/742)
- **English**: Modified audio file path generation to include language suffix when context.lang is specified. Updated audioFilePath function in src/actions/audio.ts to append language suffix to filename using getAudioArtifactFilePath with the format `{fileName}_{lang}.mp3`. Added debug message output for audio artifact file path. This change ensures language-specific audio files are generated with appropriate naming for multilingual content generation.
- **日本語**: context.langが指定されている場合に言語サフィックスを含めるように音声ファイルパス生成を変更しました。src/actions/audio.tsのaudioFilePath関数を更新し、`{fileName}_{lang}.mp3`形式でgetAudioArtifactFilePathを使用してファイル名に言語サフィックスを追加するようにしました。音声アーティファクトファイルパスのデバッグメッセージ出力を追加しました。この変更により、多言語コンテンツ生成のために適切な命名で言語固有の音声ファイルが生成されることが保証されます。

### PR #744: use getAudioArtifactFilePath from both audio and movie - @snakajima (https://github.com/receptron/mulmocast-cli/pull/744)
- **English**: Refactored audio file path handling to centralize language suffix logic. Removed local audioFilePath function from src/actions/audio.ts and updated getAudioArtifactFilePath in src/utils/file.ts to accept MulmoStudioContext parameter and handle language suffix internally. Modified both audio and movie actions to use the centralized getAudioArtifactFilePath function instead of duplicating path generation logic. This ensures consistent audio file naming with language suffixes across all components.
- **日本語**: 言語サフィックスロジックを集約するために音声ファイルパス処理をリファクタリングしました。src/actions/audio.tsからローカルのaudioFilePath関数を削除し、src/utils/file.tsのgetAudioArtifactFilePathを更新してMulmoStudioContextパラメータを受け取り、言語サフィックスを内部で処理するようにしました。パス生成ロジックを重複させる代わりに、音声とムービーの両方のアクションを集約されたgetAudioArtifactFilePath関数を使用するように変更しました。これにより、すべてのコンポーネント間で言語サフィックスを含む一貫した音声ファイル命名が保証されます。

### PR #743: Language specific speakers - @snakajima (https://github.com/receptron/mulmocast-cli/pull/743)
- **English**: Implemented language-specific speaker support allowing different speaker configurations per language. Added `lang` field to speaker schema supporting language-specific speaker settings within individual speaker definitions. Modified getSpeaker method in MulmoPresentationStyleMethods to check for language-specific speaker data and return appropriate configuration based on context language. Added test_lang.json sample demonstrating multilingual speaker configuration. Updated schema validation to support the new language-specific speaker structure. This enables using different voice models or providers for different languages within the same presentation.
- **日本語**: 言語ごとに異なるスピーカー設定を可能にする言語固有スピーカーサポートを実装しました。個別のスピーカー定義内で言語固有のスピーカー設定をサポートするスピーカースキーマに`lang`フィールドを追加しました。MulmoPresentationStyleMethodsのgetSpeakerメソッドを変更して言語固有のスピーカーデータをチェックし、コンテキスト言語に基づいて適切な設定を返すようにしました。多言語スピーカー設定を実証するtest_lang.jsonサンプルを追加しました。新しい言語固有スピーカー構造をサポートするようにスキーマ検証を更新しました。これにより、同じプレゼンテーション内で異なる言語に対して異なる音声モデルやプロバイダーを使用できるようになります。

### PR #745: export test script - @isamu (https://github.com/receptron/mulmocast-cli/pull/745)
- **English**: Added `./scripts/test` directory to the files array in package.json to include test scripts in npm package distribution. This change makes test scripts available to users who install MulmoCast as a dependency, providing example scripts and test cases that can be referenced or used as starting points for their own implementations.
- **日本語**: package.jsonのfilesアレイに`./scripts/test`ディレクトリを追加し、npmパッケージ配布にテストスクリプトを含めるようにしました。この変更により、MulmoCastを依存関係としてインストールするユーザーがテストスクリプトを利用できるようになり、独自の実装の参考や出発点として使用できるサンプルスクリプトとテストケースが提供されます。

---

## Release Notes v1.1.5

### Developer Release Notes (English)

**Image Generation Enhancements**
- **Quality Parameter Support**: Added quality parameter for OpenAI image generation with "low", "medium", "high", and "auto" values. Updated OpenAI image agent to pass quality settings to API calls for supported models.

**Multilingual Content Support**
- **Language-Specific Audio Files**: Implemented language suffix for audio file naming, generating files with format `{fileName}_{lang}.mp3` for multilingual content workflows.
- **Language-Specific Speakers**: Added language-specific speaker support allowing different voice models or providers per language within individual speaker definitions. Updated speaker schema with `lang` field for multilingual voice configuration.
- **Centralized Audio Path Handling**: Refactored audio file path generation to centralize language suffix logic across audio and movie actions using unified getAudioArtifactFilePath function.

**Development and Distribution**
- **Test Script Export**: Added scripts/test directory to npm package distribution, making test scripts available to library users for reference and implementation examples.

**Code Quality**
- **Debug Cleanup**: Removed debug console.log statements from file cache filter for cleaner production output.

### Developer Release Notes (Japanese)

**画像生成機能強化**
- **クオリティパラメータサポート**: "low"、"medium"、"high"、"auto"値を持つOpenAI画像生成のクオリティパラメータを追加。サポートされているモデルでAPI呼び出しにクオリティ設定を渡すようにOpenAI画像エージェントを更新。

**多言語コンテンツサポート**
- **言語固有音声ファイル**: 多言語コンテンツワークフローのために`{fileName}_{lang}.mp3`形式でファイルを生成する音声ファイル命名の言語サフィックスを実装。
- **言語固有スピーカー**: 個別のスピーカー定義内で言語ごとに異なる音声モデルやプロバイダーを可能にする言語固有スピーカーサポートを追加。多言語音声設定のためのスピーカースキーマを`lang`フィールドで更新。
- **音声パス処理の集約**: 統一されたgetAudioArtifactFilePath関数を使用して音声とムービーアクション間で言語サフィックスロジックを集約する音声ファイルパス生成をリファクタリング。

**開発と配布**
- **テストスクリプトエクスポート**: npmパッケージ配布にscripts/testディレクトリを追加し、ライブラリユーザーが参考と実装例としてテストスクリプトを利用可能に。

**コード品質**
- **デバッグクリーンアップ**: よりクリーンな本番出力のためにファイルキャッシュフィルターからデバッグconsole.log文を削除。

### Creator Release Notes (English)

**Enhanced Image Quality Control**
- **Image Quality Settings**: Configure image generation quality using the new `quality` parameter in imageParams. Choose from "low", "medium", "high", or "auto" settings to balance generation speed and visual quality ([sample](https://github.com/receptron/mulmocast-cli/blob/1.1.5/scripts/test/test_images.json)).

**Advanced Multilingual Support**
- **Language-Specific Speakers**: Configure different voice models and providers for each language within the same presentation. Use the `lang` field in speaker definitions to specify language-specific voice settings ([sample](https://github.com/receptron/mulmocast-cli/blob/1.1.5/scripts/test/test_lang.json)).
- **Improved File Organization**: Audio files now automatically include language suffixes for better organization in multilingual projects.

**Better Development Resources**
- **Expanded Test Scripts**: Test scripts are now included in package distribution, providing more examples and reference implementations for various features.

### Creator Release Notes (Japanese)

**画像品質制御の強化**
- **画像品質設定**: imageParamsの新しい`quality`パラメータを使用して画像生成品質を設定できます。生成速度と視覚品質のバランスを取るために"low"、"medium"、"high"、"auto"設定から選択可能 ([サンプル](https://github.com/receptron/mulmocast-cli/blob/1.1.5/scripts/test/test_images.json))。

**高度な多言語サポート**
- **言語固有スピーカー**: 同じプレゼンテーション内で各言語に異なる音声モデルとプロバイダーを設定できます。スピーカー定義の`lang`フィールドを使用して言語固有の音声設定を指定 ([サンプル](https://github.com/receptron/mulmocast-cli/blob/1.1.5/scripts/test/test_lang.json))。
- **ファイル構成の改善**: 多言語プロジェクトでの整理しやすさのため、音声ファイルに自動的に言語サフィックスが含まれるようになりました。

**開発リソースの充実**
- **テストスクリプトの拡張**: テストスクリプトがパッケージ配布に含まれるようになり、様々な機能のより多くの例と参考実装を提供。

---

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [x] すべてのリンクがGitHubの完全URL（https://github.com/receptron/mulmocast-cli/blob/バージョン/パス）形式になっているか
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-30
チェック者: Claude Code