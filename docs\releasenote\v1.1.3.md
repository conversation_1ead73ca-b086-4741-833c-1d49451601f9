# リリースノート v1.1.3

## 今回のリリースに含まれる Pull Request

--- 以下、Generated by <PERSON> Code ---

## What's Changed

1. PR #738: Revert "Separate template func" - @isamu
2. PR #739: Revert "Script template from data" - @isamu

Full Changelog: https://github.com/receptron/mulmocast-cli/compare/1.1.2...1.1.3

## Pull Request Summaries

### PR #738: Revert "Separate template func" - @isamu (https://github.com/receptron/mulmocast-cli/pull/738)
- **English**: Reverted the template function separation that was introduced in PR #737. Moved template utility functions back from the dedicated src/utils/templates.ts module to their original locations in src/utils/file.ts and src/utils/prompt.ts. Updated all tools (create_mulmo_script_from_url, create_mulmo_script_interactively, dump_prompt, story_to_script) to import template functions from their original modules. Removed the export from src/index.common.ts and deleted the templates.ts file.
- **日本語**: PR #737で導入されたテンプレート関数の分離を取り消しました。専用のsrc/utils/templates.tsモジュールからテンプレートユーティリティ関数を元の場所であるsrc/utils/file.tsとsrc/utils/prompt.tsに戻しました。すべてのツール（create_mulmo_script_from_url、create_mulmo_script_interactively、dump_prompt、story_to_script）を元のモジュールからテンプレート関数をインポートするように更新しました。src/index.common.tsからエクスポートを削除し、templates.tsファイルを削除しました。

### PR #739: Revert "Script template from data" - @isamu (https://github.com/receptron/mulmocast-cli/pull/739)
- **English**: Reverted the data-driven template system that was introduced in PR #736. Restored file system-based template loading by modifying src/utils/file.ts to read templates from assets/templates/ directory. Updated all template files in assets/templates/ to revert schema versions and configurations. Restored src/data/promptTemplates.ts with expanded template data. Added back yarn run template step to GitHub Actions build workflow. Reverted changes to batch/template2tsobject.ts and package.json scripts.
- **日本語**: PR #736で導入されたデータ駆動テンプレートシステムを取り消しました。src/utils/file.tsを変更してassets/templates/ディレクトリからテンプレートを読み込むファイルシステムベースのテンプレート読み込みを復元しました。assets/templates/内のすべてのテンプレートファイルのスキーマバージョンと設定を元に戻しました。拡張されたテンプレートデータでsrc/data/promptTemplates.tsを復元しました。GitHub Actions buildワークフローにyarn run templateステップを再び追加しました。batch/template2tsobject.tsとpackage.jsonスクリプトの変更を取り消しました。

---

## Release Notes v1.1.3

### Developer Release Notes (English)

**Template System Changes**
- **Module Organization Rollback**: Reverted template function separation introduced in v1.1.2, returning template utilities to their original locations in src/utils/file.ts and src/utils/prompt.ts
- **Data-Driven Template System Rollback**: Reverted data-driven template system, restoring file system-based template loading from assets/templates/ directory
- **Build Process Restoration**: Restored yarn run template step to GitHub Actions build workflow for template data generation

**Code Structure**
- **Import Consolidation**: Updated all tools to import template functions from their original modules instead of dedicated template module
- **File System Restoration**: Templates now load directly from file system instead of bundled data approach
- **Template Data Restoration**: Restored expanded template data in src/data/promptTemplates.ts

**Configuration Changes**
- **Template Schema**: Template files reverted to previous schema versions and configurations
- **Build Scripts**: Restored batch/template2tsobject.ts and package.json script configurations

### Developer Release Notes (Japanese)

**テンプレートシステム変更**
- **モジュール構成のロールバック**: v1.1.2で導入されたテンプレート関数の分離を取り消し、テンプレートユーティリティを元の場所であるsrc/utils/file.tsとsrc/utils/prompt.tsに復元
- **データ駆動テンプレートシステムのロールバック**: データ駆動テンプレートシステムを取り消し、assets/templates/ディレクトリからのファイルシステムベースのテンプレート読み込みを復元
- **ビルドプロセスの復元**: テンプレートデータ生成のためにGitHub Actions buildワークフローにyarn run templateステップを復元

**コード構造**
- **インポートの統合**: すべてのツールを専用テンプレートモジュールの代わりに元のモジュールからテンプレート関数をインポートするように更新
- **ファイルシステムの復元**: バンドル済みデータアプローチの代わりにファイルシステムから直接テンプレートを読み込むように変更
- **テンプレートデータの復元**: src/data/promptTemplates.tsの拡張テンプレートデータを復元

**設定変更**
- **テンプレートスキーマ**: テンプレートファイルを以前のスキーマバージョンと設定に復元
- **ビルドスクリプト**: batch/template2tsobject.tsとpackage.jsonスクリプト設定を復元

### Creator Release Notes (English)

**Template System Restoration**
- **File-Based Templates**: Template system restored to use file-based loading, ensuring consistent template access and behavior
- **Configuration Stability**: Template configurations reverted to stable versions for reliable content creation

**System Stability**
- **Rollback for Stability**: Reverted recent template system changes to ensure system stability and consistent user experience
- **Build Process**: Restored standard build process for template generation

### Creator Release Notes (Japanese)

**テンプレートシステム復元**
- **ファイルベーステンプレート**: 一貫したテンプレートアクセスと動作を保証するため、テンプレートシステムをファイルベース読み込みを使用するように復元
- **設定の安定性**: 信頼性の高いコンテンツ作成のため、テンプレート設定を安定版に復元

**システム安定性**
- **安定性のためのロールバック**: システムの安定性と一貫したユーザー体験を保証するため、最近のテンプレートシステム変更を取り消し
- **ビルドプロセス**: テンプレート生成のための標準ビルドプロセスを復元

---

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [x] すべてのリンクがGitHubの完全URL（https://github.com/receptron/mulmocast-cli/blob/バージョン/パス）形式になっているか
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-30
チェック者: Claude Code