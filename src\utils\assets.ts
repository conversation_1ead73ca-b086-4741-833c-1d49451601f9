type BgmAsset = {
  name: string;
  title: string;
  url: string;
  suno_url: string;
  date: string;
  duration: string;
  account: string;
  original_license: string;
  prompt: string;
  model: string;
};

export type BgmAssets = {
  license: string;
  bgms: BgmAsset[];
};

export const bgmAssets: BgmAssets = {
  license: "Free to distribute as the BMG of media generated by MulmoCast, including commercial use.",
  bgms: [
    {
      name: "story001.mp3",
      title: "Whispered Melody",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/story001.mp3",
      suno_url: "https://suno.com/s/v6zer50aQJu8Y0qA",
      date: "2025-06-17",
      duration: "03:17",
      account: "@snakajima",
      original_license: "Generated by <PERSON><PERSON> with commercial use rights (PRO Plan)",
      prompt: "instrumental, smooth, piano",
      model: "v4.5 beta",
    },
    {
      name: "story002.mp3",
      title: "Rise and Shine",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/story002.mp3",
      suno_url: "https://suno.com/s/mJnvyu3UXnkdAPfQ",
      date: "2025-06-17",
      duration: "04:04",
      account: "@snakajima",
      original_license: "Generated by Suno with commercial use rights (PRO Plan)",
      prompt: "techno, inspiring, piano",
      model: "v4.5 beta",
    },
    {
      name: "story003.mp3",
      title: "Chasing the Sunset",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/story003.mp3",
      suno_url: "https://suno.com/s/2zGjMQ9vURJbaMZA",
      date: "2025-06-17",
      duration: "02:49",
      account: "@snakajima",
      original_license: "Generated by Suno with commercial use rights (PRO Plan)",
      prompt: "piano, inspiring, sunset",
      model: "v4.5 beta",
    },
    {
      name: "story004.mp3",
      title: "Whispering Keys",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/story004.mp3",
      suno_url: "https://suno.com/s/0SFoBRsBWsncw6tu",
      date: "2025-06-17",
      duration: "04:00",
      account: "@snakajima",
      original_license: "Generated by Suno with commercial use rights (PRO Plan)",
      prompt: "Piano, classical, ambient",
      model: "v4",
    },
    {
      name: "story005.mp3",
      title: "Whisper of Ivory",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/story005.mp3",
      suno_url: "https://suno.com/s/0SFoBRsBWsncw6tu",
      date: "2025-06-17",
      duration: "04:00",
      account: "@snakajima",
      original_license: "Generated by Suno with commercial use rights (PRO Plan)",
      prompt: "Piano solo, classical, ambient",
      model: "v4",
    },
    {
      name: "theme001.mp3",
      title: "Rise of the Flame",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/theme001.mp3",
      suno_url: "https://suno.com/s/WhYOf8oJYhBgSKET",
      date: "2025-06-20",
      duration: "03:23",
      account: "@snakajima",
      original_license: "Generated by Suno with commercial use rights (PRO Plan)",
      prompt: "Olympic Theme, classical, emotional",
      model: "v4",
    },
    {
      name: "olympic001.mp3",
      title: "Olympic-style Theme Music",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/olympic001.mp3",
      suno_url: "https://suno.com/s/32wpnmCrkFVvkTSQ",
      date: "2025-07-17",
      duration: "02:54",
      account: "@snakajima",
      original_license: "Generated by Suno with commercial use rights (PRO Plan)",
      prompt:
        "Epic orchestral fanfare in the style of John Williams' Olympic Fanfare and Theme. Bright brass fanfare, soaring strings, powerful percussion, and heroic French horn melodies. Triumphant and majestic mood, suitable for an opening ceremony or national celebration. Emphasize dynamic builds, rich harmonies, and cinematic grandeur.",
      model: "v4.5+",
    },
    {
      name: "morning001.mp3",
      title: "Morning Dance",
      url: "https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/morning001.mp3",
      suno_url: "https://suno.com/s/9MTkutZYqxeyBlwK",
      date: "2025-07-17",
      duration: "03:52",
      account: "@snakajima",
      original_license: "morning, piano solo, Japanese name, sexy",
      prompt: "morning, piano solo, Japanese name, sexy",
      model: "v4.5+",
    },
  ],
};
