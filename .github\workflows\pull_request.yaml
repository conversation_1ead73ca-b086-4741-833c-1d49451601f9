# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

permissions:
  contents: read
  actions: write

jobs:
  lint_test:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [22.x, 24.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'yarn'
    - run: sudo apt-get update
    - run: sudo apt-get install chromium-browser
    - run: sudo apt-get install -y ca-certificates fonts-liberation libatk-bridge2.0-0 libatk1.0-0 libcups2 libdrm2 libgbm1 libnspr4 libnss3 libxcomposite1 libxdamage1 libxrandr2 xdg-utils wget ffmpeg
    - run: yarn install
    - run: yarn run lint
    - run: yarn run build
    - run: yarn run ci_test
    - run: yarn run cli images scripts/test/test_order.json
    - run: yarn run cli images scripts/test/test_beats.json
    - run: yarn run cli movie scripts/test/test_no_audio.json
    - run: yarn run cli movie scripts/test/test_no_audio_with_credit.json
    - run: yarn run cli movie scripts/test/test_transition_no_audio.json
    - run: yarn run cli movie scripts/test/test_slideout_left_no_audio.json
    - run: yarn run cli pdf scripts/test/test_order.json --pdf_mode slide --pdf_size a4
    - run: yarn run cli pdf scripts/test/test_order.json --pdf_mode talk --pdf_size a4
    - run: yarn run cli pdf scripts/test/test_order.json --pdf_mode handout --pdf_size a4
    - run: yarn run cli pdf scripts/test/test_order_portrait.json --pdf_mode slide --pdf_size a4
    - run: yarn run cli pdf scripts/test/test_order_portrait.json --pdf_mode talk --pdf_size a4
    - run: yarn run cli pdf scripts/test/test_order_portrait.json --pdf_mode handout --pdf_size a4
    - name: Upload generated media
      uses: actions/upload-artifact@v4
      with:
        name: generatedmedia-${{ matrix.node-version }}
        path: output/
