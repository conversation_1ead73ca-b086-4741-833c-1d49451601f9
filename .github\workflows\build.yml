name: Update Build

on:
  push:
    branches:
      - main

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'receptron'
    strategy:
      matrix:
        node-version: [22.x]
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'yarn'
    - run: yarn install
    - run: yarn run template
    - run: |
        git config user.name "GitHub Actions Bot"
        git config user.email "<>"
    - name: Commit 
      run: |
        if ! git diff --cached --quiet; then
          git commit -m "format or generated file" .
          git push origin main
        else
          echo "No change format"
        fi
