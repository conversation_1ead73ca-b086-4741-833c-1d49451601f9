# プロンプト
0.0.18 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.18

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
他のバージョンのリリースノートを同様に簡潔に書いてください。

## 今回のリリースに含まれる Pull Request
## What's Changed
* add release notes by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/522
* methods by @isamu in https://github.com/receptron/mulmocast-cli/pull/521
* fix multiLingual broken by @isamu in https://github.com/receptron/mulmocast-cli/pull/523


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.17...0.0.18

## Pull Request Summaries

### PR #522: Add release notes
- **English**: Added comprehensive release notes documentation structure including index.md for GitHub releases, detailed version files (v0.0.xx.md) generated by Claude, and a prompt template for creating release notes.
- **日本語**: リリースノートのドキュメント構造を追加。GitHub向けのindex.md、Claudeで生成する詳細バージョンファイル（v0.0.xx.md）、リリースノート作成用のプロンプトテンプレートを含む。

### PR #521: Methods refactoring
- **English**: Refactored method imports and implementations in captions.ts and movie.ts. Consolidated method calls through MulmoStudioContextMethods for better encapsulation, added new getter methods (getFileName, getCaption), and improved code organization.
- **日本語**: captions.tsとmovie.tsのメソッドインポートと実装をリファクタリング。MulmoStudioContextMethodsを通じたメソッド呼び出しの統合によりカプセル化を改善し、新しいゲッターメソッド（getFileName、getCaption）を追加、コード構成を改善。

### PR #523: Fix multiLingual broken
- **English**: Fixed a critical bug in multilingual support by correcting the initialization order. The multiLingual object is now created after the studio object to ensure it uses the correct beat count. Also updated GraphAI dependency from 2.0.6 to 2.0.8.
- **日本語**: 初期化順序を修正することで多言語サポートの重大なバグを修正。studioオブジェクトの後にmultiLingualオブジェクトを作成することで、正しいビート数を使用するように修正。GraphAIの依存関係も2.0.6から2.0.8に更新。

## Release Notes

### Developer Release Notes (English)

**MulmoCast CLI v0.0.18** brings important bug fixes and code improvements:

**🐛 Bug Fixes**
- Fixed a critical multilingual support bug where the beat count was incorrectly calculated due to initialization order issues
- The multiLingual object now properly uses the studio's beat count

**🔧 Code Improvements**
- Refactored method implementations for better encapsulation in captions.ts and movie.ts
- Added new getter methods to MulmoStudioContextMethods: `getFileName()` and `getCaption()`
- Consolidated direct property access through proper method calls

**📚 Documentation**
- Added comprehensive release notes structure with templates for future releases
- Introduced Claude-powered release note generation workflow

**📦 Dependencies**
- Updated GraphAI from 2.0.6 to 2.0.8

### Developer Release Notes (日本語)

**MulmoCast CLI v0.0.18** は重要なバグ修正とコード改善をもたらします：

**🐛 バグ修正**
- 初期化順序の問題によりビート数が正しく計算されない多言語サポートの重大なバグを修正
- multiLingualオブジェクトがstudioのビート数を適切に使用するようになりました

**🔧 コード改善**
- captions.tsとmovie.tsのメソッド実装をリファクタリングし、カプセル化を改善
- MulmoStudioContextMethodsに新しいゲッターメソッドを追加: `getFileName()`と`getCaption()`
- 直接的なプロパティアクセスを適切なメソッド呼び出しに統合

**📚 ドキュメント**
- 将来のリリースのためのテンプレートを含む包括的なリリースノート構造を追加
- Claudeを活用したリリースノート生成ワークフローを導入

**📦 依存関係**
- GraphAIを2.0.6から2.0.8に更新

### Creator Release Notes (English)

**MulmoCast CLI v0.0.18** - Stability Update

This release focuses on improving the reliability of MulmoCast:

**✨ What's Fixed**
- **Multilingual presentations now work correctly** - Fixed an issue where presentations with multiple languages could have incorrect timing
- Overall stability improvements through internal code optimization

**📝 Behind the Scenes**
- We've improved our release documentation process to bring you clearer updates in future releases

### Creator Release Notes (日本語)

**MulmoCast CLI v0.0.18** - 安定性向上アップデート

このリリースはMulmoCastの信頼性向上に焦点を当てています：

**✨ 修正内容**
- **多言語プレゼンテーションが正しく動作するようになりました** - 複数言語のプレゼンテーションでタイミングが不正確になる問題を修正
- 内部コードの最適化による全体的な安定性の向上

**📝 開発の裏側**
- 今後のリリースでより分かりやすいアップデート情報をお届けするため、リリースドキュメントのプロセスを改善しました