# v0.0.15 Release Notes

**MulmoCast CLI v0.0.15** adds exciting features and improvements to enhance your creative workflows with rich visuals and streamlined production.

## Beat Images & Visual Content
- Images in Beats: Attach or auto-generate images directly within each beat (scene), enriching your videos with engaging visuals.
- Multiple Image Types Supported: Easily use URLs, local images, slides, markdown content, charts (Chart.js), diagrams (Mermaid), and HTML-based visuals.
- Intelligent Image Generation: Automatically creates visuals based on provided prompts or beat text.

Explore these features in detail in the updated documentation ([docs/image.md](https://github.com/receptron/mulmocast-cli/blob/0.0.15/docs/image.md)).  

Sample: [test_beats.json](https://github.com/receptron/mulmocast-cli/blob/0.0.15/scripts/test/test_beats.json)

## Smooth Video Transitions
- Automatically add smooth transitions between scenes, making your video flow naturally without manual editing.

Sample: [test_transition.json](https://github.com/receptron/mulmocast-cli/blob/0.0.15/scripts/test/test_transition.json)

## PDF Output Improvements
- Standardized PDF output on Puppeteer for selectable text and flexible layouts
- Enhanced PDF file path handling for better organization
- Removed the pdf-engine option in favor of the superior Puppeteer implementation

## Dry Run Mode (Quick Test)
- Quickly test your project scripts without generating large media files—perfect for fast iterations and debugging.

## Custom Output Locations
- Specify custom folders and filenames for generated media, keeping your projects organized exactly how you prefer.

## Improved Error Messages
- Clearer, helpful error messages, especially for audio generation—no more guessing when something goes wrong.

## Release Readiness
- Added a release test to verify functionality before publishing a new version. Details: [CONTRIBUTING.md#L144](https://github.com/receptron/mulmocast-cli/blob/0.0.15/CONTRIBUTING.md#L144)

## Other Improvements
- Documentation improvements for ElevenLabs setup. Details: [README.md#L111-L112](https://github.com/receptron/mulmocast-cli/blob/0.0.15/README.md#L111-L112)
- Enhanced stability with additional automated tests
- Minor internal optimizations and typo fixes for smoother operations

Enjoy creating more compelling content effortlessly!

---

## Developer-Oriented Release Notes (English)

Mulmocast CLI 0.0.15 comes with numerous improvements, refactoring, and new capabilities that developers and contributors should note:

### PDF Engine Option Added and Removed
An experimental feature to select different PDF generation engines was merged and later removed in the same release cycle. The code introduced to allow alternate PDF rendering (with an external library pdflib) was reverted, simplifying PDF output back to a single method. Developers should be aware that any interim pdfEngine configurations will no longer exist in the final 0.0.15 – the CLI continues using the default PDF generator (and pdflib has been removed from dependencies). This cleanup reduces complexity and potential maintenance issues.

### Documentation Updates
The README and documentation have been updated. In particular, a new docs/image.md details how to use the new image embedding and generation features in beats, including examples of different content types (charts, slides, mermaid diagrams, etc.). Additionally, the README now includes configuration instructions for ElevenLabs TTS integration. Developers integrating Mulmocast CLI or writing plugins should consult these docs for guidance on new features.

### Video Transitions
The video generation pipeline now supports transition effects between beats (slides/scenes). If you are working on or customizing the video rendering, note that transitions will automatically apply even when beats contain videos. A new test (test_transition.json) was added to ensure this functionality works, and an edge case was fixed where a beat with video but no text now gets a correct default duration. This enhances the polish of generated videos with no extra effort from developers.

### Output File Path Customization
The CLI now has an option or internal API to specify output file paths for generated media (such as PDFs). This means you can direct output to a custom directory or filename instead of the fixed default. If you maintain scripts or integrations that consume Mulmocast outputs, you can now programmatically set the output location via the provided interface (e.g., using outDirPath in the context). This change involved adding a helper function (pdfFilePath) to consistently generate output paths.

### Improved Error Messaging
Error handling for the OpenAI TTS agent (tts_openai_agent) has been improved. If something goes wrong (like missing API keys or service failures), the CLI will now emit clearer error messages. This makes debugging easier – instead of a generic or cryptic error, you'll see a message guiding you to the likely cause (e.g., "OpenAI TTS error: API key not set"). This improvement will help when integrating new TTS providers or troubleshooting audio generation.

### Refactoring and Internal Enhancements
The codebase saw several refactors for clarity and reliability. Notably, an obsolete onComplete node was removed from the processing graph, simplifying the sequence of operations when a project run finishes. The "document script" (responsible for generating documents/PDFs or similar) was also updated to accommodate new features (likely handling images in PDFs) and to fix minor formatting issues (including a small Markdown/HTML typo fix). These changes should not break functionality but make the codebase cleaner and output more consistent.

### Dependency Updates
Mulmocast CLI upgraded some underlying components. The GraphAI library was updated to v2.0.6, pulling in fixes and improvements for any graph-based or AI-driven content generation. If you rely on GraphAI features, expect more stable or enriched behavior. Additionally, removing pdflib (as mentioned) and adding any new required libraries for image generation (e.g., Chart.js, Mermaid, etc., if not already present) were part of this release. Check the updated package.json or dependency list for any changes that might affect your development environment.

### Testing and CI
A strong focus on testing was evident in this release. New tests were added for image preprocessing, beat handling (with a beats.json scenario), video/movie generation (movieTest), transitions, and the new image reference features. There's also a release_test to verify release builds. For developers, this means the project has better regression coverage. When contributing, you should run the test suite (which is now more comprehensive) to catch issues early. The addition of a fake data generator is a boon for testing – it allows you to easily produce dummy content (text, images, charts) to simulate various scenarios without needing real data, making local testing and unit tests easier to write and maintain.

### Dry Run Mode & Mock Agents
A new dry-run mode was introduced, along with a MediaMockAgent. From a developer perspective, this is a valuable feature: you can run the CLI in a simulation mode where external calls (video rendering, TTS, image generation) are replaced with mock outputs. This speeds up development cycles because you can test the flow of a project (ensuring that each step executes) without waiting for lengthy video/audio generation or needing API keys for external services. Technically, a dryRun flag triggers the use of a special "dryrun" provider for media; the MediaMockAgent ensures that whether it's image, audio, or video, a placeholder is returned instantly. When developing or debugging, you can enable dry-run to quickly pinpoint logic issues. Remember to disable it for actual production outputs. This modularization (using a fake provider) is cleaner than scattershot conditional flags and is easily extensible to new media types in the future.

In summary, version 0.0.15 is a developer-friendly update: it streamlines the code (removing unused components), adds robust testing and debugging tools (clearer errors, dry-run, fake data), and updates documentation to help you leverage new features. We recommend reviewing the new docs (especially docs/image.md and README changes) and running tests to familiarize yourself with the improvements. These changes set a solid foundation for future development and make it easier to integrate Mulmocast CLI into your projects or contribute to its growth.

## 開発者向けリリースノート（日本語）

Mulmocast CLI 0.0.15 では、開発者・コントリビューターにとって重要ないくつもの改善、リファクタリング、新機能が導入されています:

### PDFエンジン選択機能の追加と削除
異なるPDF生成エンジンを選択できる試験的機能が一度導入されましたが、同じリリース内で削除されました。代替のPDFレンダリングを可能にするコード（外部ライブラリpdflibを用いた実装）が追加されましたが、その後、PDFエンジン選択オプションは撤回され、PDF出力方法は従来どおり単一の手法に戻されています。これにより、一時的に存在したpdfEngine設定項目は最終的な0.0.15には残らない点に注意してください（pdflibも依存関係から除去されました）。この変更はコードの複雑さを軽減し、保守性を向上させています。

### ドキュメントの更新
READMEおよび関連ドキュメントが更新されました。特に、新たに追加された docs/image.md には、ビートでの画像埋め込み・生成機能の使い方やルールが詳しく記されています（image・imagePrompt・moviePromptの優先順位や、各種image.typeのJSON例など、画像/ビジュアル機能の活用方法を網羅）。また、READMEには ElevenLabs のTTS（音声合成）の設定手順が追記され、Mulmocast CLIでElevenLabsを統合する方法が明確に示されています。新機能を利用する際やプラグイン開発を行う際は、これら最新のドキュメントを参照して理解を深めてください。

### ビデオトランジション
ビデオ生成パイプラインにおいて、ビート間のトランジション効果がサポートされました。ビート（スライドやシーン）間で自動的に切り替え効果が挿入されるようになり、特に各ビートに動画クリップが含まれる場合でもトランジションが機能します。これに関連して、トランジション機能を検証するテスト（test_transition.json）が追加されました。また、テキスト無しで動画だけを含むビートのデュレーション（再生時間）が正しく設定されない不具合が修正され、テキストが空のビートでも適切なデュレーションが付与されるようになっています。特にレンダリング処理を拡張・カスタマイズしている開発者は、この変更によって追加の実装なしに動画の仕上がりが向上することに留意してください。

### 出力ファイルパスのカスタマイズ
CLIに生成ファイルの出力先を指定するオプション/機能が追加されました。これにより、生成されたメディア（例: PDF）をデフォルトの場所ではなく、任意のディレクトリやファイル名で保存できるようになっています。内部的には、出力パスを統一的に生成するヘルパー関数（pdfFilePathなど）が追加され、contextの中でoutDirPathを指定すれば、それに基づいて出力ファイル名が決定されます。Mulmocastの出力を他のシステムに渡すスクリプトを書いている場合、この新機能を使って出力先を制御できるようになります。

### エラーメッセージの改善
OpenAIのTTSエージェント（tts_openai_agent）におけるエラーメッセージが改善されました。APIキーの未設定やサービス側の問題などでエラーが発生した際、これまでは曖昧なメッセージしか得られない場合がありましたが、今後は原因を特定しやすい明確なメッセージが表示されます。例として、「OpenAI TTS error: API key not set（APIキーが設定されていません）」のように具体的な指摘がなされるため、開発者にとってトラブルシューティングが容易になります。新たなTTSプロバイダー統合時や音声生成の不具合調査の際には、この改善が役立つでしょう。

### リファクタリングと内部的な強化
コードベースのいくつかの部分が整理され、可読性と信頼性が向上しています。特筆すべきは、処理フロー終端の**onCompleteノードが削除された点です。タスク完了時の冗長なノードを排除することで、処理シーケンスが簡素化されました。また、「document script」（おそらくPDF等のドキュメント生成を担うスクリプト）の更新も行われており、新機能に対応できるよう改善されています（ビジュアル要素をPDFに組み込む処理やフォーマットの微修正などが含まれます）。さらに、Markdown内のHTML表記ミスに対処するタイポ修正**（1行のみの変更）も実施されており、ドキュメントやテンプレートの正確性が向上しました。これらの変更はいずれも機能そのものへの大きな影響はありませんが、コードが洗練され、出力結果の体裁がより整う効果があります。

### 依存関係の更新
Mulmocast CLIはいくつかの内部コンポーネントをアップデートしています。GraphAI ライブラリがバージョン2.0.6に更新され、グラフ生成やAI関連の処理における不具合修正や機能強化が取り込まれました。GraphAIを用いた出力（グラフビジュアライゼーション等）を利用する場合、本更新により安定性や機能性の向上が期待できます。また前述の通り、pdflibの除去や、新しい画像生成機能のために必要なライブラリ（Chart.jsやMermaid等）の追加が行われています。開発環境で依存パッケージを再度インストールし、package.jsonの変更点を確認しておくことをお勧めします。

### テストとCIの強化
本リリースではテストカバレッジが大幅に強化されました。画像前処理、ビート処理（beats.jsonを用いたケース）、動画生成（movieTest）、トランジション、そして新たに導入された画像参照機能に関するテストが追加されています。また、リリースビルドを検証する**release_test**も導入されました。プロジェクトに貢献する開発者にとって、テストスイートがより包括的になったことは重要です。変更を加えた際には、これらのテストを走らせることで不具合を早期に発見できるでしょう。さらに、フェイクデータジェネレーターの追加はテスト・デバッグ作業を容易にします。これはダミーデータ（テキスト・画像・チャート用のデータなど）を簡単に生成できるユーティリティで、本物の入力データが無くても種々のシナリオをシミュレートできるため、ユニットテストや動作検証用シナリオの作成が格段に楽になります。

### ドライランモード & モックエージェント
新たにドライラン（Dry Run）モードが追加され、合わせてMediaMockAgent（メディアモックエージェント）が導入されました。開発者にとって非常に有用なこれらの機能を使うと、Mulmocast CLIの生成プロセスをシミュレーションモードで実行でき、ビデオレンダリングやTTSリクエストといった重い処理や外部サービスへのアクセスを行いません。dryRunフラグを有効にすると、映像・音声・画像の各生成処理が特殊なモックエージェントに置き換わり、即座に擬似アウトプットを返すようになります。本PR開発中には、フラグによる条件分岐実装から、専用の「dryrun」プロバイダを用意する実装へと改善されました。これにより、あらゆるメディアタイプ（画像・音声・動画）について、ドライラン実行時には実ファイルの生成を完全にスキップできます。複数のメディアにまたがるプロジェクトでも、ドライランで一貫してフロー全体を高速にテストできるため、開発中のデバッグやシナリオ検証が飛躍的に効率化されます。実運用時には通常モードに戻すことを忘れないようにしてくださいが、このモードは将来的な新メディアタイプに対しても容易に拡張可能であり、開発・CI環境でのツールとして非常に価値があります。

総じて、バージョン0.0.15は開発者に優しいアップデートと言えます。未使用機能の整理（PDFエンジン選択の撤回等）によるコードの単純化、エラーメッセージの明確化やドライラン/フェイクデータ生成といった強力なデバッグ支援機能の追加、そして包括的なテストの充実により、Mulmocast CLIの開発・保守が以前よりスムーズになっています。最新のドキュメント（特にdocs/image.mdや更新されたREADME）に目を通し、新機能や変更点を把握してください。これらの改善は、Mulmocast CLIをプロジェクトに組み込む開発者やOSSコントリビューターにとって、今後の作業をより効率的かつ安心なものにしてくれるでしょう。

## Creator-Oriented Release Notes (English)

Mulmocast CLI 0.0.15 brings exciting new features and improvements for creators using the tool to produce videos, podcasts, and other media content. Here's what's new and changed from a user (creator) perspective:

### Images and Visual Content in Your Projects
You can now include images and other visual elements in your Mulmocast projects! This is a major enhancement – each "beat" (which you can think of as a scene or segment in your script) can have an image associated with it. You can provide an image yourself (by URL or local file path), or let Mulmocast generate one from a prompt. For example, if you have a beat about a topic, you can set an imagePrompt and the CLI might create an illustrative image for you. Mulmocast also supports creating text-based slides (with titles and bullet points), rendering Markdown content into an image, generating charts from data (using Chart.js), and even making simple diagrams (via Mermaid syntax) or styled text images (via HTML/Tailwind). The CLI will decide what visual to produce based on what you specify:

- If you give it an explicit image (like a URL or path to an image), it will use that image.
- If you don't have an image file but provide an imagePrompt (a description), it will try to generate a relevant image from that description.
- If you only provide a moviePrompt (for animated/video backgrounds) and no image, it will focus on the video generation without a static image.
- If you provide none of those, Mulmocast can smartly take your beat's text and come up with an image idea on its own, generating an image to match your narration.
- Additionally, if you have both an image (from either of the above methods) and a video prompt, Mulmocast can combine them – for instance, using your generated image as part of the video scene along with motion or effects guided by the moviePrompt.

What this means for you: Your videos can now be enriched with visuals automatically. You could get slides summarizing key points, graphs illustrating data, or background images that reflect your story, without manual editing. This feature can save you time and give your content a professional touch with relevant imagery. Be sure to check the documentation (docs/image.md) for examples on how to use the new image property in your project JSON – it's full of neat tricks for creators!

### Smoother Video Transitions
Your videos will look more polished with the new transition effects between beats. Mulmocast CLI now automatically adds a transition when moving from one beat to the next. If you have images or video clips in your beats, the transition will blend them (for example, a fade or similar effect) so the cut isn't jarring. You don't need to do anything special – it's enabled by default. Also, a bug was fixed so that if you have a beat with a video and no text caption, it won't flash by too quickly; the duration for such a beat is set properly. Overall, expect more seamless and professional-looking scene changes in the videos you generate.

### Output File Location Option
You now have more control over where your output files are saved. Previously, Mulmocast would save generated videos, audio, and PDFs to a default directory with predetermined names. In 0.0.15, you (or the person who set up your project JSON) can specify an output path or filename. For example, you might direct the CLI to save the final video in a particular folder or with a specific name. This is great for organizing your projects – especially if you generate multiple versions or have a preferred folder structure. If you're using the CLI directly, look for a new option to set the output directory; if you're using a JSON config, the outDirPath in the context can be configured. No more hunting around for where the file went – you decide the destination.

### ElevenLabs TTS Integration (Documented)
If you want to use ElevenLabs for text-to-speech (voice synthesis), it's now easier. The CLI has had support for multiple TTS providers, and in this release the README was updated with clear instructions on how to configure ElevenLabs API keys and settings. ElevenLabs is known for high-quality, realistic voices. By following the guide in the README, you can plug in your ElevenLabs credentials and have Mulmocast use those voices for narration. This addition doesn't change any existing default, so if you don't use it, nothing changes – but if you've been looking to enhance the voice quality of your projects, give ElevenLabs a try with the new docs as your starting point.

### Better Error Messages
We've improved some of the messages Mulmocast gives you, particularly around voice generation. If something goes wrong with the OpenAI TTS agent (which some projects use for voice), the error message will now clearly tell you what happened. For instance, if you forgot to set your OpenAI API key, it will say so, instead of giving a vague error. This means less frustration and quicker fixes – you'll know if an error is due to a missing key, a network issue, or something else, and you can address it without guesswork.

### Dry Run Mode for Quick Testing
This is a more advanced feature, but useful for creators who like to tweak and test: a "dry run" mode. Dry run allows you to simulate running your project without actually generating the full video or audio. Think of it as a rehearsal. Mulmocast will go through the motions: it'll process your script, beats, prompts, etc., and even pretend to create media by using mock images and audio, but it won't call heavy external services or produce large files. You'll get a quick confirmation that all your settings and content are okay. This is super handy if you have a long project – you can run a dry run in a fraction of the time to catch any errors (like a typo in a prompt or a missing asset) before committing to the full generation. When you're happy, you run it normally to get your actual video or podcast. To use dry run, you'll likely use a command-line flag (check the usage docs) – when enabled, Mulmocast might output text logs indicating what it would have done, without saving big files. It's like a checklist run-through for your project.

### Stability and Quality Improvements
Aside from new features, several under-the-hood improvements mean a smoother experience for you. The application has more tests now – while you might not see this directly, it translates to fewer bugs and regressions. A small example: an internal cleanup removed something called an "onComplete node" – this doesn't require action from you, but it streamlines the rendering process, which is part of ongoing efforts to make Mulmocast more efficient. Also, if you ever use advanced content like Mermaid diagrams or HTML blocks, a minor typo in the template was fixed, so those should render correctly without broken formatting. Finally, an update to the GraphAI component means that any AI-driven graph visuals or logic might work better now, though this is a low-level detail.

In summary, v0.0.15 empowers creators to do more with Mulmocast CLI. The headline is visual content: you can automatically enrich your videos with images, slides, and charts relevant to your narrative. Videos will have nicer transitions, and you have greater control over output organization and voice quality (with ElevenLabs support). The new dry-run capability can speed up your edit-preview cycles. And thanks to behind-the-scenes fixes, you should encounter fewer hiccups. We encourage you to experiment with adding an imagePrompt to your beats, watch the transitions flow, and enjoy the new creative possibilities this release offers!

## クリエイター向けリリースノート（日本語）

Mulmocast CLI 0.0.15 は、動画や音声コンテンツを制作するクリエイターの皆さんにとって魅力的な新機能と改善をもたらします。ユーザー視点での主な変更点は以下のとおりです。

### プロジェクトへの画像・ビジュアル素材の組み込み
Mulmocastプロジェクト内に画像を含めることが可能になりました！これは大きな強化ポイントで、脚本の各「ビート」（シーンやセクション）に画像などの視覚要素を関連付けることができます。画像はユーザー自身で用意（URL指定やローカルファイル参照）することもできますし、キーワード説明（プロンプト）からMulmocastに自動生成させることもできます。例えば、あるビートで特定のトピックを語っているとき、その内容に合った画像プロンプトを設定すれば、Mulmocastがそれに沿ったイメージ画像を生成してくれます。またMulmocastは、テキストスライド（タイトルと箇条書きリストを画像化したスライド）を作成したり、Markdownテキストを画像にレンダリングしたり、Chart.jsを使ってグラフを描いたり、Mermaid記法でダイアグラムを作成したり、HTML+Tailwindでデザインしたブロックを画像化したりすることもできます。どう使うかはビート内の指定次第で、以下のようなルールで動作します：

- 明示的にimageプロパティで画像（URLまたはファイルパス）を指定すれば、その画像がそのまま使われます。
- 画像ファイルの指定がなくてもimagePrompt（画像用の説明文）が設定されていれば、そのプロンプト内容からMulmocastが画像を生成します。
- moviePrompt（動画アニメーション用のプロンプト）のみを指定し、imageやimagePromptがない場合、静止画は作らずにそのプロンプトだけで動画（映像効果）の生成を行います。
- imageもimagePromptもmoviePromptも何も設定していない場合は、Mulmocastがビートのテキスト内容から自動的にイメージ用のプロンプトを作り出し、それを使って関連しそうな画像を生成してくれます（完全にお任せで視覚素材を提案・作成してくれるイメージです）。
- さらに、上記のようにして画像が用意できた場合で、なおかつ同じビートにmoviePrompt（映像効果の指示）が存在する場合、その画像＋プロンプトを組み合わせて映像を作成します。例えば、自動生成した画像を背景に、moviePromptで指定した動きやエフェクトを加えたクリップを作る、といった動作になります。

この機能で何ができるか: これからはMulmocastが自動的にあなたの動画を豊かなビジュアルで彩ってくれます。ナレーションやスクリプトに合わせて、関連する画像やグラフィックを付け足すことができ、編集の手間を減らしつつ、よりプロフェッショナルな仕上がりに近づけることができます。例えば解説動画ではポイントをまとめたスライドやグラフを自動生成させたり、物語コンテンツではシーンに合った雰囲気の背景画像を自動で付けたりといったことが可能です。使い方の詳細やプロジェクトJSONでの指定方法については、新しく追加されたドキュメント（docs/image.md）に豊富なサンプルが載っていますので、ぜひ参照してみてください。画像生成に関するたくさんの工夫が詰まっていて、クリエイティブの幅が一気に広がるはずです。

### 滑らかなビデオトランジション
動画におけるシーン切り替えがよりスムーズになりました。ビート間のトランジション効果が新たに自動適用されるようになったため、シーンとシーンの繋ぎ目でフェード等のエフェクトが入り、急なカット割りによる違和感が軽減されます。特に、ビートに画像や動画クリップを含めている場合でも、自動的に適切なトランジションが挿入されますので、何も追加設定しなくても映像全体のつながりが自然になります。また、細かな修正点として、テキストのない動画ビートの再生時間が短すぎた問題が解消されました（テキストが無い場合でも一定の表示時間が確保されます）。これらにより、生成される動画はこれまで以上に見栄えが良くなり、編集作業なしでプロっぽい演出が加わります。

### 出力ファイルの保存場所を指定可能に
生成したファイル（動画・音声・PDF）の出力先フォルダやファイル名を指定できるオプションが追加されました。従来はMulmocast CLIが決まった場所・名前で出力していましたが、バージョン0.0.15ではユーザー側である程度コントロールできます。例えば、「出力をプロジェクト用の特定のフォルダにまとめたい」「ファイル名にバージョンや日付を入れたい」といった場合に対応しやすくなります。CLIを直接利用する場合は、新しいコマンドライン引数や設定項目で出力ディレクトリを指定できます。JSONでコンフィグを組んでいる場合も、コンテキストのoutDirPathなどを設定することで反映されます。これにより、生成物がどこに行ったか探し回る必要が減り、プロジェクト毎・バージョン毎に整理されたファイル管理がしやすくなるでしょう。

### ElevenLabs の音声合成に対応（ドキュメント追加）
高品質な音声合成サービスElevenLabsをMulmocast CLIで利用しやすくなりました。以前から複数のTTSプロバイダーに対応していましたが、本リリースでREADMEにElevenLabsの設定方法が追記され、APIキーの設定や使い方が明確に案内されています。ElevenLabsは非常に自然な音声で定評がありますので、ナレーションのクオリティを上げたい場合に有力な選択肢です。READMEのガイドに従ってAPIキー等を設定すれば、MulmocastがElevenLabsの音声を使って読み上げを行います。デフォルトではこれまで通りOpenAIベースの音声等が使われますので、特に使う予定がなければ何も変わりませんが、興味のある方はぜひこの新しい統合を試してみてください。簡単な設定で声のバリエーションが広がります。

### エラーメッセージの分かりやすさ向上
特に音声合成周りで、これまでより親切なエラーメッセージが出るようになりました。例えばOpenAIのTTSを使っている際にエラーが起きた場合、その原因がはっきり表示されます。APIキーを入れ忘れていたら「APIキーが設定されていません」という風に知らせてくれるので、「何か動かないけど理由が分からない…」というストレスが減ります。設定ミスやネットワークエラーなど、ユーザー側で対処すべき問題が明示されますので、問題発生時にはメッセージをよく確認してみてください。すぐに解決策に辿り着けるはずです。

### ドライランモード（試行実行機能）の追加
こちらは少し上級者向けの機能ですが、「ドライラン」モードが使えるようになりました。ドライランとは、本番の重たい生成処理を行わずに、一通り処理をシミュレーションしてみるモードです。いわばプレビュー検証用のリハーサル機能で、映像や音声の実ファイルを出力しない代わりに、各ステップがちゃんと動くか高速にチェックできます。長尺のプロジェクトや複雑なスクリプトを作っているときに、いきなりフル生成をすると時間がかかりますが、ドライランならそのごく一部の時間で一通り流れを確認できます。例えば、「テキストに誤字があって音声生成で失敗しないか」「指定した画像のURLが間違っていないか」といった基本的なチェックを、実際の動画を吐き出す前に済ませられます。使い方はCLIのオプションでドライランを有効にするだけです（具体的な手順はドキュメントや--helpで確認できます）。ドライラン中は、Mulmocastは画像や音声をダミー出力で代用しつつログを出して進みます。「ここで本当は画像生成」「ここで映像レンダリング」などと教えてくれるイメージです。本番前の最終チェックや試行錯誤の迅速化に、この機能をぜひ活用してみてください。

### 安定性と品質の向上
新機能以外にも、見えない部分でMulmocastはより堅実になっています。テスト（動作検証）が増えたことで、今後バグが発生しにくくなり、リリース前に多くの問題が潰されるようになりました（つまり、皆さんが遭遇する不具合が減ることにつながります）。例えば内部処理の簡素化として「onCompleteノードの削除」という変更がありましたが、これは生成完了時の無駄な処理を無くすリファクタリングで、裏方の効率化ですのでユーザー操作には影響ありません。また、Mermaid記法の図やHTML埋め込みコンテンツを使っている場合に、テンプレート中の細かな記述ミスが修正されたことで、レイアウト崩れ等が起きず正しく描画されるようになっています。さらに、GraphAIコンポーネントのアップデートにより、もしグラフ自動生成のような機能を間接的に利用している場合は、より安定・高性能になっている可能性があります（これも内部的な改善なので意識しなくて大丈夫です）。

まとめると、バージョン0.0.15はクリエイターの創作体験を強化するアップデートです。最大のトピックはビジュアル面の進化で、スクリプトに沿って自動で画像・スライド・グラフなどを入れ込めるようになり、コンテンツの情報量と魅力が増します。映像面ではトランジションで滑らかさが増し、出力管理では保存場所指定やElevenLabs対応で柔軟性と品質アップが図れます。ドライランなど上手く使えば制作の効率も上げられるでしょう。ぜひ新機能を試し、Mulmocast CLIが提供するクリエイティブな可能性を存分に活用してみてください。あなたのコンテンツ制作がこれまで以上にパワーアップすること間違いありません！

---

## Pull Request Summaries (English & Japanese)

### #461 – Add PDF engine option (WIP)
- **English**: Introduced a work-in-progress feature to allow selecting a PDF generation engine. This PR merged updates from a previous attempt (#410) with the latest main branch and fixed build issues. It added an option for choosing different PDF output modes/sizes (e.g. handout, slides), and included sample outputs for each mode.
- **日本語**: PDF生成エンジンを選択可能にする試験的な機能が追加されました。以前のPR #410 の内容を最新の main ブランチにマージし、ビルドの不具合を修正しています。ハンドアウトやスライドなど各種モードに応じたPDF出力エンジンのオプションが追加され、各モードでのPDF出力サンプルが含まれています。

### #465 – Remove PDF engine option
- **English**: Removed the PDF engine selection feature that was introduced earlier. This reversed the changes from #461, eliminating the option to choose an alternate PDF generation engine. The code was simplified back to using the default PDF generation method due to the decision not to include the alternative engine.
- **日本語**: 前述のPDFエンジン選択機能を削除しました。#461 で導入された変更を取り消し、代替のPDF生成エンジンを選択するオプションを廃止しています。結果として、PDF生成は従来のデフォルト手法のみに戻され、コードが簡素化されました。

### #466 – Docs: Update README for ElevenLabs TTS config
- **English**: Updated documentation to include instructions for configuring ElevenLabs text-to-speech. The README now has details on how to set up and use ElevenLabs as a TTS provider in Mulmocast CLI, making it easier for users to integrate this service for voice generation.
- **日本語**: ドキュメント（README）が更新され、ElevenLabs のテキスト読み上げ（TTS）の設定方法が追記されました。Mulmocast CLIでElevenLabsを音声合成プロバイダーとして利用する手順が詳述されており、ユーザーがこのサービスを音声生成に統合しやすくなっています。

### #467 – Video transition
- **English**: Implemented support for video transitions between beats/scenes. This update ensures that transitions now work even for beats that include video content. A new test file (test_transition.json) was added to verify transition effects. Additionally, it fixes an issue where beats with a video but no text had incorrect duration; the duration for video-only beats with empty text is now set correctly.
- **日本語**: ビデオトランジション（場面転換効果）のサポートが実装されました。ビデオ付きのビート（シーン）間でもトランジションが正しく動作するようになります。トランジション効果を検証するためのテストファイル（test_transition.json）も追加されています。また、テキストが空のビデオ付きビートで再生時間が正しく設定されない不具合を修正し、テキストが無い場合でもビデオビートの再生時間が正しく設定されるようになりました。

### #468 – Add output file path
- **English**: Added the ability to specify an output file path for generated files. A new option or internal function allows the CLI to determine the output directory/path (e.g. for PDFs) based on a given setting. This makes it possible for users to direct where generated media files are saved, instead of using only the default location.
- **日本語**: 生成ファイルの出力先パスを指定できる機能が追加されました。新しいオプション（または内部関数）により、与えられた設定に基づいてCLIが出力ディレクトリ/パス（PDFなど）を決定できるようになっています。これにより、生成されたメディアファイルの保存先をデフォルト以外の任意の場所に指定できるようになりました。

### #469 – Remove pdflib
- **English**: Removed the pdflib library/dependency from the project. This change eliminated code and utilities related to pdflib, likely as part of simplifying PDF generation. By removing this library, the project now relies on a single PDF generation method and reduces external dependencies.
- **日本語**: プロジェクトから pdflib ライブラリ（依存関係）を削除しました。pdflib に関連するコードやユーティリティが削除されており、PDF生成処理の簡素化の一環と思われます。この変更により、PDF生成方法が一本化され、外部依存が削減されています。

### #470 – Add test_image_preprocess
- **English**: Added a new test for image preprocessing functionality. This introduces automated tests to verify that image preprocessing (e.g. resizing, formatting images before use) works correctly. It helps ensure reliability of the image handling pipeline by catching issues early through continuous integration.
- **日本語**: 画像プリプロセス機能に関する新しいテストを追加しました。画像の前処理（例：使用前のリサイズやフォーマット）が正しく機能することを確認する自動テストが導入されています。これにより、CI（継続的インテグレーション）で画像処理パイプラインの信頼性を確保し、問題を早期に発見できるようになります。

### #473 – Add test_beats.json test
- **English**: Introduced a test case using a beats.json file to validate beat processing. This test ensures that the application can correctly handle and interpret a beats configuration (the timeline or script of beats for a project). It increases test coverage for the core content structure ("beats") functionality.
- **日本語**: beats.json ファイルを使用したテストケースが追加され、ビートの処理が検証されるようになりました。これにより、プロジェクトのビート（脚本やタイムライン）構成をアプリケーションが正しく解釈・処理できることを確認しています。ビート構造に関する機能のテストカバレッジが向上しました。

### #471 – Fix agent filter no context
- **English**: Fixed a bug in agent filtering when no context is provided. Previously, if an agent filter was applied without a given context, it could cause errors or unexpected behavior. This patch ensures that the filter behaves gracefully (or is skipped) when context information is missing, improving stability of agent operations.
- **日本語**: コンテキストがない場合のエージェントフィルター処理のバグを修正しました。従来は、コンテキストなしでエージェントフィルターが適用されるとエラーや予期しない動作を引き起こす可能性がありました。この修正により、コンテキスト情報が無い場合でもフィルターが適切に対処（もしくはスキップ）されるようになり、エージェント動作の安定性が向上しています。

### #472 – release_test
- **English**: Added a release_test (release testing) routine. This likely involves new test code or scripts to verify that the release build of the CLI works as expected. It may include sample scenarios or checks (15 new lines of test code) ensuring that packaging and distribution aspects are functioning, which helps maintain release quality.
- **日本語**: release_test（リリーステスト）用の処理が追加されました。リリースビルドが期待通り動作するか確認するテストコードやスクリプトが新たに含まれています（約15行のコードが追加）。サンプルシナリオの実行やパッケージング/配布に関するチェックを行う内容で、リリース時の品質保証に役立てられています。

### #474 – movieTest
- **English**: Introduced a movieTest to the test suite. This is a new automated test (or set of tests) focused on video/movie generation functionality. It likely runs a sample movie generation through the CLI to ensure that the video creation pipeline (combining audio, images, transitions, etc.) executes without errors, thereby safeguarding the video output feature.
- **日本語**: テストスイートに movieTest が追加されました。これは動画生成機能に焦点を当てた新たな自動テスト（または一連のテスト）です。CLI上でサンプルの動画生成処理を実行し、（音声や画像、トランジションの組み合わせによる）動画作成パイプラインがエラー無く完了することを確認する内容となっており、動画出力機能の信頼性を高めます。

### #475 – Add reference beat image test (in test_media)
- **English**: Added a test for "reference beat image" functionality in the media tests. This ensures that when a beat references an image (for example, using an image as part of a beat's content), the system can correctly handle it. The test likely covers loading and integrating reference images into the media generation pipeline, confirming the new image-support features work as intended.
- **日本語**: test_media 内に「参照ビート画像」機能のテストが追加されました。ビートが画像を参照して利用する場合（例えばビートのコンテンツの一部に画像を指定するケース）に、システムが正しく処理できることを確認するものです。このテストでは、参照画像の読み込みやメディア生成処理への統合が正しく行われるかを検証し、新しい画像サポート機能が意図した通り動作することを保証しています。

### #476 – Improve tts_openai_agent error message
- **English**: Enhanced the error messages for the OpenAI TTS (text-to-speech) agent. If the OpenAI TTS agent encounters an error (for example, due to missing API keys or network issues), it now provides a clearer, more informative message. This improvement will help developers and users diagnose configuration issues with OpenAI's voice generation more easily.
- **日本語**: OpenAI TTS（テキスト読み上げ）エージェントのエラーメッセージが改善されました。OpenAI TTSエージェント使用時にエラーが発生した場合（例：APIキーの未設定やネットワーク問題）、より明確で詳細なエラーメッセージが表示されるようになります。この改善により、OpenAI音声合成の設定不備などの問題を開発者やユーザーが容易に特定できるようになりました。

### #478 – Beat image
- **English**: New Feature: Added support for images in "beats." This is a significant update allowing each beat (segment of the script) to include or generate an image. The system now can handle an image property in beat definitions, which can be provided by the user or generated from prompts. Multiple image-generation plugins/types were introduced (e.g., using a direct image by URL or path, creating slides with text and bullets, rendering markdown to image, generating charts via Chart.js, diagrams via Mermaid, or even HTML/Tailwind content). The logic for visual generation follows new rules: if an image is specified, it's used; if not, but an imagePrompt is given, an image is generated from that prompt; if only a moviePrompt is given, no static image is generated; if none are given, a prompt is auto-derived from text to generate an image. If both an image (or generated image) and a moviePrompt exist, the image plus the prompt are used together to produce video. This feature greatly enhances the visual capabilities of Mulmocast, enabling richer content (like custom graphics, slides, charts) to be incorporated into videos and PDFs.
- **日本語**: 新機能: ビート（脚本の各セクション）で画像を扱えるようになりました。各ビートに画像を含めたり、画像を生成したりすることが可能となる大規模なアップデートです。ビート定義内でimageプロパティが利用できるようになり、ユーザーが直接画像を指定することも、プロンプトから画像を自動生成することもできます。複数の画像生成プラグイン/タイプが導入されており、例として、URLやパスで指定した画像の直接使用、タイトルと箇条書きからスライド画像を生成する「textSlide」、Markdownテキストを画像としてレンダリングする「markdown」、Chart.jsによるグラフ生成「chart」、Mermaidによるダイアグラム生成「mermaid」、あるいはHTML+Tailwind内容の画像化「html_tailwind」などが可能です。ビートのビジュアル生成には新しいルールが適用されます：画像プロパティがあればそのimage.typeに応じた方法で画像を取得・生成し、なければimagePromptから画像を生成、それもなければmoviePromptだけで動画生成を行い、どれもない場合はテキストから自動で画像用プロンプトを生成して画像を生成します。また、画像（または生成された画像）が得られ、なおかつmoviePromptが存在する場合には、その画像とmoviePromptを組み合わせて映像を生成します。この機能により、Mulmocastのビジュアル表現力が飛躍的に向上し、動画やPDFにオリジナルのグラフィック、スライド、チャートなどより豊かなコンテンツを組み込むことが可能になりました。

### #479 – Got rid of onComplete node
- **English**: Refactored the internal processing graph by removing the onComplete node. This change streamlines the workflow/pipeline by eliminating a now-unnecessary node that was executed upon completion of tasks. Removing the onComplete node likely simplifies the code and could marginally improve performance or reduce confusion in the execution flow, as the completion handling is now integrated or handled differently.
- **日本語**: 内部処理フローのリファクタリングとして、onComplete ノードが削除されました。タスク完了時に実行されていたこのノードを取り除くことで、ワークフロー/パイプラインが簡潔になり、不必要な処理が減りました。onCompleteノードの削除により、コードがシンプルになり、実行フロー内での完了処理が別の形で統合・処理されることで、パフォーマンスのわずかな向上や処理流れの明確化が期待できます。

### #480 – Updated document script
- **English**: Updated the script related to documentation generation or export. This could refer to improving how documents (like PDFs or project documentation) are produced. Given the context, it might have been adjusted to accommodate the new image handling (ensuring generated documents include new visual content properly) or to fix formatting issues. This update modernizes the document-generation script for accuracy and consistency with the latest features.
- **日本語**: ドキュメント生成や書き出しに関するスクリプトが更新されました。これはPDFなどのドキュメントを生成する処理の改善を指している可能性があります。コンテキストから判断すると、新しい画像処理機能に対応するため（生成されるドキュメントに新しいビジュアルコンテンツが正しく反映されるように）調整されたか、あるいは書式の問題を修正したものと思われます。この更新により、ドキュメント生成スクリプトが最新の機能に合わせて正確かつ一貫性のあるものに改善されています。

### #483 – Update graphai
- **English**: Upgraded the GraphAI component/dependency to version 2.0.6. This update brings in the latest improvements or fixes from the GraphAI library. GraphAI is likely used for creating graph-based visuals or performing AI-driven planning in content; updating it means Mulmocast CLI can leverage new features or stability enhancements from that library, improving output quality or performance where GraphAI is involved.
- **日本語**: GraphAI コンポーネント（依存ライブラリ）がバージョン2.0.6にアップデートされました。この更新により、GraphAIライブラリの最新の改良やバグ修正が取り入れられます。GraphAIはグラフに基づくビジュアル生成やコンテンツのAIプランニングに使用されていると考えられ、このアップデートによってMulmocast CLIは同ライブラリの新機能や安定性向上の恩恵を受け、GraphAIを利用する機能における出力品質やパフォーマンスが改善されます。

### #482 – Add fake data generator
- **English**: Added a fake data generator utility. This feature provides a way to generate synthetic data for testing or demo purposes. It likely includes functions or scripts to produce placeholder content (such as dummy text, images, or chart data) which can be used to test the CLI's media generation without real input. Developers can use this to easily create test scenarios and ensure the system behaves correctly with sample data.
- **日本語**: フェイクデータジェネレーター ユーティリティが追加されました。テストやデモ向けに合成データを生成するための機能です。ダミーのテキスト、画像、チャート用データなど、架空のコンテンツを作り出す関数やスクリプトが含まれていると考えられます。これにより、開発者は実際の入力データがなくても簡単にテストシナリオを作成でき、サンプルデータでMulmocast CLIの動作を検証しやすくなります。

### #485 – Dry run
- **English**: Introduced a "dry run" mode for the CLI. This adds the ability to execute the generation process in simulation, without producing the actual heavy outputs (no real video/audio files generated). In implementation, a dryRun flag or mode uses mock agents and skips external service calls. Initially, this PR added conditions in the code (e.g., if dryRun then use mock agent instead of real generator). It allows developers/users to quickly validate a project's script flow (checking that all steps succeed) without waiting for full rendering, greatly speeding up debugging and iteration.
- **日本語**: CLIに**「ドライラン」**モードが導入されました。実際の重い出力（動画・音声ファイルなど）を生成せずに処理をシミュレーション実行できる機能です。実装上は、dryRun フラグやモードを追加し、モックのエージェントを使用して外部サービスへの実際のリクエストを行わないようにしています（例：dryRunが真の場合は実際の生成器の代わりにモックエージェントを使用する等の条件分岐を追加）。この機能により、開発者やユーザーはプロジェクトのシナリオフローを素早く検証でき、フルレンダリングを待たずに各ステップが正しく動作するか確認できるため、デバッグや反復作業の効率が大幅に向上します。

### #486 – Media mock agent
- **English**: Added a mediaMockAgent to support the dry-run and testing features. This is a special agent that simulates media generation (for images, audio, video) by providing fake or stub outputs. The PR refines the dry-run implementation by introducing a dedicated "dryrun" provider/agent that can be selected instead of the real providers. With this media mock agent, the dry run mode now consistently avoids real media creation for all types (image, audio, movie), making the simulation more comprehensive and allowing the dry-run flag to apply uniformly across different media outputs.
- **日本語**: mediaMockAgent（メディアモックエージェント）が追加され、ドライランおよびテスト機能を支援するようになりました。これは画像・音声・動画の生成を模倣し、フェイクの出力を返す特殊なエージェントです。本PRでは、実際のプロバイダの代わりに選択可能な専用の「dryrun」プロバイダ/エージェントを導入することで、ドライラン実行の実装を改善しています。MediaMockAgentにより、ドライランモードで全ての種類のメディア（画像・音声・動画）の実生成を確実に回避できるようになり、シミュレーションが一貫した形で行われます。これにより、dry-run フラグが様々なメディア出力に対して統一的に適用され、より包括的な動作検証が可能になりました。

### #487 – Add image.md
- **English**: Added a new documentation file docs/image.md detailing the image-generation rules and usage. This documentation (written in Japanese) explains how the image property works, the priority of image, imagePrompt, and moviePrompt, and provides JSON examples for different image.type values (such as using remote images via URL, local images via path, remote videos, text slides, markdown content, charts, Mermaid diagrams, and HTML with Tailwind). It serves as a guide for users to understand and utilize the new image/visual features introduced in this release.
- **日本語**: docs/image.md という新しいドキュメントファイルが追加され、画像生成のルールと使い方が詳しく説明されました。このドキュメントでは、画像プロパティの動作や、image・imagePrompt・moviePromptの優先順位が解説されており、様々なimage.typeのJSON例（URL経由のリモート画像、パス指定のローカル画像、リモート動画、テキストスライド、Markdownコンテンツ、Chart.jsによるチャート、Mermaid記法のダイアグラム、Tailwindを用いたHTMLなど）が含まれています。本リリースで導入された新しい画像/ビジュアル機能をユーザーが理解し活用するためのガイドとなっています。

### #488 – Add images
- **English**: Included new image assets in the repository. This PR added image files (23 new lines in assets, likely binary files such as PNGs or other media) used for features or documentation examples. For instance, it includes images like mulmocast_credit.png and possibly others needed for default content or as samples in the documentation (as referenced in image.md). These assets support the new image functionality by providing ready-to-use visuals and ensuring the documentation examples have corresponding actual image files.
- **日本語**: リポジトリに新たな画像アセットが追加されました。このPRでは画像ファイルが追加されており（23行分のバイナリファイルの変更があることから、PNG等の画像がいくつか含まれていると推測されます）、mulmocast_credit.png などドキュメントや機能で使用するメディアが含まれています。これらの画像アセットは、新しい画像機能を支えるもので、ドキュメント内のサンプルで参照されている実際の画像ファイルを提供するとともに、デフォルトのコンテンツや例としてすぐ使えるビジュアル素材を用意しています。

### #489 – Fix markdown HTML typo
- **English**: Fixed a minor typo in Markdown/HTML content. This was a one-line change correcting an HTML formatting mistake in a markdown context (perhaps a small tag closure or syntax error in documentation or template). The fix ensures that the markdown content is rendered correctly without broken HTML, improving the quality of documentation or generated content.
- **日本語**: Markdown/HTMLコンテンツ中の軽微なタイポ（誤字）を修正しました。1行のみの変更で、Markdown内のHTML整形の誤り（おそらくドキュメントやテンプレート中のタグの閉じ忘れや構文ミス）を修正しています。この修正により、Markdownコンテンツが正しくレンダリングされ、HTMLの崩れが解消されてドキュメントや生成物の品質が向上します。