# プロンプト
0.0.25 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.25

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
要約の文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は [v0.0.16.md](./v0.0.16.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## 今回のリリースに含まれる Pull Request
## What's Changed
* Save html on htmlPromp by @snakajima in https://github.com/receptron/mulmocast-cli/pull/582
* fix: set max tokens by @isamu in https://github.com/receptron/mulmocast-cli/pull/583
* getHtmlImageAgentInfo use llmConfig by @isamu in https://github.com/receptron/mulmocast-cli/pull/584
* RL sample by @snakajima in https://github.com/receptron/mulmocast-cli/pull/585
* add release note: v0.0.23 - v0.0.24 by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/586
* docs: add Anthropic API configuration guide by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/587
* update by @isamu in https://github.com/receptron/mulmocast-cli/pull/588


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.24...0.0.25

--- 以下、Generated by Claude Code --- 

## Pull Request Summaries / PRサマリー

### PR #582: Save html on htmlPrompt - @snakajima
- **English**: Implemented HTML saving functionality for the htmlPrompt feature, significantly enhancing debugging capabilities and reusability of generated HTML content. When using htmlPrompt to generate images through HTML rendering, the system now automatically saves the generated HTML files, allowing developers to inspect the exact HTML that was used to create each image. This is particularly valuable for troubleshooting layout issues, understanding how AI interprets prompts into HTML structures, and reusing successful HTML templates for future image generation. The saved HTML files serve as both documentation and a cache, enabling faster iteration and better understanding of the HTML-to-image pipeline.
- **日本語**: htmlPrompt機能にHTML保存機能を実装し、デバッグ機能と生成されたHTMLコンテンツの再利用性を大幅に向上させました。HTMLレンダリングを通じて画像を生成するhtmlPromptを使用する際、システムは生成されたHTMLファイルを自動的に保存するようになり、開発者は各画像の作成に使用された正確なHTMLを検査できます。これは、レイアウトの問題のトラブルシューティング、AIがプロンプトをHTML構造にどのように解釈するかの理解、将来の画像生成のための成功したHTMLテンプレートの再利用において特に価値があります。保存されたHTMLファイルは、ドキュメントとキャッシュの両方として機能し、より速い反復とHTMLから画像へのパイプラインのより良い理解を可能にします。

### PR #583: fix: set max tokens - @isamu  
- **English**: Fixed a critical issue with max_tokens configuration in LLM API calls that was causing unexpected token limit errors or inefficient API usage. The fix ensures that the max_tokens parameter is properly set and propagated through all LLM interactions, preventing situations where responses are truncated prematurely or where excessive tokens are requested unnecessarily. This change is particularly important for htmlPrompt and other features that generate longer outputs, as proper token management directly affects both the quality of generated content and API cost efficiency. The fix improves reliability across all LLM-powered features in MulmoCast.
- **日本語**: 予期しないトークン制限エラーや非効率的なAPI使用を引き起こしていたLLM APIコールのmax_tokens設定に関する重要な問題を修正しました。この修正により、max_tokensパラメータがすべてのLLMインタラクションを通じて適切に設定され、伝播されることが保証され、レスポンスが早期に切り捨てられたり、過剰なトークンが不必要に要求されたりする状況を防ぎます。この変更は、適切なトークン管理が生成されたコンテンツの品質とAPIコスト効率の両方に直接影響するため、より長い出力を生成するhtmlPromptやその他の機能にとって特に重要です。この修正により、MulmoCastのすべてのLLM駆動機能の信頼性が向上します。

### PR #584: getHtmlImageAgentInfo use llmConfig - @isamu
- **English**: Refactored the `getHtmlImageAgentInfo` function to utilize the centralized `llmConfig` configuration system, creating a more maintainable and consistent approach to LLM configuration management. Previously, LLM configurations were scattered across different parts of the codebase, making it difficult to maintain consistency and implement global changes. By centralizing these configurations through `llmConfig`, the system now provides a single source of truth for all LLM-related settings, including model selection, token limits, and provider-specific parameters. This refactoring particularly benefits the htmlPrompt feature, ensuring that all HTML-to-image generation processes use consistent LLM settings and making it easier to switch between different LLM providers or adjust global parameters.
- **日本語**: `getHtmlImageAgentInfo`関数を中央管理された`llmConfig`設定システムを使用するようにリファクタリングし、LLM設定管理へのより保守しやすく一貫したアプローチを作成しました。以前は、LLM設定がコードベースの異なる部分に散在しており、一貫性を維持し、グローバルな変更を実装することが困難でした。`llmConfig`を通じてこれらの設定を中央管理することで、システムは現在、モデル選択、トークン制限、プロバイダー固有のパラメータを含むすべてのLLM関連設定の単一の真実の源を提供しています。このリファクタリングは特にhtmlPrompt機能に利益をもたらし、すべてのHTMLから画像への生成プロセスが一貫したLLM設定を使用することを保証し、異なるLLMプロバイダー間の切り替えやグローバルパラメータの調整を容易にします。

### PR #585: RL sample - @snakajima
- **English**: Added reinforcement learning (RL) educational content sample scripts demonstrating htmlPrompt and traditional image generation approaches. The samples include `rl_html.json` and `rl_image.json`, providing educational content about reinforcement learning concepts with parallel implementations for HTML-based and traditional image generation. These samples serve as reference implementations for creating educational content and showcase the differences between htmlPrompt and standard image generation workflows.
- **日本語**: 強化学習（RL）の教育コンテンツサンプルスクリプトを追加し、htmlPromptと従来の画像生成アプローチを実証しました。サンプルには`rl_html.json`と`rl_image.json`が含まれ、HTMLベースと従来の画像生成の並列実装で強化学習の概念に関する教育コンテンツを提供します。これらのサンプルは、教育コンテンツ作成のリファレンス実装として機能し、htmlPromptと標準的な画像生成ワークフローの違いを示しています。

### PR #586: add release note: v0.0.23 - v0.0.24 - @ystknsh
- **English**: Added comprehensive release notes for versions v0.0.23 and v0.0.24, providing detailed documentation of all changes, improvements, and new features introduced in these releases. The release notes follow the established format with multiple perspectives (developer/creator, English/Japanese), ensuring that all stakeholders can understand the impact of changes in their preferred language and technical level. This documentation effort is crucial for maintaining transparency about the project's evolution, helping users understand what has changed between versions, and providing migration guidance when necessary. The detailed PR summaries included in these release notes serve as valuable historical documentation for future development decisions.
- **日本語**: バージョンv0.0.23およびv0.0.24の包括的なリリースノートを追加し、これらのリリースで導入されたすべての変更、改善、新機能の詳細なドキュメントを提供しました。リリースノートは、複数の視点（開発者/クリエイター、英語/日本語）で確立された形式に従っており、すべての利害関係者が好みの言語と技術レベルで変更の影響を理解できることを保証します。このドキュメント作成の取り組みは、プロジェクトの進化について透明性を維持し、ユーザーがバージョン間で何が変更されたかを理解するのを助け、必要に応じて移行ガイダンスを提供するために重要です。これらのリリースノートに含まれる詳細なPRサマリーは、将来の開発決定のための貴重な履歴ドキュメントとして機能します。

### PR #587: docs: add Anthropic API configuration guide - @ystknsh
- **English**: Enhanced documentation by adding a comprehensive guide for configuring the Anthropic API (Claude) integration in MulmoCast. This documentation update covers essential setup steps including environment variable configuration (`ANTHROPIC_API_TOKEN`), specific usage instructions for the htmlPrompt feature with Claude models, and best practices for leveraging Anthropic's advanced language capabilities. The guide is particularly valuable as it clarifies how to enable and use Claude for HTML-based image generation, which offers different strengths compared to other LLM providers. This documentation ensures that users can quickly set up and take advantage of Claude's capabilities for generating sophisticated HTML layouts and creative content interpretations.
- **日本語**: MulmoCastにおけるAnthropic API（Claude）統合の設定に関する包括的なガイドを追加することで、ドキュメントを強化しました。このドキュメントの更新では、環境変数の設定（`ANTHROPIC_API_TOKEN`）、ClaudeモデルでのhtmlPrompt機能の具体的な使用手順、Anthropicの高度な言語機能を活用するためのベストプラクティスなど、重要なセットアップ手順をカバーしています。このガイドは、他のLLMプロバイダーと比較して異なる強みを提供するHTMLベースの画像生成のためにClaudeを有効にして使用する方法を明確にするため、特に価値があります。このドキュメントにより、ユーザーは洗練されたHTMLレイアウトと創造的なコンテンツ解釈を生成するためのClaudeの機能を迅速にセットアップして活用できることが保証されます。

### PR #588: update - @isamu
- **English**: Performed essential maintenance updates to keep the MulmoCast codebase current and stable. While the specific changes aren't detailed in the PR title, these types of updates typically include dependency version bumps, minor bug fixes, code cleanup, or small improvements that enhance overall system reliability. Regular maintenance updates like this are crucial for preventing technical debt accumulation, ensuring compatibility with the latest versions of dependencies, and maintaining the health of the codebase. These updates, though sometimes minor individually, collectively contribute to the stability and performance of MulmoCast, making it more reliable for all users.
- **日本語**: MulmoCastのコードベースを最新かつ安定した状態に保つための重要なメンテナンス更新を実行しました。PR タイトルには具体的な変更が詳述されていませんが、これらのタイプの更新には通常、依存関係のバージョンアップ、マイナーなバグ修正、コードのクリーンアップ、またはシステム全体の信頼性を向上させる小さな改善が含まれます。このような定期的なメンテナンス更新は、技術的負債の蓄積を防ぎ、依存関係の最新バージョンとの互換性を確保し、コードベースの健全性を維持するために重要です。これらの更新は、個別には時に小さなものですが、集合的にMulmoCastの安定性とパフォーマンスに貢献し、すべてのユーザーにとってより信頼性の高いものにします。

---

## Developer Release Notes (English)

### New Features and Enhancements

**HTML-Based Image Generation Improvements**
- **HTML Saving in htmlPrompt** (#582): The htmlPrompt feature now automatically saves generated HTML files alongside images, providing valuable debugging capabilities and enabling reuse of successful HTML templates. This enhancement significantly improves the development workflow for HTML-based image generation.

**Educational Content Samples**
- **RL Educational Scripts** (#585): Added sample scripts for reinforcement learning educational content, demonstrating both htmlPrompt and traditional image generation approaches in parallel implementations.

### Bug Fixes and Improvements

**LLM Integration Fixes**
- **Max Tokens Configuration** (#583): Fixed critical token limit handling in LLM API calls, preventing premature response truncation and optimizing API usage efficiency
- **Centralized LLM Configuration** (#584): Refactored `getHtmlImageAgentInfo` to use centralized `llmConfig`, ensuring consistent LLM settings across all features

**Documentation and Maintenance**
- **Anthropic API Guide** (#587): Added comprehensive documentation for Claude integration, including setup instructions and htmlPrompt usage guidelines
- **Release Notes** (#586): Added detailed release notes for v0.0.23 and v0.0.24
- **General Updates** (#588): Various maintenance updates to keep the codebase stable and current

### Technical Details

The htmlPrompt HTML saving feature (#582) creates a persistent record of generated HTML, invaluable for:
- Debugging layout and rendering issues
- Understanding AI prompt interpretation
- Building a library of reusable HTML templates

The LLM configuration improvements (#583, #584) establish a more robust foundation for multi-provider LLM support, making it easier to:
- Switch between different LLM providers
- Maintain consistent behavior across features
- Optimize token usage and API costs

---

## Developer Release Notes (Japanese)

### 新機能と機能強化

**HTMLベース画像生成の改善**
- **htmlPromptでのHTML保存** (#582): htmlPrompt機能が生成されたHTMLファイルを画像と共に自動保存するようになり、貴重なデバッグ機能と成功したHTMLテンプレートの再利用を可能にしました。この機能強化により、HTMLベースの画像生成の開発ワークフローが大幅に改善されます。

**教育コンテンツサンプル**
- **RL教育スクリプト** (#585): 強化学習の教育コンテンツ用サンプルスクリプトを追加し、htmlPromptと従来の画像生成アプローチを並列実装で実証。

### バグ修正と改善

**LLM統合の修正**
- **Max Tokens設定** (#583): LLM APIコールにおける重要なトークン制限処理を修正し、レスポンスの早期切り捨てを防ぎ、API使用効率を最適化
- **中央管理されたLLM設定** (#584): `getHtmlImageAgentInfo`を中央管理された`llmConfig`を使用するようにリファクタリングし、すべての機能で一貫したLLM設定を保証

**ドキュメントとメンテナンス**
- **Anthropic APIガイド** (#587): Claude統合の包括的なドキュメントを追加、セットアップ手順とhtmlPrompt使用ガイドラインを含む
- **リリースノート** (#586): v0.0.23とv0.0.24の詳細なリリースノートを追加
- **一般的な更新** (#588): コードベースを安定かつ最新の状態に保つための各種メンテナンス更新

### 技術詳細

htmlPromptのHTML保存機能（#582）は、生成されたHTMLの永続的な記録を作成し、以下に価値があります：
- レイアウトとレンダリングの問題のデバッグ
- AIプロンプト解釈の理解
- 再利用可能なHTMLテンプレートライブラリの構築

LLM設定の改善（#583、#584）は、マルチプロバイダーLLMサポートのためのより堅牢な基盤を確立し、以下を容易にします：
- 異なるLLMプロバイダー間の切り替え
- 機能間での一貫した動作の維持
- トークン使用とAPIコストの最適化

---

## Creator Release Notes (English)

### What's New

**Better HTML-Based Image Creation**
- When using the htmlPrompt feature to create images, the system now saves the HTML code used to generate each image. This makes it easier to understand how your prompts are interpreted and lets you reuse successful designs.

**New Educational Samples**
- Added sample scripts for reinforcement learning educational content, showing how to create educational presentations with both HTML-based and traditional image generation.

### What's Fixed

**More Reliable AI Responses**
- Fixed issues where AI responses could be cut off unexpectedly
- Improved consistency in AI behavior across different features

### What's Improved

**Better Documentation**
- Added clear instructions for using Claude (Anthropic's AI) with MulmoCast
- Comprehensive release notes now available for recent versions
- Various stability improvements throughout the system

---

## Creator Release Notes (Japanese)

### 新機能

**HTMLベースの画像作成の改善**
- htmlPrompt機能を使用して画像を作成する際、各画像の生成に使用されたHTMLコードがシステムに保存されるようになりました。これにより、プロンプトがどのように解釈されるかを理解しやすくなり、成功したデザインを再利用できます。

**新しい教育サンプル**
- 強化学習の教育コンテンツ用サンプルスクリプトを追加し、HTMLベースと従来の画像生成の両方で教育プレゼンテーションを作成する方法を示しています。

### 修正された問題

**より信頼性の高いAIレスポンス**
- AIレスポンスが予期せず途切れる問題を修正
- 異なる機能間でのAI動作の一貫性を改善

### 改善点

**より良いドキュメント**
- MulmoCastでClaude（AnthropicのAI）を使用するための明確な手順を追加
- 最近のバージョンの包括的なリリースノートが利用可能に
- システム全体の様々な安定性の改善