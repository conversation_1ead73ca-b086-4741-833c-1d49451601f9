{"name": "mulmocast", "version": "1.1.9", "description": "", "type": "module", "main": "lib/index.node.js", "exports": {".": {"types": "./lib/index.node.d.ts", "default": "./lib/index.node.js"}, "./browser": {"types": "./lib/index.browser.d.ts", "default": "./lib/index.browser.js"}, "./data": {"default": "./lib/data/index.js"}}, "bin": {"mulmo": "lib/cli/bin.js"}, "files": ["./lib", "./scripts/templates", "./scripts/test", "./assets/audio/silent60sec.mp3", "./assets/html/", "./assets/templates/"], "directories": {"lib": "lib", "test": "tests"}, "scripts": {"audio": "npx tsx ./src/cli/bin.ts audio", "translate": "npx tsx ./src/cli/bin.ts translate", "movie": "npx tsx ./src/cli/bin.ts movie", "images": "npx tsx ./src/cli/bin.ts images", "preprocess": "npx tsx ./src/cli/bin.ts preprocess", "pdf": "npx tsx ./src/cli/bin.ts pdf", "test": "rm -f scratchpad/test*.* && npx tsx ./src/audio.ts scripts/test/test.json && npx tsx ./src/images.ts scripts/test/test.json && npx tsx ./src/movie.ts scripts/test/test.json", "ci_test": "NODE_ENV=test tsx --test ./test/*/test_*.ts", "lint": "eslint src test", "build": "tsc", "build_test": "tsc && git checkout -- lib/*", "cli": "npx tsx ./src/cli/bin.ts", "scripting": "npx tsx ./src/cli/bin.ts tool scripting", "prompt": "npx tsx ./src/cli/bin.ts tool prompt", "schema": "npx tsx ./src/cli/bin.ts tool schema", "story_to_script": "npx tsx ./src/cli/bin.ts tool story_to_script", "latest": "yarn upgrade-interactive  --latest", "format": "prettier --write '{src,scripts,assets/templates,assets/styles,draft,ideason,scripts_mag2,proto,test,batch,graphai,output,docs/scripts}/**/*.{ts,json,yaml}'", "deep_research": "npx tsx ./src/tools/deep_research.ts", "template": "npx tsx batch/template2tsobject.ts && yarn run format", "fake_data": "npx tsx test/fake/sample.ts", "mcp_server": "npx tsx ./src/mcp/server.ts"}, "repository": "git+ssh://**************/receptron/mulmocast-cli.git", "author": "sna<PERSON><PERSON>", "license": "AGPL-3.0-only", "bugs": {"url": "https://github.com/receptron/mulmocast-cli/issues"}, "homepage": "https://github.com/receptron/mulmocast-cli#readme", "dependencies": {"@google-cloud/text-to-speech": "^6.2.0", "@graphai/anthropic_agent": "^2.0.5", "@graphai/browserless_agent": "^2.0.1", "@graphai/gemini_agent": "^2.0.0", "@graphai/groq_agent": "^2.0.0", "@graphai/input_agents": "^1.0.1", "@graphai/openai_agent": "^2.0.3", "@graphai/stream_agent_filter": "^2.0.2", "@graphai/vanilla": "^2.0.6", "@graphai/vanilla_node_agents": "^2.0.1", "@modelcontextprotocol/sdk": "^1.15.1", "@tavily/core": "^0.5.9", "canvas": "^3.1.2", "clipboardy": "^4.0.0", "dotenv": "^17.2.1", "fluent-ffmpeg": "^2.1.3", "google-auth-library": "^10.1.0", "graphai": "^2.0.13", "inquirer": "^12.7.0", "marked": "^16.1.1", "ora": "^8.2.0", "puppeteer": "^24.15.0", "replicate": "^1.0.1", "yaml": "^2.8.0", "yargs": "^18.0.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@anatine/zod-mock": "^3.14.0", "@faker-js/faker": "^9.9.0", "@receptron/test_utils": "^2.0.0", "@types/fluent-ffmpeg": "^2.1.26", "@types/yargs": "^17.0.33", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-sonarjs": "^3.0.4", "prettier": "^3.6.2", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.9.2", "typescript-eslint": "^8.37.0"}, "engines": {"node": ">=18.0.0"}}