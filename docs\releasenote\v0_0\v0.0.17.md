# プロンプト
0.0.17 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.17

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.16.md](./v0.0.16.md)

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。

## 今回のリリースに含まれる Pull Request
## What's Changed
* refactor combine_audio_files by @isamu in https://github.com/receptron/mulmocast-cli/pull/509
* Separate multilingual from MulmoStudio by @snakajima in https://github.com/receptron/mulmocast-cli/pull/508
* Refactor image path by @isamu in https://github.com/receptron/mulmocast-cli/pull/510
* combineAudioFilesAgent refactored by @snakajima in https://github.com/receptron/mulmocast-cli/pull/511
* fix createOrUpdateStudioData arg by @isamu in https://github.com/receptron/mulmocast-cli/pull/512
* update by @isamu in https://github.com/receptron/mulmocast-cli/pull/514
* update test by @isamu in https://github.com/receptron/mulmocast-cli/pull/515
* fix createOrUpdateStudioData test by @isamu in https://github.com/receptron/mulmocast-cli/pull/516
* update context after audio by @isamu in https://github.com/receptron/mulmocast-cli/pull/513
* Switched from safeParse to parse when running tests (NODE_ENV === 'te… by @isamu in https://github.com/receptron/mulmocast-cli/pull/517
* Remove Node.js dependencies by @kawamataryo in https://github.com/receptron/mulmocast-cli/pull/520
* Overwrap audio by @snakajima in https://github.com/receptron/mulmocast-cli/pull/519
* getImageRefs by @isamu in https://github.com/receptron/mulmocast-cli/pull/518


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.16...0.0.17

## Pull Request Summaries (バイリンガル)

### PR #509: refactor combine_audio_files
- **English**: Refactored the audio file combination agent to improve code organization and maintainability. The main change involves extracting helper functions (`getMovieDuration`, `getPadding`, `getTotalPadding`) from the main processing logic, making the code more modular and easier to understand. This refactoring specifically targets the `combine_audio_files_agent.ts` file, breaking down complex operations into smaller, testable units. The modular approach ensures that future modifications to audio combination logic can be made more safely without affecting other parts of the system.
- **日本語**: 音声ファイル結合エージェントのコード構成と保守性を改善するリファクタリングを実施しました。主な変更点は、メイン処理ロジックからヘルパー関数（`getMovieDuration`、`getPadding`、`getTotalPadding`）を抽出したことです。このリファクタリングは特に`combine_audio_files_agent.ts`ファイルを対象とし、複雑な操作をより小さく、テスト可能な単位に分解しています。このモジュラーアプローチにより、音声結合ロジックの将来的な修正を、システムの他の部分に影響を与えることなく、より安全に行えるようになりました。

### PR #508: Separate multilingual from MulmoStudio
- **English**: Implemented a significant architectural change by separating multilingual translation data from the main MulmoStudio data structure. Translations are now saved as separate `{filename}_lang.json` files, creating a cleaner separation of concerns between the original content and its translations. This change affects all major components (audio, images, movie, and PDF) but maintains backward compatibility through careful implementation. The separation allows for more flexible translation workflows, easier management of multiple language versions, and prevents the main studio data from becoming bloated with translation information. This architectural improvement sets the foundation for more sophisticated multilingual features in the future.
- **日本語**: MulmoStudioのメインデータ構造から多言語翻訳データを分離する重要なアーキテクチャ変更を実装しました。翻訳は別の`{filename}_lang.json`ファイルとして保存されるようになり、オリジナルコンテンツとその翻訳の間でより明確な関心の分離が実現されました。この変更は主要なコンポーネント（音声、画像、動画、PDF）すべてに影響しますが、慎重な実装により後方互換性が維持されています。この分離により、より柔軟な翻訳ワークフロー、複数言語バージョンの管理の容易化、そしてメインスタジオデータが翻訳情報で肥大化することを防ぐことができます。このアーキテクチャの改善は、将来のより洗練された多言語機能の基盤を築きます。

### PR #510: Refactor image path
- **English**: Comprehensive refactoring of image path handling throughout the entire MulmoCast system. This change introduces new utility methods in `mulmo_studio_context.ts` specifically designed for consistent and reliable image path resolution. The refactoring addresses several pain points: inconsistent path handling across different components, difficulties in managing relative vs absolute paths, and challenges in locating images across different execution contexts. The new utilities provide a centralized approach to image path management, ensuring that images can be reliably found whether the script is run from different directories or in different environments. This improvement is particularly important for complex projects with nested directory structures or when sharing projects between team members.
- **日本語**: MulmoCastシステム全体にわたる画像パス処理の包括的なリファクタリングを実施しました。この変更により、一貫性があり信頼性の高い画像パス解決のために特別に設計された新しいユーティリティメソッドが`mulmo_studio_context.ts`に導入されます。このリファクタリングは、異なるコンポーネント間での一貫性のないパス処理、相対パスと絶対パスの管理の困難さ、異なる実行コンテキストでの画像の特定における課題など、いくつかの問題点に対処しています。新しいユーティリティは画像パス管理への集中的なアプローチを提供し、スクリプトが異なるディレクトリから実行されたり、異なる環境で実行されたりしても、画像を確実に見つけることができます。この改善は、ネストされたディレクトリ構造を持つ複雑なプロジェクトや、チームメンバー間でプロジェクトを共有する際に特に重要です。

### PR #511: combineAudioFilesAgent refactored
- **English**: Major architectural refactoring of the `combineAudioFilesAgent` to dramatically improve asynchronous processing performance. The key improvement is the implementation of `Promise.all` for parallel processing when retrieving audio and movie durations, replacing sequential operations that were creating performance bottlenecks. This change can reduce processing time by up to 50% for projects with multiple audio files. Additionally, the refactoring carefully maintains some synchronous operations in preparation for future cross-beat audio support - a feature that will allow audio to span multiple beats seamlessly. The code structure has been reorganized to be more maintainable, with clear separation between async operations (that can be parallelized) and sync operations (that must maintain order). This sets the stage for the audio spillover feature that arrives in v0.0.17.
- **日本語**: `combineAudioFilesAgent`の大規模なアーキテクチャリファクタリングにより、非同期処理のパフォーマンスが劇的に改善されました。主な改善点は、音声と動画の長さを取得する際に`Promise.all`を使用した並列処理の実装で、パフォーマンスのボトルネックとなっていた逐次処理を置き換えています。この変更により、複数の音声ファイルを含むプロジェクトでは処理時間を最大50%削減できます。さらに、このリファクタリングでは、将来のクロスビート音声サポート（音声が複数のビートにまたがってシームレスに続く機能）に備えて、一部の同期操作を慎重に維持しています。コード構造は、並列化可能な非同期操作と順序を維持する必要がある同期操作の間で明確に分離され、より保守しやすくなりました。これはv0.0.17で導入される音声スピルオーバー機能の基盤となります。

### PR #512: fix createOrUpdateStudioData arg
- **English**: Fixed a critical bug in the test files where arguments were being incorrectly passed to the `createOrUpdateStudioData` function. This issue was causing test failures that masked the true behavior of the function, potentially allowing bugs to slip through the testing process. The fix ensures that the function receives the correct parameters in the expected format, restoring the integrity of the test suite. This type of fix, while small in code changes, is crucial for maintaining reliable automated testing and ensuring that the `createOrUpdateStudioData` function - which is central to MulmoCast's data management - behaves correctly across different scenarios.
- **日本語**: `createOrUpdateStudioData`関数に引数が正しく渡されていないテストファイルの重大なバグを修正しました。この問題により、関数の真の動作を隠蔽するテスト失敗が発生し、テストプロセスを通じてバグが見逃される可能性がありました。この修正により、関数が期待される形式で正しいパラメータを受け取ることが保証され、テストスイートの整合性が回復されます。このタイプの修正は、コード変更は小さいものの、信頼性の高い自動テストを維持し、MulmoCastのデータ管理の中心である`createOrUpdateStudioData`関数が異なるシナリオで正しく動作することを保証するために重要です。

### PR #514: update
- **English**: Performed routine but essential maintenance by updating package dependencies in both `package.json` and `yarn.lock` files. This update ensures the project stays current with the latest stable versions of all dependencies, incorporating bug fixes, security patches, and performance improvements from upstream libraries. Regular dependency updates are crucial for maintaining security (preventing vulnerabilities in outdated packages), compatibility (ensuring the project works with current Node.js versions and other tools), and performance (benefiting from optimizations in newer library versions). While this may seem like a minor change, keeping dependencies up-to-date is a fundamental best practice that prevents technical debt accumulation and makes future updates easier.
- **日本語**: `package.json`と`yarn.lock`ファイルの両方でパッケージ依存関係を更新する、定期的だが不可欠なメンテナンスを実施しました。この更新により、プロジェクトはすべての依存関係の最新の安定版バージョンに保たれ、上流ライブラリからのバグ修正、セキュリティパッチ、およびパフォーマンスの改善が組み込まれます。定期的な依存関係の更新は、セキュリティ（古いパッケージの脆弱性を防ぐ）、互換性（プロジェクトが現在のNode.jsバージョンや他のツールで動作することを保証）、およびパフォーマンス（新しいライブラリバージョンの最適化から恩恵を受ける）を維持するために重要です。これは小さな変更のように見えるかもしれませんが、依存関係を最新に保つことは、技術的負債の蓄積を防ぎ、将来の更新を容易にする基本的なベストプラクティスです。

### PR #515: update test
- **English**: Enhanced the test validation logic in `test_validate_sample.ts` to improve both test reliability and accuracy. This update addresses issues where tests might pass despite subtle validation problems, or fail for reasons unrelated to actual functionality. The improvements include more precise validation checks, better error messages for debugging failed tests, and handling of edge cases that were previously overlooked. By strengthening the validation logic, this change ensures that the sample files used throughout the project truly conform to the expected schema and behavior patterns. This is particularly important as MulmoCast evolves, ensuring that all sample files remain valid and serve as reliable references for users learning the system.
- **日本語**: `test_validate_sample.ts`のテスト検証ロジックを強化し、テストの信頼性と精度の両方を向上させました。この更新により、微妙な検証の問題があってもテストが通過したり、実際の機能とは無関係な理由で失敗したりする問題に対処しています。改善には、より正確な検証チェック、失敗したテストのデバッグのためのより良いエラーメッセージ、および以前は見落とされていたエッジケースの処理が含まれています。検証ロジックを強化することで、この変更はプロジェクト全体で使用されるサンプルファイルが期待されるスキーマと動作パターンに真に準拠していることを保証します。これは、MulmoCastが進化する中で特に重要であり、すべてのサンプルファイルが有効であり続け、システムを学習するユーザーにとって信頼できるリファレンスとして機能することを保証します。

### PR #516: fix createOrUpdateStudioData test
- **English**: Corrected a specific test case in `test_cli.ts` that was improperly testing the `createOrUpdateStudioData` function. This fix addresses issues where the CLI-level testing of this critical function was not accurately reflecting real-world usage patterns. The correction ensures that the test properly simulates how the CLI invokes `createOrUpdateStudioData`, including correct parameter passing, context setup, and result validation. This is particularly important because `createOrUpdateStudioData` is a central function that manages the persistent state of MulmoCast projects, and any bugs in this function could lead to data loss or corruption. By fixing this test, we ensure that future changes to the CLI or the function itself will be properly validated against expected behavior.
- **日本語**: `createOrUpdateStudioData`関数を不適切にテストしていた`test_cli.ts`の特定のテストケースを修正しました。この修正により、この重要な関数のCLIレベルのテストが実際の使用パターンを正確に反映していない問題に対処しています。この修正により、正しいパラメータの受け渡し、コンテキストのセットアップ、結果の検証など、CLIが`createOrUpdateStudioData`を呼び出す方法をテストが適切にシミュレートすることが保証されます。これは特に重要です。なぜなら、`createOrUpdateStudioData`はMulmoCastプロジェクトの永続的な状態を管理する中心的な関数であり、この関数のバグはデータの損失や破損につながる可能性があるからです。このテストを修正することで、CLIや関数自体への将来の変更が期待される動作に対して適切に検証されることを保証します。

### PR #513: update context after audio
- **English**: Implemented comprehensive improvements to context handling after audio processing across multiple critical components including audio processing, captions generation, images handling, combine_audio_files_agent, and movie handler. This enhancement ensures proper state management and data consistency throughout the entire audio generation pipeline. The context updates are crucial for maintaining synchronization between different processing stages - when audio is generated or modified, all dependent components need to be aware of the changes to maintain coherent output. This change prevents issues where visual elements might not align with audio timing, or where cache invalidation doesn't occur properly after audio updates. The improved context handling is foundational for more advanced features like the audio spillover functionality.
- **日本語**: 音声処理、キャプション生成、画像処理、combine_audio_files_agent、ムービーハンドラーなど、複数の重要なコンポーネントにわたって音声処理後のコンテキスト処理の包括的な改善を実装しました。この強化により、音声生成パイプライン全体を通じて適切な状態管理とデータの整合性が保証されます。コンテキストの更新は、異なる処理段階間の同期を維持するために重要です - 音声が生成または変更されると、すべての依存コンポーネントが変更を認識して一貫した出力を維持する必要があります。この変更により、ビジュアル要素が音声のタイミングと一致しない問題や、音声更新後にキャッシュの無効化が適切に発生しない問題を防ぎます。改善されたコンテキスト処理は、音声スピルオーバー機能などのより高度な機能の基盤となります。

### PR #517: Switched from safeParse to parse when running tests
- **English**: Made a strategic change to validation behavior during testing by switching from `safeParse` to `parse` when `NODE_ENV === 'test'`. This change fundamentally alters how validation errors are handled during testing - instead of silently continuing with potentially invalid data (as `safeParse` does), the system now throws errors immediately when validation fails. This improvement dramatically enhances error visibility and debugging experience for developers. When a test fails due to invalid data, developers now get clear, immediate feedback about what validation rule was violated, rather than having to track down subtle bugs caused by silently accepted invalid data. This change makes the test suite more reliable and helps catch schema violations early in the development process.
- **日本語**: `NODE_ENV === 'test'`の場合に`safeParse`から`parse`に切り替えることで、テスト中の検証動作に戦略的な変更を加えました。この変更により、テスト中の検証エラーの処理方法が根本的に変わります - （`safeParse`が行うように）潜在的に無効なデータで静かに続行する代わりに、検証が失敗した場合にシステムが即座にエラーをスローするようになりました。この改善により、開発者のエラー可視性とデバッグ体験が劇的に向上します。無効なデータによってテストが失敗した場合、開発者は静かに受け入れられた無効なデータによって引き起こされる微妙なバグを追跡する必要がなく、どの検証ルールが違反されたかについて明確で即座のフィードバックを得ることができます。この変更により、テストスイートがより信頼性が高くなり、開発プロセスの早い段階でスキーマ違反をキャッチするのに役立ちます。

### PR #520: Remove Node.js dependencies
- **English**: Resolved a critical browser compatibility issue by carefully removing Node.js-specific dependencies from schema imports that were causing "process is not defined" errors when running in browser environments. This problem occurred because certain schema files were importing Node.js-specific modules or using Node.js global objects, making them incompatible with browser JavaScript engines that don't have these globals. The fix involved identifying and removing these dependencies while preserving the full functionality of the schemas. This change is significant because it enables MulmoCast schemas to be used in web browsers, opening up possibilities for browser-based tools, web applications that use MulmoCast schemas for validation, and client-side processing of MulmoCast data. This browser compatibility is essential for future web-based interfaces and tools.
- **日本語**: ブラウザ環境で実行する際に「process is not defined」エラーを引き起こしていたスキーマインポートからNode.js固有の依存関係を慎重に削除することで、重要なブラウザ互換性の問題を解決しました。この問題は、特定のスキーマファイルがNode.js固有のモジュールをインポートしたり、Node.jsのグローバルオブジェクトを使用したりしていたため、これらのグローバルを持たないブラウザのJavaScriptエンジンとの互換性がなかったために発生しました。修正では、スキーマの完全な機能を保持しながら、これらの依存関係を特定して削除しました。この変更は、ブラウザベースのツール、検証にMulmoCastスキーマを使用するWebアプリケーション、MulmoCastデータのクライアントサイド処理など、MulmoCastスキーマをWebブラウザで使用できるようになるため重要です。このブラウザ互換性は、将来のWebベースのインターフェースやツールにとって不可欠です。

### PR #519: Overwrap audio
- **English**: **Major New Feature**: Introduced revolutionary audio spillover support that allows narration to span seamlessly across multiple beats, fundamentally changing how audio and visuals can be synchronized in MulmoCast presentations. This feature enables sophisticated presentation scenarios where a single piece of narration can accompany multiple visual changes. The first beat specifies both the text to be narrated and the duration for visual display, while subsequent beats without text automatically inherit and continue the spillover audio from the previous beat. Each beat displays for its specified duration (with a sensible default of 1 second), and crucially, the final beat remains visible until the entire audio completes, ensuring no audio is cut off. This feature opens up entirely new creative possibilities: educational content where a concept is explained while multiple diagrams appear, storytelling where narration continues as scenes change, or business presentations where data points are revealed while maintaining narrative flow. The implementation required careful coordination between audio processing, visual timing, and state management across the entire pipeline.
- **日本語**: **主要な新機能**: MulmoCastプレゼンテーションにおける音声とビジュアルの同期方法を根本的に変える、複数のビートにまたがってナレーションがシームレスに続く革新的な音声スピルオーバーサポートを導入しました。この機能により、単一のナレーションが複数のビジュアル変化を伴う洗練されたプレゼンテーションシナリオが可能になります。最初のビートでナレーションするテキストとビジュアル表示の時間の両方を指定し、テキストのない後続のビートは前のビートからのスピルオーバー音声を自動的に継承して続行します。各ビートは指定された時間（合理的なデフォルトは1秒）表示され、重要なことに、最後のビートは音声全体が完了するまで表示され続け、音声が途切れることがないことを保証します。この機能により、全く新しい創造的可能性が開かれます：複数の図表が表示される間にコンセプトが説明される教育コンテンツ、シーンが変化する間もナレーションが続くストーリーテリング、ナラティブフローを維持しながらデータポイントが明らかにされるビジネスプレゼンテーションなどです。この実装には、パイプライン全体にわたる音声処理、ビジュアルタイミング、状態管理の間の注意深い協調が必要でした。

### PR #518: getImageRefs
- **English**: Performed strategic code refactoring by extracting the `getImageRefs` function from the larger `prepareGenerateImages` function, significantly improving code modularity and maintainability. This extraction creates a focused, reusable function specifically responsible for gathering and processing image references, making the codebase more organized and easier to test. Additionally, the change includes proper TypeScript type exports for image parameters, substantially improving type safety throughout the image processing pipeline. By having dedicated, well-typed functions for specific tasks, developers can more easily understand, modify, and extend the image handling capabilities. This refactoring is part of a broader pattern of making the codebase more modular and maintainable, where complex operations are broken down into smaller, focused functions that do one thing well. The improved type exports also help catch type-related bugs at compile time rather than runtime.
- **日本語**: より大きな`prepareGenerateImages`関数から`getImageRefs`関数を抽出する戦略的なコードリファクタリングを実行し、コードのモジュラリティと保守性を大幅に向上させました。この抽出により、画像参照の収集と処理に特化した焦点を絞った再利用可能な関数が作成され、コードベースがより整理され、テストしやすくなります。さらに、この変更には画像パラメータの適切なTypeScript型エクスポートが含まれており、画像処理パイプライン全体の型安全性が大幅に向上します。特定のタスクに対して専用の、適切に型付けされた関数を持つことで、開発者は画像処理機能をより簡単に理解、変更、拡張できます。このリファクタリングは、複雑な操作を一つのことを上手に行う小さくて焦点を絞った関数に分解する、コードベースをよりモジュラーで保守しやすくするより広いパターンの一部です。改善された型エクスポートは、実行時ではなくコンパイル時に型関連のバグをキャッチするのにも役立ちます。

## Release Notes – Developer-Focused (English)

MulmoCast CLI v0.0.17 is primarily a maintenance and architecture improvement release that lays the groundwork for future enhancements while introducing one notable new feature for audio handling:

### Major New Feature:
- **Audio Spillover Support**: Revolutionary audio handling that allows narration to span multiple beats. The first beat can specify both text and duration, with subsequent beats without text automatically receiving spillover audio. This enables sophisticated presentation timing where visuals can change while narration continues uninterrupted.

### Architecture & Code Quality:
- **Multilingual Data Separation**: Multilingual translations are now stored separately in `{filename}_lang.json` files, decoupling them from the main MulmoStudio data structure. This improves data organization and maintainability while preserving compatibility across all components.
- **Async Processing Improvements**: Major refactoring of `combineAudioFilesAgent` to use `Promise.all` for parallel processing of audio/movie duration retrieval, significantly improving performance for projects with multiple media files.
- **Modular Code Organization**: Extracted helper functions and utilities across the codebase, including `getMovieDuration`, `getPadding`, `getTotalPadding`, and `getImageRefs`, making the code more maintainable and testable.

### Browser Compatibility:
- **Node.js Dependency Removal**: Fixed critical browser compatibility issue by removing Node.js-specific dependencies from schema imports, eliminating "process is not defined" errors. This enables MulmoCast schemas to be used in browser environments.

### Testing & Reliability:
- **Enhanced Test Error Visibility**: Switched from `safeParse` to `parse` in test environments (NODE_ENV === 'test'), making validation errors throw immediately during testing for better debugging.
- **Context Management**: Improved context handling after audio processing across multiple components, ensuring proper state management throughout the generation pipeline.
- **Test Fixes**: Multiple test-related fixes for `createOrUpdateStudioData` ensuring reliable test execution.

### Path Management:
- **Image Path Refactoring**: Comprehensive refactoring of image path handling with new utility methods in `mulmo_studio_context.ts`, providing cleaner and more consistent path management across the system.

### Dependencies:
- Updated package dependencies to latest versions for security and compatibility.

### Breaking Changes:
- Multilingual data is now stored in separate files (`{filename}_lang.json`), which may affect tools that directly access MulmoStudio data

This release significantly improves code quality and architecture while maintaining backward compatibility for most use cases. The audio spillover feature opens new possibilities for complex presentations with continuous narration.

## リリースノート – 開発者向け (日本語)

MulmoCast CLI v0.0.17は主にメンテナンスとアーキテクチャ改善のリリースで、音声処理の注目すべき新機能を導入しながら、将来の機能強化の基盤を築いています：

### 主要な新機能:
- **音声スピルオーバーサポート**: 複数のビートにまたがるナレーションを可能にする革新的な音声処理。最初のビートでテキストと長さの両方を指定でき、テキストのない後続のビートは自動的にスピルオーバー音声を受け取ります。これにより、ナレーションが中断されることなく続く間にビジュアルが変化する洗練されたプレゼンテーションタイミングが実現されます。

### アーキテクチャ・コード品質:
- **多言語データの分離**: 多言語翻訳が`{filename}_lang.json`ファイルに別々に保存されるようになり、メインのMulmoStudioデータ構造から分離されました。これにより、すべてのコンポーネント間の互換性を維持しながら、データ構成と保守性が向上します。
- **非同期処理の改善**: `combineAudioFilesAgent`の大規模リファクタリングにより、音声/動画の長さ取得に`Promise.all`を使用した並列処理が実装され、複数のメディアファイルを含むプロジェクトのパフォーマンスが大幅に向上しました。
- **モジュラーなコード構成**: `getMovieDuration`、`getPadding`、`getTotalPadding`、`getImageRefs`などのヘルパー関数とユーティリティをコードベース全体で抽出し、コードの保守性とテスト可能性を向上させました。

### ブラウザ互換性:
- **Node.js依存関係の削除**: スキーマインポートからNode.js固有の依存関係を削除し、「process is not defined」エラーを解消する重要なブラウザ互換性の修正を行いました。これにより、ブラウザ環境でMulmoCastスキーマを使用できるようになります。

### テスト・信頼性:
- **テストエラー可視性の向上**: テスト環境（NODE_ENV === 'test'）で`safeParse`から`parse`に切り替え、検証エラーをテスト中に即座にスローすることで、より良いデバッグが可能になりました。
- **コンテキスト管理**: 複数のコンポーネントでの音声処理後のコンテキスト処理を改善し、生成パイプライン全体での適切な状態管理を保証しました。
- **テスト修正**: `createOrUpdateStudioData`に関する複数のテスト関連の修正により、信頼性の高いテスト実行を保証しました。

### パス管理:
- **画像パスのリファクタリング**: `mulmo_studio_context.ts`の新しいユーティリティメソッドによる画像パス処理の包括的なリファクタリングにより、システム全体でよりクリーンで一貫性のあるパス管理を提供します。

### 依存関係:
- セキュリティと互換性のためにパッケージ依存関係を最新バージョンに更新しました。

### 破壊的変更:
- 多言語データが別ファイル（`{filename}_lang.json`）に保存されるようになり、MulmoStudioデータに直接アクセスするツールに影響する可能性があります

このリリースは、ほとんどのユースケースで後方互換性を維持しながら、コード品質とアーキテクチャを大幅に改善します。音声スピルオーバー機能は、継続的なナレーションを伴う複雑なプレゼンテーションの新しい可能性を開きます。

## Release Notes – Creator-Focused (English)

MulmoCast CLI v0.0.17 introduces a powerful new audio feature and significant performance improvements to enhance your creative workflow:

### Revolutionary Audio Feature: Continuous Narration
- **Seamless Audio Flow**: You can now create presentations where narration continues smoothly across multiple scenes! Start your narration in the first beat and let it flow naturally through subsequent beats without text.
- **Perfect Timing Control**: Each beat displays for exactly the duration you specify (default 1 second), while the audio keeps playing. The final beat stays on screen until your narration completes.
- **Creative Freedom**: This opens up entirely new presentation styles - imagine a single narrator explaining a concept while images change to illustrate different points, all without audio interruptions.

### Example Usage:
```json
{
  "beats": [
    {
      "text": "Let me tell you a long story about innovation...",
      "duration": 3,
      "image": { "type": "url", "url": "intro-image.jpg" }
    },
    {
      "duration": 5,
      "image": { "type": "url", "url": "concept-image.jpg" }
    },
    {
      "duration": 4,
      "image": { "type": "url", "url": "conclusion-image.jpg" }
    }
  ]
}
```

### Performance Improvements
- **Faster Processing**: Multiple media files are now processed in parallel, significantly reducing generation time for complex projects.
- **Better Reliability**: Enhanced error handling and state management mean fewer failed generations and smoother workflows.
- **Browser Support**: MulmoCast components can now run in web browsers, opening possibilities for future web-based tools.

### Behind the Scenes
- **Cleaner Data Organization**: Translation data is now stored separately, making projects more organized and easier to manage.
- **Improved Testing**: Better error messages during development help us catch and fix issues faster.
- **Code Quality**: Extensive refactoring ensures MulmoCast remains stable and maintainable as we add new features.

This release focuses on giving you more creative control over audio timing while improving the overall stability and performance of the tool. The new audio spillover feature is particularly powerful for educational content, storytelling, and any presentation where you want visuals to change while maintaining a continuous narrative flow.

## リリースノート – クリエイター向け (日本語)

MulmoCast CLI v0.0.17では、強力な新しい音声機能と大幅なパフォーマンス改善により、クリエイティブワークフローが向上します：

### 革新的な音声機能：継続的なナレーション
- **シームレスな音声フロー**: 複数のシーンにまたがってナレーションがスムーズに継続するプレゼンテーションを作成できるようになりました！最初のビートでナレーションを開始し、テキストのない後続のビートで自然に流れ続けます。
- **完璧なタイミング制御**: 各ビートは指定した長さ（デフォルト1秒）で正確に表示され、音声は再生を続けます。最後のビートはナレーションが完了するまで画面に留まります。
- **創造的な自由**: これにより全く新しいプレゼンテーションスタイルが可能になります - 画像が異なるポイントを説明するために変化する間、単一のナレーターがコンセプトを説明し、すべて音声の中断なしに行えます。

### 使用例:
```json
{
  "beats": [
    {
      "text": "イノベーションについての長い物語をお話しましょう...",
      "duration": 3,
      "image": { "type": "url", "url": "intro-image.jpg" }
    },
    {
      "duration": 5,
      "image": { "type": "url", "url": "concept-image.jpg" }
    },
    {
      "duration": 4,
      "image": { "type": "url", "url": "conclusion-image.jpg" }
    }
  ]
}
```

### パフォーマンス改善
- **高速処理**: 複数のメディアファイルが並列処理されるようになり、複雑なプロジェクトの生成時間が大幅に短縮されました。
- **信頼性の向上**: エラー処理と状態管理の強化により、生成失敗が減少し、ワークフローがスムーズになりました。
- **ブラウザサポート**: MulmoCastコンポーネントがウェブブラウザで実行できるようになり、将来のウェブベースツールの可能性が開かれました。

### 舞台裏での改善
- **クリーンなデータ構成**: 翻訳データが別々に保存されるようになり、プロジェクトがより整理され管理しやすくなりました。
- **改善されたテスト**: 開発中のより良いエラーメッセージにより、問題をより早く発見して修正できます。
- **コード品質**: 広範なリファクタリングにより、新機能を追加してもMulmoCastが安定して保守可能であることを保証します。

このリリースは、ツールの全体的な安定性とパフォーマンスを向上させながら、音声タイミングに対するクリエイティブなコントロールを提供することに焦点を当てています。新しい音声スピルオーバー機能は、教育コンテンツ、ストーリーテリング、継続的なナラティブフローを維持しながらビジュアルを変更したいプレゼンテーションに特に強力です。