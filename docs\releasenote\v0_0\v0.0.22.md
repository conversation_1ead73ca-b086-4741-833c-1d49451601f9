# プロンプト
0.0.22 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.22

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
要約の文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は v0.0.16 を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## 今回のリリースに含まれる Pull Request
## What's Changed
* enable to loop bgm by @asuhacoder in https://github.com/receptron/mulmocast-cli/pull/561
* html prompt by @isamu in https://github.com/receptron/mulmocast-cli/pull/560
* update Image.md by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/563
* update image doc by @isamu in https://github.com/receptron/mulmocast-cli/pull/562

## New Contributors
* @asuhacoder made their first contribution in https://github.com/receptron/mulmocast-cli/pull/561

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.21...0.0.22

--- 以下、Generated by Claude Code --- 
## Pull Request Summaries

### [PR #561: enable to loop bgm](https://github.com/receptron/mulmocast-cli/pull/561)
**Author**: @asuhacoder (First contribution!)

**English Summary**:
This PR fixes an issue where background music (BGM) would stop playing in videos longer than the BGM duration (default BGM is 4 minutes 4 seconds). The fix adds FFmpeg's `-stream_loop -1` option to make BGM loop infinitely. Technical changes include modifying `FfmpegContextAddInput` to accept input options and updating the BGM input handling in `add_bgm_agent.ts`. This ensures continuous background music throughout the entire video, preventing silent BGM sections with only narration.

**日本語要約**:
BGMの長さ（デフォルトBGMは4分4秒）を超える動画でBGMが停止する問題を修正しました。FFmpegの `-stream_loop -1` オプションを追加してBGMを無限にループさせるようにしました。技術的な変更として、`FfmpegContextAddInput`を入力オプションを受け取れるように修正し、`add_bgm_agent.ts`でBGM入力の処理を更新しました。これにより、動画全体を通して継続的なBGMが確保され、ナレーションのみの無音BGMセクションを防ぎます。

### [PR #560: html prompt](https://github.com/receptron/mulmocast-cli/pull/560)
**Author**: @isamu

**English Summary**:
This PR introduces a powerful new feature that generates slide images from HTML prompts using AI. It adds a new `htmlPrompt` field to the MulmoScript schema, which accepts a prompt, optional data, and system instructions. When provided, the system uses OpenAI to generate complete HTML code using Tailwind CSS for styling and Chart.js for data visualization, then converts it to an image. The pipeline includes new GraphAI nodes (`htmlImageAgent` and `htmlImageGenerator`) and integrates seamlessly with the existing caching system. This enables creation of data-rich, visually complex slides that would be difficult with traditional image generation.

**日本語要約**:
AIを使用してHTMLプロンプトからスライド画像を生成する強力な新機能を導入しました。MulmoScriptスキーマに新しい`htmlPrompt`フィールドを追加し、プロンプト、オプションのデータ、システム指示を受け取ります。提供されると、システムはOpenAIを使用してTailwind CSSでスタイリングされ、Chart.jsでデータ視覚化された完全なHTMLコードを生成し、それを画像に変換します。パイプラインには新しいGraphAIノード（`htmlImageAgent`と`htmlImageGenerator`）が含まれ、既存のキャッシングシステムとシームレスに統合されます。これにより、従来の画像生成では困難なデータリッチで視覚的に複雑なスライドの作成が可能になります。

### [PR #563: update Image.md](https://github.com/receptron/mulmocast-cli/pull/563)
**Author**: @ystknsh

**English Summary**:
This PR updates the image generation documentation to correct rule #6 and add htmlPrompt support to the rules table. It changes rule #6 from "conditions 1 or 2" to "conditions 1 or 3" for movie generation with existing images, and adds a new column for htmlPrompt in the beat image generation rules table. A new row for condition 2 (htmlPrompt) was added, explaining that it generates HTML first then converts to image.

**日本語要約**:
画像生成ドキュメントを更新し、ルール#6を修正してルールテーブルにhtmlPromptサポートを追加しました。既存画像を使用した動画生成に関するルール#6を「条件1または2」から「条件1または3」に変更し、ビート画像生成ルールテーブルにhtmlPrompt用の新しい列を追加しました。条件2（htmlPrompt）の新しい行を追加し、最初にHTMLを生成してから画像に変換することを説明しています。

### [PR #562: update image doc](https://github.com/receptron/mulmocast-cli/pull/562)  
**Author**: @isamu

**English Summary**:
This PR comprehensively updates the image documentation to include the new htmlPrompt feature. It adds htmlPrompt as rule #2 in the image generation priority order, shifting other rules down. The documentation now includes htmlPrompt examples showing both data visualization (with birthrate/fertility data) and text-based slides. The rules table was restructured to accommodate the new feature, and section references were renumbered accordingly. This documentation ensures users understand how to leverage HTML generation for complex visualizations.

**日本語要約**:
新しいhtmlPrompt機能を含めるように画像ドキュメントを包括的に更新しました。画像生成優先順位でhtmlPromptをルール#2として追加し、他のルールを下にシフトしました。ドキュメントには、データ視覚化（出生率/妊娠率データ）とテキストベースのスライドの両方を示すhtmlPromptの例が含まれています。ルールテーブルは新機能に対応するように再構築され、セクション参照もそれに応じて番号を付け直しました。このドキュメントにより、ユーザーは複雑な視覚化のためにHTML生成を活用する方法を理解できます。

## Release Notes

### For Developers (English)

**MulmoCast CLI v0.0.22** introduces AI-powered HTML slide generation and resolves a critical BGM looping issue.

**New Features**:
- **HTML Prompt Support**: Generate slides from HTML using AI (PR #560)
  - New `htmlPrompt` field in MulmoScript schema for data-driven visualizations
  - OpenAI generates complete HTML with Tailwind CSS and Chart.js
  - Seamless integration with existing image pipeline and caching system
  - Perfect for complex layouts, charts, and data visualizations

**Bug Fixes**:
- **BGM Looping**: Fixed BGM stopping in videos longer than music duration (PR #561)
  - Added FFmpeg `-stream_loop -1` option for infinite BGM looping
  - Prevents silent sections with only narration

**Documentation**:
- Updated image documentation with htmlPrompt examples and corrected rules (PR #562, #563)
- Added comprehensive usage examples for data visualization

This release significantly expands creative possibilities while ensuring continuous background music in longer videos.

### 開発者向け（日本語）

**MulmoCast CLI v0.0.22** はAIを活用したHTMLスライド生成を導入し、重要なBGMループ問題を解決しました。

**新機能**：
- **HTMLプロンプトサポート**：AIを使用してHTMLからスライドを生成（PR #560）
  - データ駆動型視覚化のためのMulmoScriptスキーマに新しい`htmlPrompt`フィールド
  - OpenAIがTailwind CSSとChart.jsを使用した完全なHTMLを生成
  - 既存の画像パイプラインとキャッシングシステムとのシームレスな統合
  - 複雑なレイアウト、チャート、データ視覚化に最適

**バグ修正**：
- **BGMループ**：音楽の長さを超える動画でBGMが停止する問題を修正（PR #561）
  - 無限BGMループのためのFFmpeg `-stream_loop -1`オプションを追加
  - ナレーションのみの無音セクションを防止

**ドキュメント**：
- htmlPromptの例と修正されたルールで画像ドキュメントを更新（PR #562、#563）
- データ視覚化のための包括的な使用例を追加

このリリースは、長い動画での継続的なBGMを確保しながら、創造的な可能性を大幅に拡張します。

### For Creators (English)

**MulmoCast CLI v0.0.22** brings exciting new creative capabilities and fixes an important audio issue.

**New Creative Tools**:
- **Data-Rich Slides**: Create beautiful charts and data visualizations automatically
  - Simply provide your data and description - AI handles the design
  - Perfect for business presentations, statistics, and infographics
  - No coding knowledge required

**Audio Improvements**:
- **Continuous Background Music**: BGM now loops seamlessly in long videos
  - No more silent background music sections
  - Professional audio experience throughout your entire presentation

**Getting Started**:
- Use `htmlPrompt` in your script to create data visualizations
- Your existing scripts continue to work exactly as before

This update makes it easier to create professional, data-driven presentations while ensuring your videos sound great from start to finish.

### クリエイター向け（日本語）

**MulmoCast CLI v0.0.22** はエキサイティングな新しい創造的機能をもたらし、重要な音声問題を修正しました。

**新しい創作ツール**：
- **データリッチなスライド**：美しいチャートやデータ視覚化を自動的に作成
  - データと説明を提供するだけ - AIがデザインを処理
  - ビジネスプレゼンテーション、統計、インフォグラフィックに最適
  - コーディング知識は不要

**音声の改善**：
- **継続的なBGM**：長い動画でBGMがシームレスにループ
  - BGMの無音セクションがなくなりました
  - プレゼンテーション全体を通してプロフェッショナルな音声体験

**始め方**：
- データ視覚化を作成するにはスクリプトで`htmlPrompt`を使用
- 既存のスクリプトは以前と同じように動作し続けます

このアップデートにより、プロフェッショナルでデータ駆動型のプレゼンテーションを作成しやすくなり、動画が最初から最後まで素晴らしいサウンドになることを保証します。