# リリースノート v1.1.2

## 今回のリリースに含まれる Pull Request

--- 以下、Generated by <PERSON> Code ---

## What's Changed

1. PR #730: add build - @isamu
2. PR #729: update test script to 1.1 - @isamu
3. PR #728: Fix script template schema - @isamu
4. PR #731: More lip sync - @snakajima
5. PR #732: math mystery sample - @snakajima
6. PR #733: Add error message - @kawamataryo
7. PR #735: delete mulmo_script_template methods - @isamu
8. PR #734: skip PromptTemplates validate - @isamu
9. PR #736: Script template from data - @isamu
10. PR #737: Separate template func - @isamu

Full Changelog: https://github.com/receptron/mulmocast-cli/compare/1.1.1...1.1.2

## Pull Request Summaries

### PR #730: add build - @isamu (https://github.com/receptron/mulmocast-cli/pull/730)
- **English**: Added `yarn run template` step to the GitHub Actions build workflow in .github/workflows/build.yml. This ensures that template data files are generated during the CI/CD process before running format and other build steps.
- **日本語**: GitHub Actions buildワークフローの.github/workflows/build.ymlに`yarn run template`ステップを追加しました。これにより、CI/CDプロセス中にformatやその他のビルドステップを実行する前に、テンプレートデータファイルが確実に生成されます。

### PR #729: update test script to 1.1 - @isamu (https://github.com/receptron/mulmocast-cli/pull/729)
- **English**: Updated all test script files in scripts/test/ directory to use schema version 1.1. Changed `$mulmocast.version` from "1.0" to "1.1" in 33 test files, including test.json, test_audio.json, test_replicate.json, and others. Also made minor adjustments to some speaker configurations and audio instructions to align with the new schema version.
- **日本語**: scripts/test/ディレクトリ内のすべてのテストスクリプトファイルをスキーマバージョン1.1を使用するように更新しました。test.json、test_audio.json、test_replicate.jsonなど33のテストファイルで`$mulmocast.version`を"1.0"から"1.1"に変更しました。また、新しいスキーマバージョンに合わせて一部のスピーカー設定と音声指示に軽微な調整を行いました。

### PR #728: Fix script template schema - @isamu (https://github.com/receptron/mulmocast-cli/pull/728)
- **English**: Fixed script template schema references across multiple files by updating type names and schema imports. Changed references from MulmoPresentationStyle to MulmoScript in various utilities, CLI commands, and schema definitions. Updated affected files include schema.ts, type.ts, file.ts, and CLI builders for prompt, scripting, and story_to_script commands. This refactoring ensures consistent schema naming throughout the codebase.
- **日本語**: 複数のファイルにわたってスクリプトテンプレートスキーマの参照を修正し、型名とスキーマインポートを更新しました。様々なユーティリティ、CLIコマンド、スキーマ定義でMulmoPresentationStyleからMulmoScriptへの参照を変更しました。影響を受けたファイルには、schema.ts、type.ts、file.ts、およびprompt、scripting、story_to_scriptコマンドのCLIビルダーが含まれます。このリファクタリングにより、コードベース全体で一貫したスキーマ命名が保証されます。

### PR #731: More lip sync - @snakajima (https://github.com/receptron/mulmocast-cli/pull/731)
- **English**: Enhanced lip sync functionality with additional model support and improved duration handling. Added support for new lip sync models including tmappdev/lipsync and kwaivgi/kling-lip-sync. Implemented beatDuration handling for better synchronization when moviePrompt has no explicit duration. Updated test_lipsync.json with expanded test scenarios and improved image preprocessing agents. Enhanced provider2agent mapping to support the new lip sync models and improved error handling.
- **日本語**: 追加のモデルサポートと改善された継続時間処理により、リップシンク機能を強化しました。tmappdev/lipsyncとkwaivgi/kling-lip-syncを含む新しいリップシンクモデルのサポートを追加しました。moviePromptに明示的な継続時間がない場合のより良い同期のためにbeatDuration処理を実装しました。拡張されたテストシナリオでtest_lipsync.jsonを更新し、画像前処理エージェントを改善しました。新しいリップシンクモデルをサポートし、エラーハンドリングを改善するためにprovider2agentマッピングを強化しました。

### PR #732: math mystery sample - @snakajima (https://github.com/receptron/mulmocast-cli/pull/732)
- **English**: Added new math mystery sample script in scripts/snakajima/math_mystery.json demonstrating educational content creation capabilities. Also made minor updates to test_voices.json for voice configuration testing. The math mystery sample showcases how to create educational presentations with mathematical problem-solving scenarios.
- **日本語**: 教育コンテンツ作成機能を実証する新しい数学ミステリーサンプルスクリプトをscripts/snakajima/math_mystery.jsonに追加しました。また、音声設定テストのためにtest_voices.jsonに軽微な更新を行いました。数学ミステリーサンプルは、数学的問題解決シナリオを含む教育プレゼンテーションの作成方法を紹介しています。

### PR #733: Add error message - @kawamataryo (https://github.com/receptron/mulmocast-cli/pull/733)
- **English**: Improved error message handling in create_mulmo_script_from_url.ts by replacing console.error with GraphAILogger.info for consistent logging. Added proper GraphAI logger import and updated the showErrorMessage function to use the standard logging system instead of direct console output.
- **日本語**: create_mulmo_script_from_url.tsでconsole.errorをGraphAILogger.infoに置き換えて一貫したログ処理のためにエラーメッセージ処理を改善しました。適切なGraphAIロガーのインポートを追加し、直接のコンソール出力の代わりに標準ログシステムを使用するようにshowErrorMessage関数を更新しました。

### PR #735: delete mulmo_script_template methods - @isamu (https://github.com/receptron/mulmocast-cli/pull/735)
- **English**: Removed deprecated mulmo_script_template methods and cleaned up related imports. Deleted methods from src/methods/mulmo_script_template.ts, removed the export from src/methods/index.ts, and updated src/utils/file.ts to remove dependencies on the deprecated functionality. This cleanup simplifies the codebase by removing unused template handling methods.
- **日本語**: 非推奨のmulmo_script_templateメソッドを削除し、関連するインポートをクリーンアップしました。src/methods/mulmo_script_template.tsからメソッドを削除し、src/methods/index.tsからエクスポートを削除し、非推奨機能への依存関係を削除するためにsrc/utils/file.tsを更新しました。このクリーンアップにより、未使用のテンプレート処理メソッドを削除してコードベースが簡素化されます。

### PR #734: skip PromptTemplates validate - @isamu (https://github.com/receptron/mulmocast-cli/pull/734)
- **English**: Modified template validation process to skip prompt template validation and updated script templates data. Changed src/utils/file.ts to bypass prompt template schema validation during processing. Updated src/data/scriptTemplates.ts with regenerated template data reflecting the new validation approach. This optimization improves performance by avoiding unnecessary validation overhead for prompt templates.
- **日本語**: プロンプトテンプレートの検証をスキップし、スクリプトテンプレートデータを更新するようにテンプレート検証プロセスを変更しました。処理中にプロンプトテンプレートスキーマ検証をバイパスするようにsrc/utils/file.tsを変更しました。新しい検証アプローチを反映した再生成されたテンプレートデータでsrc/data/scriptTemplates.tsを更新しました。この最適化により、プロンプトテンプレートの不要な検証オーバーヘッドを避けることでパフォーマンスが向上します。

### PR #736: Script template from data - @isamu (https://github.com/receptron/mulmocast-cli/pull/736)
- **English**: Refactored template system to load script templates from bundled data instead of file system. Updated all template files in assets/templates/ to use schema version 1.1. Modified src/utils/file.ts to use data-driven approach for script template loading. Updated batch/template2tsobject.ts and package.json scripts. Simplified src/data/promptTemplates.ts by removing redundant data. Removed yarn run template step from GitHub Actions build workflow as templates are now bundled.
- **日本語**: ファイルシステムの代わりにバンドル済みデータからスクリプトテンプレートを読み込むようにテンプレートシステムをリファクタリングしました。assets/templates/内のすべてのテンプレートファイルをスキーマバージョン1.1を使用するように更新しました。スクリプトテンプレート読み込みにデータ駆動アプローチを使用するようにsrc/utils/file.tsを変更しました。batch/template2tsobject.tsとpackage.jsonスクリプトを更新しました。冗長なデータを削除してsrc/data/promptTemplates.tsを簡素化しました。テンプレートがバンドル済みになったため、GitHub Actions buildワークフローからyarn run templateステップを削除しました。

### PR #737: Separate template func - @isamu (https://github.com/receptron/mulmocast-cli/pull/737)
- **English**: Extracted template-related functions into a dedicated module for better code organization. Created new src/utils/templates.ts file containing template utility functions moved from src/utils/file.ts and src/utils/prompt.ts. Updated all tools (create_mulmo_script_from_url, create_mulmo_script_interactively, dump_prompt, story_to_script) to import template functions from the new module. Added export to src/index.common.ts for external access. This refactoring improves modularity by separating template handling concerns.
- **日本語**: より良いコード構成のために、テンプレート関連関数を専用モジュールに抽出しました。src/utils/file.tsとsrc/utils/prompt.tsから移動したテンプレートユーティリティ関数を含む新しいsrc/utils/templates.tsファイルを作成しました。すべてのツール（create_mulmo_script_from_url、create_mulmo_script_interactively、dump_prompt、story_to_script）を新しいモジュールからテンプレート関数をインポートするように更新しました。外部アクセス用にsrc/index.common.tsにエクスポートを追加しました。このリファクタリングにより、テンプレート処理の関心事を分離してモジュラリティが向上します。

---

## Release Notes v1.1.2

### Developer Release Notes (English)

**Lip Sync Enhancements**
- **Extended Model Support**: Added support for new lip sync models including tmappdev/lipsync and kwaivgi/kling-lip-sync through Replicate API integration
- **Improved Duration Handling**: Enhanced beatDuration processing for better synchronization when moviePrompt lacks explicit duration

**Template System Refactoring**
- **Data-Driven Architecture**: Refactored template system to load script templates from bundled data instead of file system access
- **Schema Consistency**: Updated all template files in assets/templates/ to use schema version 1.1
- **Module Organization**: Extracted template functions into dedicated src/utils/templates.ts module for better code organization
- **Performance Optimization**: Implemented template validation skipping for prompt templates to reduce processing overhead

**Build and CI/CD Improvements**
- **Automated Template Generation**: Added yarn run template step to GitHub Actions build workflow for consistent template data generation
- **Build Process Optimization**: Removed template generation step from CI workflow after implementing bundled template approach

**Code Quality and Maintenance**
- **Deprecated Code Removal**: Removed obsolete mulmo_script_template methods and cleaned up related imports
- **Schema Standardization**: Fixed script template schema references by updating type names from MulmoPresentationStyle to MulmoScript
- **Logging Improvements**: Enhanced error message handling with GraphAI logger integration
- **Test Updates**: Updated all test scripts in scripts/test/ directory to use schema version 1.1

**Sample Content**
- **Educational Samples**: Added math mystery sample demonstrating educational content creation capabilities

### Developer Release Notes (Japanese)

**リップシンク機能強化**
- **拡張モデルサポート**: Replicate API統合を通じてtmappdev/lipsyncとkwaivgi/kling-lip-syncを含む新しいリップシンクモデルのサポートを追加
- **継続時間処理の改善**: moviePromptに明示的な継続時間がない場合のより良い同期のためにbeatDuration処理を強化

**テンプレートシステムリファクタリング**
- **データ駆動アーキテクチャ**: ファイルシステムアクセスの代わりにバンドル済みデータからスクリプトテンプレートを読み込むようにテンプレートシステムをリファクタリング
- **スキーマ一貫性**: assets/templates/内のすべてのテンプレートファイルをスキーマバージョン1.1を使用するように更新
- **モジュール構成**: より良いコード構成のためにテンプレート関数を専用のsrc/utils/templates.tsモジュールに抽出
- **パフォーマンス最適化**: 処理オーバーヘッドを削減するためにプロンプトテンプレートのテンプレート検証スキップを実装

**ビルドとCI/CD改善**
- **自動テンプレート生成**: 一貫したテンプレートデータ生成のためにGitHub Actions buildワークフローにyarn run templateステップを追加
- **ビルドプロセス最適化**: バンドル済みテンプレートアプローチの実装後にCIワークフローからテンプレート生成ステップを削除

**コード品質とメンテナンス**
- **非推奨コード削除**: 廃止されたmulmo_script_templateメソッドを削除し、関連するインポートをクリーンアップ
- **スキーマ標準化**: MulmoPresentationStyleからMulmoScriptへの型名更新によりスクリプトテンプレートスキーマ参照を修正
- **ログ改善**: GraphAIロガー統合によりエラーメッセージ処理を強化
- **テスト更新**: scripts/test/ディレクトリ内のすべてのテストスクリプトをスキーマバージョン1.1を使用するように更新

**サンプルコンテンツ**
- **教育サンプル**: 教育コンテンツ作成機能を実証する数学ミステリーサンプルを追加

### Creator Release Notes (English)

**Enhanced Lip Sync Features**
- **More Realistic Results**: New lip sync models (tmappdev/lipsync, kwaivgi/kling-lip-sync) provide additional options for creating natural-looking presenter videos
- **Better Audio Sync**: Improved timing handling ensures better synchronization between speech and lip movements

**Improved Performance**
- **Faster Template Access**: Templates now load from bundled data, resulting in quicker template selection and processing
- **Streamlined Processing**: Optimized validation processes reduce processing time for script generation

**Educational Content**
- **Math Mystery Sample**: New sample script demonstrates how to create educational presentations with problem-solving scenarios

**System Stability**
- **Updated Schema**: All templates and test scripts now use consistent schema version 1.1 for improved reliability
- **Better Error Handling**: Enhanced error messages provide clearer feedback during content creation

### Creator Release Notes (Japanese)

**強化されたリップシンク機能**
- **よりリアルな結果**: 新しいリップシンクモデル（tmappdev/lipsync、kwaivgi/kling-lip-sync）により、自然に見えるプレゼンター動画作成のための追加オプションを提供
- **より良い音声同期**: 改善されたタイミング処理により、音声と口の動きの間のより良い同期を保証

**パフォーマンス向上**
- **高速テンプレートアクセス**: テンプレートがバンドル済みデータから読み込まれるようになり、テンプレート選択と処理が高速化
- **処理の合理化**: 最適化された検証プロセスにより、スクリプト生成の処理時間を短縮

**教育コンテンツ**
- **数学ミステリーサンプル**: 問題解決シナリオを含む教育プレゼンテーションの作成方法を実証する新しいサンプルスクリプト

**システム安定性**
- **更新されたスキーマ**: すべてのテンプレートとテストスクリプトが一貫したスキーマバージョン1.1を使用し、信頼性が向上
- **改善されたエラーハンドリング**: 強化されたエラーメッセージにより、コンテンツ作成中により明確なフィードバックを提供

---

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [x] すべてのリンクがGitHubの完全URL（https://github.com/receptron/mulmocast-cli/blob/バージョン/パス）形式になっているか
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-30
チェック者: Claude Code