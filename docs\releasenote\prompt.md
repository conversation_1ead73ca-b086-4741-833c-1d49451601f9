# プロンプト

**バージョン指定**（毎回ここを変更）：
- [VERSION] = v0.1.3
- [PREVIOUS_VERSION] = v0.1.2

---

[VERSION] がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/[VERSION]

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## STEP0 → 対象ファイルを作成してください
docs/releasenote/[VERSION].md ファイルを新規作成し、以下の最小限の内容で開始してください：

```markdown
# リリースノート [VERSION]

## 今回のリリースに含まれる Pull Request
## What's Changed
[ここに人間が手動でPRリストを貼り付ける]

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/[PREVIOUS_VERSION]...[VERSION]

--- 以下、Generated by Claude Code --- 
```

作成後、以下のSTEP1-4を順番に実行してください。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
- 絵文字は使わないでください
- **日本語入力の注意**: 日本語変換ミスや誤字脱字がないよう、特に技術用語は入力後に必ず確認してください
- **事実ベースの記述を徹底してください**：
  - **推測・憶測の禁止**: PRの実際の変更内容のみを記述し、「将来の可能性」や「〜につながる」といった推測的表現は避ける
  - **具体的な変更内容**: ファイル追加、機能実装、設定変更など、実際に行われた変更を具体的に記述
  - **過大解釈の回避**: PRタイトルや説明文から過度に意味を汲み取らず、コード変更の事実に基づいて記述
  - **未来予測の排除**: 「〜の基盤となる」「将来のAI機能」「より洗練された〜」などの表現は使用しない
  - **感情的・誇張表現の禁止**: 「革新的」「画期的」「完璧」「sophisticated」「revolutionary」などの主観的評価を避ける
  - **利益や効果の断定禁止**: 「最適」「理想的」「プロフェッショナル」「エンゲージメント向上」などユーザー体験への断定的表現を避ける
  - **PR本文の確認必須**: PRタイトルだけでなく、PRの説明文とコード変更を必ず確認して記述する
  - **PRコメント内容の活用**: PRのコメント欄に詳細な説明がある場合は、その内容を積極的に活用して具体的で正確な記述を行う

**禁止表現の例**:
- ❌ 「革新的機能」「画期的な改善」「完璧な同期」
- ❌ 「プロフェッショナルな品質」「ユーザー体験の向上」「創造的可能性の拡大」
- ❌ 「将来のAI機能の基盤を築く実験的な実装」
- ❌ 「より知的なワークフロー最適化への重要な一歩」
- ❌ 「sophisticated multimedia production platform」
- ❌ 「enabling professional video editing workflows」

**適切な表現の例**:
- ✅ 「実験的なMCPサーバー実装とJSONスキーマを追加」
- ✅ 「設定管理のための標準化されたインターフェースを提供」
- ✅ 「voice_over画像タイプを追加」
- ✅ 「movieParams.speedパラメータによる速度制御を実装」
- ✅ 「エラーメッセージ内容を更新」
- ✅ 「インタラクティブモードでの欠落したreturn文を修正」

## STEP1 →　 このファイルに追記してください。
**重要**: 
1. **作業開始前の準備**：
   - 必ず [v0.0.17.md](./v0.0.17.md) を読んで、PRサマリーの詳細レベルと文章量を確認
   - このprompt.mdの条件と指示を再読し、確実に従うことを確認
2. **PR調査の必須手順**：
   - PRタイトルだけでなく、**PR本文（説明文）を必ず読む**
   - **PRのコメント欄をチェック**し、作者による詳細説明があれば積極的に活用
   - **コード変更内容も確認**する
   - Taskツールを使う場合は「PRのコメント欄も含めて詳細を確認してください」と明示的に指示
3. **記述前の最終確認**：
   - PR情報を記述する前に、prompt.mdの指示に沿っているかチェック
   - 特に「PRコメント内容の活用」ができているか確認

すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
各PRサマリーは [v0.0.17.md](./v0.0.17.md) の形式に合わせて：

**形式**：
```
### PR #XXX: [PR Title] - @[author] ([PR URL])
- **English**: [英語での説明]
- **日本語**: [日本語での説明]
```

**内容**：
- 英語: 技術的詳細、実装内容を含む100-200語程度（事実ベースで簡潔に）
- 日本語: 英語版と同等の詳細レベルで翻訳
- **実際の変更内容のみ**を記述し、推測や誇張は一切含めない
- WHYとIMPACTは**実際のPR説明に記載されている場合のみ**言及する
- **PRのURLを必ず含める**（例：https://github.com/receptron/mulmocast-cli/pull/XXX）


## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

**記述ルール**:
- **実装された機能のみ**を記述し、利益や効果は推測しない
- 「革新的」「画期的」「プロフェッショナル」などの形容詞は使用禁止
- 「〜に最適」「〜を可能にする」「〜につながる」などの効果説明は避ける
- 技術的事実のみを簡潔に記述する
- **パッケージ更新**: 開発者向けではパッケージ名を記載（例：「Updated GraphAI, Puppeteer, Zod」）、バージョン番号は不要
- **新機能には必ずリンクを追加**：
  - **まずPR本文と変更ファイルを確認**: PRの説明文と一緒に追加・変更されたファイルをチェック
  - **サンプルファイル**: PR内で追加されたテストファイルや、`scripts/test/`、`scripts/samples/`内の関連ファイルを探してリンク
  - **ドキュメント**: PR内で更新されたドキュメントや、`docs/`内の関連セクションにリンク
  - **リンク追加前に必ずファイル内容を確認**し、機能との関連性を検証する
  - 関連ファイルが見つからない場合はリンクなしでも可
- 文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください
- PR の文量が少ないときは、少なくても大丈夫です


## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。

**エンドユーザー視点の原則**：
- **最重要**: 設定方法や記述方法の変更・追加はサンプルリンクと共に紹介
- **判断基準**: ユーザーの作業方法への影響と、スクリプト修正・新機能活用時の実践的な変更点を中心に記載
- **除外対象**: 内部開発者向け情報（リリースノート追加、タイポ修正、リファクタリング等）
  - 開発者はGitHubの「What's Changed」でPR詳細を確認可能
- **優先順位**:
  1. 新機能の使い方（サンプル必須）
  2. 設定・記述方法の変更
  3. 体感できるパフォーマンス改善
  4. 破壊的変更や互換性の注意点

**GitHub向けリリースノート記述ルール**:
- **実装された機能のみ**を客観的に記述
- マーケティング的表現や誇張は一切使用しない
- 「transforms」「enables」「revolutionary」などの変革的表現は禁止
- 技術的事実を簡潔にまとめる
- **パッケージ更新は簡潔に**: 「Updated core dependencies」程度で十分、具体的なバージョン番号の羅列は避ける
- **新機能には必ずリンクを追加**：
  - **まずPR本文と変更ファイルを確認**: 各PRの説明文と一緒に追加・変更されたファイルをチェック
  - サンプルファイル、ドキュメントなどの関連リンクを括弧内に記載
  - リンク形式: `([sample](URL), [docs](URL))` または `([sample](URL))`
  - 必ずファイル内容を確認してから適切なリンクを選択する
- リリースノートの文量、内容は [v0.0.16.md](./v0.0.16.md) を参考にしてください
- PR の文量が少ないときは、少なくても大丈夫です

### セクション分類ガイドライン
リリースノートでは以下の基本構成でセクション分けしてください：

**基本セクション構成**：
- **New Features**: 真の新機能のみ（新しいコマンド、パラメータ、ユーザーが使える機能）
- **Breaking Changes**: 既存APIの変更など、ユーザーのコード修正が必要な変更（該当する場合）
- **Technical Improvements** または **System Improvements**: 内部改善、リファクタリング、パフォーマンス向上、依存関係更新
- **Bug Fixes** または **Error Handling**: バグ修正、エラーハンドリング改善（該当するものがある場合）
- **Samples & Templates**: サンプルスクリプト、テンプレート、使用例の追加
- **Documentation & Maintenance** または **Others**: ドキュメント更新、リリースノート追加、タイポ修正、その他メンテナンス

**特定機能の強化がある場合**（オプション）：
- 大規模な機能追加や強化がある場合は、専用セクションを作成可能
  - 例: "Audio Enhancements", "AI Video Generation", "Content Creation & Import"
- ただし、基本構成を優先し、必要な場合のみ使用

**注意事項**：
- サンプルやテンプレートは「New Features」ではなく「Samples & Templates」に分類
- UI専用スキーマやリファクタリングは「Technical Improvements」に分類
- 真の新機能（ユーザーが実際に使える機能）のみを「New Features」に含める

## STEP4 →　 最終確認と品質チェック（対象ファイルに記録）
全ての作業が完了したら、以下のチェックリストを対象ファイル（[VERSION].md）の最後に追加し、各項目を確認してください：

```markdown
## 品質チェック記録

**PRサマリーの品質確認**：
- [ ] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [ ] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [ ] 実際のコード変更内容を確認したか
- [ ] 推測や誇張表現を避け、事実ベースの記述になっているか
- [ ] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [ ] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [ ] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [ ] すべてのリンクがGitHubの完全URL（https://github.com/receptron/mulmocast-cli/blob/バージョン/パス）形式になっているか
- [ ] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [ ] GitHub向けリリースノートをindex.mdに追加したか
- [ ] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [ ] prompt.mdの全ての条件と指示に従って作業したか
- [ ] 各セクションが適切に分類されているか
- [ ] 日本語の誤字脱字がないか（特に技術用語）
- [ ] 全体的な整合性と一貫性が保たれているか

チェック完了日: [YYYY-MM-DD]
チェック者: Claude Code
```


## 今回のリリースに含まれる Pull Request
## What's Changed
* update release note  by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/631
* update release note by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/632
* add release note v0.1.1 to v0.1.2 and update prompt for release note by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/629
* image generation of reference images by @snakajima in https://github.com/receptron/mulmocast-cli/pull/633
* add types to agents by @isamu in https://github.com/receptron/mulmocast-cli/pull/634
* Settings and env to config by @isamu in https://github.com/receptron/mulmocast-cli/pull/635
* add lint rule by @isamu in https://github.com/receptron/mulmocast-cli/pull/636
* fix fix_shorthand by @isamu in https://github.com/receptron/mulmocast-cli/pull/637
* Fix reassign by @isamu in https://github.com/receptron/mulmocast-cli/pull/638
* add sonarjs by @isamu in https://github.com/receptron/mulmocast-cli/pull/639
* fix no-nested-conditional by @isamu in https://github.com/receptron/mulmocast-cli/pull/640
* fix no-ignored-exceptions by @isamu in https://github.com/receptron/mulmocast-cli/pull/641
* Html template and multi-character template by @snakajima in https://github.com/receptron/mulmocast-cli/pull/628
* fix sonarjs/no-nested-conditional by @isamu in https://github.com/receptron/mulmocast-cli/pull/642
* fix no-shadow by @isamu in https://github.com/receptron/mulmocast-cli/pull/643
* girl and cat script: by @snakajima in https://github.com/receptron/mulmocast-cli/pull/644
* refactor image action by @isamu in https://github.com/receptron/mulmocast-cli/pull/645
* fix: remove noisy logs by @isamu in https://github.com/receptron/mulmocast-cli/pull/646
* a simple movie sample by @snakajima in https://github.com/receptron/mulmocast-cli/pull/648
* Refactor image action2 by @isamu in https://github.com/receptron/mulmocast-cli/pull/647
* Refactor image ref by @isamu in https://github.com/receptron/mulmocast-cli/pull/649
* Image update by @isamu in https://github.com/receptron/mulmocast-cli/pull/650
* update by @isamu in https://github.com/receptron/mulmocast-cli/pull/652
* update public image api by @isamu in https://github.com/receptron/mulmocast-cli/pull/651

+**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/[PREVIOUS_VERSION]...[VERSION]

--- 以下、Generated by Claude Code ---