# リリースノート v1.1.1

## 今回のリリースに含まれる Pull Request

--- 以下、Generated by <PERSON> Code ---

## What's Changed

1. PR #719: flipped title and description - @snakajima
2. PR #720: Lip sync - @snakajima
3. PR #722: audit fix - @isamu
4. PR #721: export validateSchemaAgent - @isamu
5. PR #723: add release note v1.1.0 - @ystknsh
6. PR #724: add getScriptFromTemplate - @isamu
7. PR #726: Template2tsobject - @isamu
8. PR #727: update packages - @isamu

Full Changelog: https://github.com/receptron/mulmocast-cli/compare/1.1.0...1.1.1

## Pull Request Summaries

### PR #719: flipped title and description - @snakajima (https://github.com/receptron/mulmocast-cli/pull/719)
- **English**: Fixed incorrect swapping of title and description fields between English and Japanese Ani templates. The English template (ani.json) previously had Japanese-specific metadata while the Japanese template (ani_ja.json) had generic English metadata. This correction ensures proper template identification: ani.json now correctly shows "Presentation with Ani" and ani_ja.json shows "Presentation with Ani in Japanese". This fix improves template selection clarity for users working with different language versions.
- **日本語**: 英語と日本語のAniテンプレート間でタイトルと説明フィールドが誤って入れ替わっていた問題を修正しました。英語テンプレート（ani.json）には日本語固有のメタデータが、日本語テンプレート（ani_ja.json）には汎用的な英語のメタデータが設定されていました。この修正により適切なテンプレート識別が保証されます：ani.jsonは正しく「Presentation with Ani」を表示し、ani_ja.jsonは「Presentation with Ani in Japanese」を表示します。この修正により、異なる言語バージョンで作業するユーザーのテンプレート選択の明確さが向上します。

### PR #720: Lip sync - @snakajima (https://github.com/receptron/mulmocast-cli/pull/720)
- **English**: Implemented lip sync functionality that synchronizes video generated from moviePrompt with audio from text. Added new lipSyncReplicateAgent that uses Replicate API models (bytedance/latentsync or tmappdev/lipsync) to process videos. Introduced enableLipSync boolean flag and lipSyncParams configuration at both presentation and beat levels. The feature works by taking generated movie and audio files, processing them through the selected lip sync model, and replacing the original video with synchronized version. Added lipSyncFile to MulmoStudioBeat for storing processed video paths. The implementation includes caching support and proper file handling with prioritized selection in final movie assembly.
- **日本語**: moviePromptから生成されたビデオとテキストからの音声を同期させるリップシンク機能を実装しました。Replicate APIモデル（bytedance/latentsyncまたはtmappdev/lipsync）を使用してビデオを処理する新しいlipSyncReplicateAgentを追加しました。プレゼンテーションレベルとビートレベルの両方でenableLipSyncブール値フラグとlipSyncParams設定を導入しました。この機能は、生成された動画と音声ファイルを取得し、選択されたリップシンクモデルで処理し、元のビデオを同期されたバージョンに置き換えることで動作します。処理されたビデオパスを保存するためのlipSyncFileをMulmoStudioBeatに追加しました。実装にはキャッシュサポートと、最終的な動画アセンブリでの優先順位付けされた選択を含む適切なファイル処理が含まれています。

### PR #722: audit fix - @isamu (https://github.com/receptron/mulmocast-cli/pull/722)
- **English**: Updated yarn.lock file to resolve security vulnerabilities detected by yarn audit. This update addresses dependency security issues without modifying package.json, ensuring the project maintains secure versions of transitive dependencies.
- **日本語**: yarn auditで検出されたセキュリティ脆弱性を解決するためにyarn.lockファイルを更新しました。この更新は、package.jsonを変更することなく依存関係のセキュリティ問題に対処し、プロジェクトが推移的依存関係の安全なバージョンを維持することを保証します。

### PR #721: export validateSchemaAgent - @isamu (https://github.com/receptron/mulmocast-cli/pull/721)
- **English**: Fixed the export of validateSchemaAgent in index.browser.ts by changing from wildcard export to named export. This ensures proper module resolution and availability of the validateSchemaAgent when using MulmoCast in browser environments.
- **日本語**: index.browser.tsでvalidateSchemaAgentのエクスポートをワイルドカードエクスポートから名前付きエクスポートに変更して修正しました。これにより、ブラウザ環境でMulmoCastを使用する際のvalidateSchemaAgentの適切なモジュール解決と利用可能性が保証されます。

### PR #723: add release note v1.1.0 - @ystknsh (https://github.com/receptron/mulmocast-cli/pull/723)
- **English**: Added release notes documentation for version 1.1.0. Created v1.1.0.md file containing detailed PR summaries and release notes in multiple formats, and updated index.md with GitHub-oriented release notes for the v1.1.0 release.
- **日本語**: バージョン1.1.0のリリースノートドキュメントを追加しました。詳細なPRサマリーと複数形式のリリースノートを含むv1.1.0.mdファイルを作成し、v1.1.0リリースのGitHub向けリリースノートでindex.mdを更新しました。

### PR #724: add getScriptFromTemplate - @isamu (https://github.com/receptron/mulmocast-cli/pull/724)
- **English**: Added getScriptFromPromptTemplate function and refactored template-related code for better clarity. Renamed TemplateFile type to ScriptTemplate, and all template-related functions to use "promptTemplate" naming convention. The new getScriptFromPromptTemplate function extracts MulmoScript data from prompt templates, supporting the scriptName field. Also added getAvailablePromptTemplates as an alias for getAvailableTemplates. These changes improve code organization and make the distinction between prompt templates and script templates clearer.
- **日本語**: getScriptFromPromptTemplate関数を追加し、テンプレート関連のコードをより明確にするためにリファクタリングしました。TemplateFile型をScriptTemplateに変更し、すべてのテンプレート関連関数を「promptTemplate」命名規則を使用するように変更しました。新しいgetScriptFromPromptTemplate関数は、プロンプトテンプレートからMulmoScriptデータを抽出し、scriptNameフィールドをサポートします。また、getAvailableTemplatesのエイリアスとしてgetAvailablePromptTemplatesを追加しました。これらの変更により、コードの構成が改善され、プロンプトテンプレートとスクリプトテンプレートの区別がより明確になります。

### PR #726: Template2tsobject - @isamu (https://github.com/receptron/mulmocast-cli/pull/726)
- **English**: Added template2tsobject batch script to generate TypeScript data files from JSON templates. Created promptTemplates.ts and scriptTemplates.ts in src/data/ containing all template data as TypeScript exports. Updated all script template JSON files to version 1.1. Added getAvailableScriptTemplates function and removed ScriptTemplate type definition. This change allows bundling template data directly into the compiled code, improving performance and reducing file system dependencies.
- **日本語**: JSONテンプレートからTypeScriptデータファイルを生成するtemplate2tsobjectバッチスクリプトを追加しました。src/data/にすべてのテンプレートデータをTypeScriptエクスポートとして含むpromptTemplates.tsとscriptTemplates.tsを作成しました。すべてのスクリプトテンプレートJSONファイルをバージョン1.1に更新しました。getAvailableScriptTemplates関数を追加し、ScriptTemplate型定義を削除しました。この変更により、テンプレートデータをコンパイル済みコードに直接バンドルでき、パフォーマンスが向上し、ファイルシステムの依存関係が削減されます。

### PR #727: update packages - @isamu (https://github.com/receptron/mulmocast-cli/pull/727)
- **English**: Updated project dependencies by modifying package.json and yarn.lock files. This routine maintenance ensures the project uses the latest compatible versions of dependencies, incorporating bug fixes, security patches, and performance improvements from upstream packages.
- **日本語**: package.jsonとyarn.lockファイルを変更してプロジェクトの依存関係を更新しました。この定期的なメンテナンスにより、プロジェクトが依存関係の最新の互換バージョンを使用し、上流パッケージからのバグ修正、セキュリティパッチ、パフォーマンス改善を組み込むことが保証されます。

---

## Release Notes v1.1.1

### Developer Release Notes (English)

**New Features**
- **Lip Sync Support**: Added comprehensive lip sync functionality that synchronizes video content with audio narration. Features new lipSyncReplicateAgent supporting multiple models (bytedance/latentsync, tmappdev/lipsync) through Replicate API. Includes enableLipSync flag and lipSyncParams configuration at both presentation and beat levels.

**API Improvements**
- **Template System Enhancement**: Added getScriptFromPromptTemplate function and improved template handling with clear separation between prompt templates and script templates. Enhanced type definitions and naming conventions for better code organization.
- **Browser Compatibility**: Fixed validateSchemaAgent export in browser builds to ensure proper module resolution in browser environments.

**Technical Improvements**
- **Template Data Bundling**: Added template2tsobject batch script that generates TypeScript data files from JSON templates, allowing template data to be bundled directly into compiled code for improved performance.
- **Dependencies**: Updated core packages and resolved security vulnerabilities through yarn audit fixes.

**Templates & Samples**
- **Template Fixes**: Corrected title and description metadata in English and Japanese Ani templates.
- **Version Updates**: Updated all script template JSON files to version 1.1.

**Documentation & Maintenance**
- Added release notes for version 1.1.0.

### Developer Release Notes (Japanese)

**新機能**
- **リップシンクサポート**: ビデオコンテンツと音声ナレーションを同期する包括的なリップシンク機能を追加しました。Replicate APIを通じて複数のモデル（bytedance/latentsync、tmappdev/lipsync）をサポートする新しいlipSyncReplicateAgentを搭載。プレゼンテーションレベルとビートレベルの両方でenableLipSyncフラグとlipSyncParams設定が含まれます。

**API改善**
- **テンプレートシステム強化**: getScriptFromPromptTemplate関数を追加し、プロンプトテンプレートとスクリプトテンプレートの明確な分離によりテンプレート処理を改善しました。より良いコード構成のために型定義と命名規則を強化しました。
- **ブラウザ互換性**: ブラウザビルドでvalidateSchemaAgentのエクスポートを修正し、ブラウザ環境での適切なモジュール解決を保証しました。

**技術的改善**
- **テンプレートデータバンドル**: JSONテンプレートからTypeScriptデータファイルを生成するtemplate2tsobjectバッチスクリプトを追加し、テンプレートデータをコンパイル済みコードに直接バンドルしてパフォーマンスを向上させました。
- **依存関係**: コアパッケージを更新し、yarn auditによるセキュリティ脆弱性を解決しました。

**テンプレートとサンプル**
- **テンプレート修正**: 英語と日本語のAniテンプレートでタイトルと説明のメタデータを修正しました。
- **バージョン更新**: すべてのスクリプトテンプレートJSONファイルをバージョン1.1に更新しました。

**ドキュメントとメンテナンス**
- バージョン1.1.0のリリースノートを追加しました。

### Creator Release Notes (English)

**New Creative Features**
- **Realistic Lip Sync**: Create more engaging presenter videos with automatic lip synchronization. Simply add `enableLipSync: true` to any beat with both `text` and `moviePrompt` fields. The system will automatically match lip movements to your spoken content, making your presentations look more natural and engaging.

**Template Improvements**
- **Fixed Template Selection**: Corrected the Ani template descriptions so you can now properly distinguish between the English and Japanese versions when selecting templates.

**Enhanced Performance**
- **Faster Template Loading**: Template data is now bundled into the application, resulting in faster startup times and reduced file system dependencies.

**Updated Dependencies**
- **Security & Stability**: Updated core dependencies for improved security and stability of your content generation workflows.

### Creator Release Notes (Japanese)

**新しいクリエイティブ機能**
- **リアルなリップシンク**: 自動リップシンクロ機能により、より魅力的なプレゼンター動画を作成できます。`text`と`moviePrompt`の両方のフィールドを持つビートに`enableLipSync: true`を追加するだけで、システムが自動的に口の動きを話している内容に合わせ、プレゼンテーションをより自然で魅力的に見せます。

**テンプレートの改善**
- **テンプレート選択の修正**: Aniテンプレートの説明を修正し、テンプレート選択時に英語版と日本語版を適切に区別できるようになりました。

**パフォーマンス向上**
- **高速テンプレート読み込み**: テンプレートデータがアプリケーションにバンドルされ、起動時間の短縮とファイルシステム依存関係の削減が実現されました。

**依存関係の更新**
- **セキュリティと安定性**: コンテンツ生成ワークフローのセキュリティと安定性向上のため、コア依存関係を更新しました。

---

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [x] すべてのリンクがGitHubの完全URL（https://github.com/receptron/mulmocast-cli/blob/バージョン/パス）形式になっているか
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-30
チェック者: Claude Code