export const templateDataSet = {
  a<PERSON><PERSON>_comic:
    "Generate a script for a presentation of the given topic. Another AI will generate images for each beat based on the image prompt of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>AKIRA aesthetic.</style>","images":{"girl":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/akira_presenter.png"}}}}}\n` +
    "```",
  ani:
    "Generate a script for a presentation of the given topic. 言葉づかいは少しツンデレにして。Another AI will generate comic for each beat based on the image prompt of that beat. You don't need to specify the style of the image, just describe the scene. Mention the reference in one of beats, if it exists. Use the JSON below as a template. Create appropriate amount of beats, and make sure the beats are coherent and flow well.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"movieParams":{"provider":"replicate","model":"bytedance/seedance-1-lite"},"speechParams":{"provider":"openai","speakers":{"Presenter":{"voiceId":"shimmer","speechOptions":{"instruction":"Speak in a slightly high-pitched, curt tone with sudden flustered shifts—like a tsundere anime girl."}}}},"audioParams":{"bgm":{"kind":"url","url":"https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/morning001.mp3"}},"canvasSize":{"width":1024,"height":1536},"imageParams":{"style":"<style>A highly polished 2D digital illustration in anime and manga style, featuring clean linework, soft shading, vivid colors, and expressive facial detailing. The composition emphasizes clarity and visual impact with a minimalistic background and a strong character focus. The lighting is even and bright, giving the image a crisp and energetic feel, reminiscent of high-quality character art used in Japanese visual novels or mobile games.</style>","images":{"ani":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ani.png"}}}}}\n` +
    "```",
  ani_ja:
    "Generate a Japanese script for a presentation of the given topic. 言葉づかいは少しツンデレにして。Another AI will generate comic for each beat based on the image prompt of that beat. You don't need to specify the style of the image, just describe the scene. Mention the reference in one of beats, if it exists. Use the JSON below as a template. Create appropriate amount of beats, and make sure the beats are coherent and flow well.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"ja","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"movieParams":{"provider":"replicate","model":"bytedance/seedance-1-lite"},"audioParams":{"bgm":{"kind":"url","url":"https://github.com/receptron/mulmocast-media/raw/refs/heads/main/bgms/morning001.mp3"}},"canvasSize":{"width":1024,"height":1536},"speechParams":{"speakers":{"Presenter":{"provider":"nijivoice","voiceId":"9d9ed276-49ee-443a-bc19-26e6136d05f0"}}},"imageParams":{"style":"<style>A highly polished 2D digital illustration in anime and manga style, featuring clean linework, soft shading, vivid colors, and expressive facial detailing. The composition emphasizes clarity and visual impact with a minimalistic background and a strong character focus. The lighting is even and bright, giving the image a crisp and energetic feel, reminiscent of high-quality character art used in Japanese visual novels or mobile games.</style>","images":{"ani":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ani.png"}}}}}\n` +
    "```",
  business:
    "Generate a script for a business presentation of the given topic. Use textSlides, markdown, mermaid, or chart to show slides. Extract image links in the article (from <img> tag) to reuse them in the presentation. Mention the reference in one of beats, if it exists. Use the JSON below as a template. chartData is the data for Chart.js\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"Sample Title","references":[{"url":"https://www.somegreatwebsite.com/article/123","title":"Title of the article we are referencing","type":"article"}],"lang":"en","beats":[{"text":"Today we're exploring a fascinating concept that has shaped some of the most innovative companies and leaders of our time: the Reality Distortion Field.","image":{"type":"textSlide","slide":{"title":"This is the title of the presentation"}}},{"text":"This is a sample slide, which just displays the title and the presenter's name of this presentation.","image":{"type":"textSlide","slide":{"title":"This is the title of the presentation","subtitle":"Tom Johnson"}}},{"text":"The evolution of humans is a complex journey that spans millions of years, shaped by biology, environment, and culture. Here's a high-level summary of the key stages in human evolution","image":{"type":"textSlide","slide":{"title":"Human Evolution","bullets":["Early Primates","Hominids and Hominins","Australopithecus","Genus Homo Emerges","Homo erectus and Migration","Neanderthals and Other Archaic Humans","Homo sapiens"]}}},{"text":"This is a table of items in the store.","image":{"type":"markdown","markdown":["# Markdown Table Example","| Item              | In Stock | Price |","| :---------------- | :------: | ----: |","| Python Hat        |   True   | 23.99 |","| SQL Hat           |   True   | 23.99 |","| Codecademy Tee    |  False   | 19.99 |","| Codecademy Hoodie |  False   | 42.99 |"]}},{"text":"This page shows the sales and profits of this company from January 2024 to June 2024.","image":{"type":"chart","title":"Sales and Profits (from Jan to June)","chartData":{"type":"bar","data":{"labels":["January","February","March","April","May","June"],"datasets":[{"label":"Revenue ($1000s)","data":[120,135,180,155,170,190],"backgroundColor":"rgba(54, 162, 235, 0.5)","borderColor":"rgba(54, 162, 235, 1)","borderWidth":1},{"label":"Profit ($1000s)","data":[45,52,68,53,61,73],"backgroundColor":"rgba(75, 192, 192, 0.5)","borderColor":"rgba(75, 192, 192, 1)","borderWidth":1}]},"options":{"responsive":true,"animation":false}}}},{"text":"This is a sample pie chart","image":{"type":"chart","title":"A sample pie chart","chartData":{"type":"pie","data":{"labels":["OpenAIと投資家の取り分","マイクロソフトの取り分"],"datasets":[{"data":[90,10],"backgroundColor":["rgba(75, 192, 192, 0.5)","rgba(54, 162, 235, 0.5)"],"borderColor":["rgba(75, 192, 192, 1)","rgba(54, 162, 235, 1)"],"borderWidth":1}]},"options":{"responsive":true,"animation":false,"plugins":{"legend":{"position":"bottom"}}}}}},{"text":"Next, let's look at a diagram of our business process flow. This illustrates the key steps from product development to sales.","image":{"type":"mermaid","title":"Business Process Flow","code":{"kind":"text","text":"graph LR\\n    A[Market Research] --> B[Product Planning]\\n    B --> C[Development]\\n    C --> D[Testing]\\n    D --> E[Manufacturing]\\n    E --> F[Marketing]\\n    F --> G[Sales]\\n    G --> H[Customer Support]\\n    H --> A"}}},{"text":"This is a tailwind html format.","image":{"type":"html_tailwind","html":["<main class=\\"flex-grow\\">","  <!-- Hero Section -->","  <section class=\\"bg-blue-600 text-white py-20\\">","    <div class=\\"container mx-auto px-6 text-center\\">","      <h1 class=\\"text-4xl md:text-5xl font-bold mb-4\\">Welcome to Mulmocast</h1>","      <p class=\\"text-lg md:text-xl mb-8\\">A modern web experience powered by Tailwind CSS</p>","      <a href=\\"#features\\" class=\\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold shadow hover:bg-gray-100 transition\\">","        Learn More","      </a>","    </div>","  </section>","","  <!-- Features Section -->","  <section id=\\"features\\" class=\\"py-16 bg-gray-100\\">","    <div class=\\"container mx-auto px-6\\">","      <div class=\\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\\">","        <div>","          <div class=\\"text-blue-600 text-4xl mb-2\\">⚡</div>","          <h3 class=\\"text-xl font-semibold mb-2\\">Fast</h3>","          <p class=\\"text-gray-600\\">Built with performance in mind using modern tools.</p>","        </div>","        <div>","          <div class=\\"text-blue-600 text-4xl mb-2\\">🎨</div>","          <h3 class=\\"text-xl font-semibold mb-2\\">Beautiful</h3>","          <p class=\\"text-gray-600\\">Styled with Tailwind CSS for clean, responsive design.</p>","        </div>","        <div>","          <div class=\\"text-blue-600 text-4xl mb-2\\">🚀</div>","          <h3 class=\\"text-xl font-semibold mb-2\\">Launch Ready</h3>","          <p class=\\"text-gray-600\\">Easy to deploy and extend for your next big idea.</p>","        </div>","      </div>","    </div>","  </section>","</main>","","<!-- Footer -->","<footer class=\\"bg-white text-gray-500 text-center py-6 border-t\\">","  2025 Mulmocast.","</footer>"]}},{"text":"This is the image of the future of enterprise applications.","image":{"type":"image","source":{"kind":"url","url":"https://satoshi.blogs.com/mag2/May2025/enterprise_app.png"}}}]}\n` +
    "```",
  characters:
    "Generate a script for a the given story with multiple characters. Generate image prompts for each character, and make references to them in the beats. Use the JSON below as a template.\n" +
    "```JSON\n" +
    '{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE OF THE PRESENTAITON OR STORY]","imageParams":{"images":{"[CHARACTER_ID_1]":{"type":"imagePrompt","prompt":"[IMAGE PROMPT FOR THIS CHARACTER]"},"[CHARACTER_ID_2]":{"type":"imagePrompt","prompt":"[IMAGE PROMPT FOR THIS CHARACTER]"}}},"beats":[{"text":"[NARRATION FOR THIS BEAT]","imagePrompt":"[IMAGE PROMPT FOR THIS BEAT (with both characters)]","imageNames":["[CHARACTER_ID_1]","[CHARACTER_ID_2]"]},{"text":"[NARRATION FOR THIS BEAT]","imagePrompt":"[IMAGE PROMPT FOR THIS BEAT (only character 1)]","imageNames":["[CHARACTER_ID_1]"]},{"text":"[NARRATION FOR THIS BEAT]","imagePrompt":"[IMAGE PROMPT FOR THIS BEAT (no character)]","imageNames":[]}],"canvasSize":{"width":1536,"height":1024}}\n' +
    "```",
  children_book:
    "Please generate a script for a children book on the topic provided by the user. Each page (=beat) must haven an image prompt appropriate for the text.\n" +
    "```JSON\n" +
    '{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"桃太郎","lang":"ja","beats":[{"text":"むかしむかし、あるところにおじいさんとおばあさんが住んでいました。おじいさんは山へ芝刈りに、おばあさんは川へ洗濯に行きました。","imagePrompt":"藁葺き屋根の古い日本家屋。近くには清らかな川が流れ、裏には緑豊かな山がある。おじいさんは鎌を持って山へ向かい、おばあさんは洗濯かごを持って川へ向かっている。春の穏やかな日差しが風景を照らしている。"},{"text":"おばあさんが川で洗濯をしていると、上流から大きな桃が流れてきました。「まあ、なんて大きな桃でしょう」とおばあさんは驚きました。","imagePrompt":"川で洗濯するおばあさん。川面に映る青空と白い雲。上流から流れてくる異様に大きくて鮮やかな赤い桃。驚いた表情でそれを見つめるおばあさん。周りには洗濯物と石。"},{"text":"おばあさんはその桃を持ち帰り、「おじいさん、大きな桃を見つけましたよ」と言いました。二人が桃を切ろうとすると、中から元気な男の子が生まれました。","imagePrompt":"家の中、赤ん坊を高く抱き上げて、驚きと喜びの表情を浮かべる老夫婦。"},{"text":"二人は男の子を「桃太郎」と名付けて、大切に育てました。桃太郎はすくすくと成長し、とても強い子になりました。","imagePrompt":"時間の経過を示す4コマの連続画像。最初は赤ちゃん、次に幼児、そして少年、最後に若い男性へと成長する桃太郎。各段階でおじいさんとおばあさんが愛情深く見守っている。最後の画像では、たくましく成長した桃太郎が木を持ち上げたり、重い石を運んだりして力の強さを示している。"},{"text":"ある日、鬼が島から来た鬼たちが村を荒らしているという話を聞いた桃太郎は、おじいさんとおばあさんに「鬼退治に行きます」と告げました。","imagePrompt":"家の中の桃太郎、おじいさんとおばあさん。窓の外では村人たちが恐怖の表情で逃げ回り、遠くには炎と煙が見える。決意に満ちた表情の桃太郎が立ち上がり、おじいさんとおばあさんに語りかけている。憂慮と誇りの入り混じった表情の老夫婦。"},{"text":"おばあさんは桃太郎のために、日本一のきびだんごを作ってくれました。おじいさんは立派な刀と着物をくれました。","imagePrompt":"家の中。おばあさんが台所できびだんごを作り、おじいさんが桐箱から刀と鮮やかな着物を取り出している。準備を整える桃太郎。テーブルの上には小さな布包みにきびだんごが包まれている。朝日が障子を通して部屋を温かく照らしている。"},{"text":"「いってきます」と言って、桃太郎はきびだんごを持って、鬼が島へ向かいました。","imagePrompt":"家の前で出発する桃太郎。腰にはきびだんごの入った袋と刀、背中には小さな旗。見送るおじいさんとおばあさん、そして村人たち。桃太郎は自信に満ちた表情で前方を見つめている。朝霧の中、道は山々へと続いている。"},{"text":"道中、桃太郎は犬に出会いました。「桃太郎さん、桃太郎さん、お腰につけたきびだんご、一つわたしに下さいな」と犬は言いました。","imagePrompt":"山道を進む桃太郎。横には大きな茶色の犬が立っている。犬は尾を振り、期待を込めた表情で桃太郎を見上げている。周りには春の花と緑豊かな自然。桃太郎は犬に微笑みかけている。"},{"text":"「よし、一つあげよう。その代わり家来になるんだよ」と桃太郎は言いました。犬は喜んできびだんごを食べ、桃太郎の家来になりました。","imagePrompt":"桃太郎がきびだんごを犬に渡している様子。犬が嬉しそうにきびだんごを食べている。桃太郎の表情は優しく頼もしい。背景には山と川、遠くには鬼が島を思わせる遠景。"},{"text":"次に、桃太郎と犬は猿に出会いました。猿もきびだんごと引き換えに、桃太郎の家来になりました。","imagePrompt":"森の中の道。桃太郎と犬が木にとまる猿と話している。猿は好奇心いっぱいの表情で桃太郎の手にあるきびだんごを見ている。周りには色とりどりの木々と花。犬は猿を友好的に見上げている。"},{"text":"さらに進むと、今度はキジに出会いました。キジもきびだんごをもらい、桃太郎の家来になりました。","imagePrompt":"山の開けた場所。空高く舞うカラフルなキジが桃太郎たちに近づいてきている。地面には桃太郎、犬、猿が立っており、空を見上げている。キジは美しい羽を広げ、桃太郎のきびだんごに目を向けている。背景には雄大な山々と澄んだ青空。"},{"text":"こうして桃太郎は、犬、猿、キジを家来にして、いよいよ鬼が島へと向かいました。","imagePrompt":"海に浮かぶ鬼が島に向かう小さな船。船の上には桃太郎、犬、猿、キジが乗っている。桃太郎は立って指揮を取り、犬は船の前方を見据え、猿は帆を操作し、キジは空から見張りをしている。荒々しい波と暗雲が立ち込める中、島へと近づく彼らの姿。島には険しい岩山と不気味な城が見える。"},{"text":"鬼が島に着くと、そこには大きな門がありました。キジが飛んで様子を見ると、中では鬼たちが宴会をしていました。","imagePrompt":"鬼ヶ島の大きな赤い門。門の上空を飛ぶキジ。門の向こう側では、様々な色の鬼たちが酒を飲み、踊り、騒いでいる様子が見える。鬼の中には角が1本、2本、3本のものなど様々。宴会場の周りには盗んできた宝物が山積みになっている。門の手前には桃太郎、犬、猿が隠れて様子をうかがっている。"},{"text":"「よーし、みんな準備はいいか。今から鬼退治だ！」と桃太郎は言いました。","imagePrompt":"鬼ヶ島の入り口近く、岩陰に隠れた桃太郎と家来たち。桃太郎は刀を抜き、決意に満ちた表情で仲間たちに語りかけている。犬は牙をむき、猿は棒を構え、キジは鋭い嘴を見せて戦う準備をしている。全員が真剣な表情で桃太郎の言葉に耳を傾けている。背景には鬼の城が不気味にそびえ立っている。"},{"text":"桃太郎たちは勇敢に戦いました。犬は鬼の足に噛みつき、猿は鬼の髪を引っ張り、キジは鬼の目をつついて攻撃しました。","imagePrompt":"鬼ヶ島の城の中での激しい戦闘シーン。様々な色の鬼たちが驚きと怒りの表情で戦っている。犬は赤鬼の足に噛みついて倒し、猿は青鬼の髪を引っ張って混乱させ、キジは緑鬼の目をつついている。中央では桃太郎が刀を振るい、黄色い鬼と対峙している。背景には他の鬼たちも逃げ惑う姿がある。戦いの熱気と混乱が画面いっぱいに広がっている。"},{"text":"そして桃太郎は鬼の大将に向かって行きました。激しい戦いの末、桃太郎は鬼の大将を倒しました。","imagePrompt":"城の奥、豪華な部屋での桃太郎と鬼の大将との一騎打ち。鬼の大将は巨大で、赤い肌に金色の兜と鉄の棍棒を持っている。桃太郎は小さいながらも勇敢に刀を構えて対峙している。部屋の周りには宝物が散らばり、窓からは戦いを見守る家来たちの姿が見える。決定的な一撃を加えようとする桃太郎と、驚きの表情を浮かべる鬼の大将。"},{"text":"「もう悪いことはしません。命だけはお助けください」と鬼たちは降参しました。そして村から盗んだ宝物をすべて差し出しました。","imagePrompt":"床に頭を下げて土下座する鬼の大将と鬼たち。勝利した桃太郎が堂々と立ち、家来たちがその横に誇らしげに並んでいる。鬼たちの前には金銀財宝、布、米俵など盗んだ宝物が山と積まれている。鬼たちは恐れと後悔の表情を浮かべている。桃太郎の表情は厳しいながらも慈悲深さを感じさせる。"},{"text":"桃太郎と家来たちは宝物を持って村に帰りました。村人たちは大喜びで彼らを迎えました。","imagePrompt":"村に凱旋する桃太郎と家来たち。宝物を運ぶ犬、猿、キジ。桃太郎は誇らしげに村人たちに手を振っている。老若男女の村人たちが道の両側に集まり、喜びの表情で花や旗を振って迎えている。おじいさんとおばあさんも最前列で涙を流しながら桃太郎の帰りを待っている。春の明るい日差しが村全体を照らしている。"},{"text":"おじいさんとおばあさんは桃太郎の無事な帰りを喜び、抱きしめました。そして村はもう二度と鬼に襲われることはありませんでした。","imagePrompt":"家の前でおじいさんとおばあさんが桃太郎を抱きしめている感動的な場面。喜びの涙を流すおじいさんとおばあさん。犬、猿、キジも幸せそうに見守っている。周りには村人たちが集まり、祝福している。家の前には宝物の一部が置かれ、背景には平和な村の風景が広がっている。夕日が温かな光を投げかけ、晴れやかな雰囲気を作り出している。"},{"text":"こうして桃太郎とおじいさんとおばあさん、そして家来たちは幸せに暮らしました。めでたし、めでたし。","imagePrompt":"時が経ち、平和になった村の風景。桃太郎の家では、おじいさんとおばあさんが縁側でお茶を飲んでいる。庭では成長した桃太郎が犬、猿、キジと一緒に楽しそうに過ごしている。背景には実りある田んぼと平和な村の様子。桃の木が花を咲かせ、その下で皆が笑顔で暮らしている。夕暮れの優しい光が全体を包み込み、物語の幸せな結末を象徴している。"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"A hand-drawn style illustration with a warm, nostalgic atmosphere. The background is rich with natural scenery—lush forests, cloudy skies, and traditional Japanese architecture. Characters have expressive eyes, soft facial features, and are portrayed with gentle lighting and subtle shading. The color palette is muted yet vivid, using earthy tones and watercolor-like textures. The overall scene feels magical and peaceful, with a sense of quiet wonder and emotional depth, reminiscent of classic 1980s and 1990s Japanese animation."}}\n' +
    "```",
  coding:
    "Generate a script for a technical presentation of the given topic. Use markdown with a code block to show some code on a slide. Avoid long coding examples, which may not fit in a single slide. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    '{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"Sample Title","lang":"en","beats":[{"text":"This is a slide, which just displays the title of this presentation.","image":{"type":"textSlide","slide":{"title":"This is the title of the presentation"}}},{"text":"This is ta slide, which just displays the title and the presenter\'s name of this presentation.","image":{"type":"textSlide","slide":{"title":"This is the title of the presentation","subtitle":"Tom Johnson"}}},{"text":"Here is the sample code","image":{"type":"markdown","markdown":"# Markdown Table Example\\n```TypeScript\\nconst main = () => {\\n  console.log(\'Hello World\')\\n}\\n```"}},{"text":"Here is two sets of code, side by side","image":{"type":"markdown","markdown":"# Hello World in two languages\\n<div style=\\"display: flex; gap: 16px;\\">\\n  <pre>// JavaScript example\\nfunction greet(name) {\\n  console.log(`Hello, ${name}!`);\\n}\\ngreet(\\"World\\");\\n</pre>\\n\\n  <pre># Python example\\ndef greet(name):\\n    print(f\\"Hello, {name}!\\")\\n\\ngreet(\\"World\\")\\n</pre>\\n</div>"}},{"text":"The evolution of humans is a complex journey that spans millions of years, shaped by biology, environment, and culture. Here\'s a high-level summary of the key stages in human evolution","image":{"type":"textSlide","slide":{"title":"Human Evolution","bullets":["Early Primates","Hominids and Hominins","Australopithecus","Genus Homo Emerges","Homo erectus and Migration","Neanderthals and Other Archaic Humans","Homo sapiens"]}}},{"text":"This table shows the items in the store.","image":{"type":"markdown","markdown":["# Markdown Table Example","| Item              | In Stock | Price |","| :---------------- | :------: | ----: |","| Python Hat        |   True   | 23.99 |","| SQL Hat           |   True   | 23.99 |","| Codecademy Tee    |  False   | 19.99 |","| Codecademy Hoodie |  False   | 42.99 |"]}},{"text":"Next, let\'s look at a diagram of our business process flow. This illustrates the key steps from product development to sales.","image":{"type":"mermaid","title":"Business Process Flow","code":{"kind":"text","text":"graph LR\\n    A[Market Research] --> B[Product Planning]\\n    B --> C[Development]\\n    C --> D[Testing]\\n    D --> E[Manufacturing]\\n    E --> F[Marketing]\\n    F --> G[Sales]\\n    G --> H[Customer Support]\\n    H --> A"}}},{"text":"This page shows the sales and profits of this company from January 2024 to June 2024.","image":{"type":"chart","title":"Sales and Profits (from Jan to June)","chartData":{"type":"bar","data":{"labels":["January","February","March","April","May","June"],"datasets":[{"label":"Revenue ($1000s)","data":[120,135,180,155,170,190],"backgroundColor":"rgba(54, 162, 235, 0.5)","borderColor":"rgba(54, 162, 235, 1)","borderWidth":1},{"label":"Profit ($1000s)","data":[45,52,68,53,61,73],"backgroundColor":"rgba(75, 192, 192, 0.5)","borderColor":"rgba(75, 192, 192, 1)","borderWidth":1}]},"options":{"responsive":true,"animation":false}}}},{"text":"This is the image of a high school girl in Harajuku.","image":{"type":"image","source":{"kind":"url","url":"https://satoshi.blogs.com/mag2/May2025/ghibli0.png"}}}]}\n' +
    "```",
  comic_strips:
    "Generate a script for a presentation of the given topic. Another AI will generate comic strips for each beat based on the text description of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>A multi panel comic strips. 1990s American workplace humor. Clean, minimalist line art with muted colors. One character is a nerdy office worker with glasses</style>"}}\n` +
    "```",
  drslump_comic:
    "Generate a script for a presentation of the given topic. Another AI will generate images for each beat based on the image prompt of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>Dragon Ball/Dr. Slump aesthetic.</style>","images":{"girl":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/slump_presenter.png"}}}}}\n` +
    "```",
  ghibli_comic:
    "Generate a script for a presentation of the given topic. Another AI will generate comic strips for each beat based on the text description of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>Ghibli style</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ghibli_presenter.png"}}}}}\n` +
    "```",
  ghibli_image_only:
    "Generate a script for a presentation of the given topic. Another AI will generate an image for each beat based on the text description of that beat. Use the JSON below as a template.\n" +
    "```JSON\n" +
    '{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","beats":[{"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>Ghibli style</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ghibli_presenter.png"}}}}}\n' +
    "```",
  ghibli_shorts:
    "Generate a Japanese script for a Youtube shorts of the given topic. Another AI will generate comic strips for each beat based on the text description of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1024,"height":1536},"speechParams":{"speakers":{"Presenter":{"provider":"nijivoice","voiceId":"3708ad43-cace-486c-a4ca-8fe41186e20c","speechOptions":{"speed":1.5}}}},"imageParams":{"style":"<style>Ghibli style</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ghibli_presenter.jpg"}}}}}\n` +
    "```",
  ghost_comic:
    "Generate a script for a presentation of the given topic. Another AI will generate images for each beat based on the image prompt of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>Ghost in the shell aesthetic.</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ghost_presenter.png"}},"optimus":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/optimus.png"}}}}}\n` +
    "```",
  html:
    "Generate a script for a business presentation of the given topic. Another LLM will generate actual slides from the prompt and data for each beat. Adding optional data would help it to generate more compelling slide. Mention the reference in one of beats, if it exists. The valid type of reference is 'article', 'paper', 'image', 'video', 'audio'. Use the JSON below as a template.\n" +
    "```JSON\n" +
    '{"$mulmocast":{"version":"1.1","credit":"closing"},"references":[{"url":"https://www.somegreatwebsite.com/article/123","title":"Title of the article we are referencing","type":"[TYPE OF ARTICLE: article, paper, image, video, audio]"}],"title":"[TITLE: Brief, engaging title for the topic]","htmlImageParams":{"provider":"anthropic","model":"claude-3-7-sonnet-20250219"},"lang":"en","beats":[{"text":"[NARRATION: Narration for the beat.]","htmlPrompt":{"prompt":"[PROMPT to create appropriate HTML page for the beat.]"}},{"text":"[NARRATION: Narration for the beat.]","htmlPrompt":{"prompt":"[PROMPT to create appropriate HTML page for the beat with the data.]","data":{"description":"DATA TO BE PRESENTED IN THIS BEAT (in any format)]","net_income":{"Q2 FY2024":320,"Q3 FY2024":333,"Q4 FY2024":350},"unit":"USD (Million)"}}}]}\n' +
    "```",
  onepiece_comic:
    "Generate a script for a presentation of the given topic. Another AI will generate images for each beat based on the image prompt of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>One Piece aesthetic.</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/onepiece_presenter.png"}}}}}\n` +
    "```",
  podcast_standard:
    "Please generate a podcast script based on the topic provided by the user.\n" +
    "\n" +
    "The output should follow the JSON schema specified below. Please provide your response as valid JSON within ```json code blocks for clarity.\n" +
    "\n" +
    "```JSON\n" +
    '{"type":"object","properties":{"$mulmocast":{"type":"object","properties":{"version":{"type":"string","const":"1.1"},"credit":{"type":"string","const":"closing"}},"required":["version"],"additionalProperties":false},"canvasSize":{"type":"object","properties":{"width":{"type":"number"},"height":{"type":"number"}},"required":["width","height"],"additionalProperties":false,"default":{"width":1280,"height":720}},"speechParams":{"type":"object","properties":{"speakers":{"type":"object","additionalProperties":{"type":"object","properties":{"displayName":{"type":"object","additionalProperties":{"type":"string"}},"voiceId":{"type":"string"},"isDefault":{"type":"boolean"},"speechOptions":{"type":"object","properties":{"speed":{"type":"number"},"instruction":{"type":"string"}},"additionalProperties":false},"provider":{"type":"string","enum":["nijivoice","openai","google","elevenlabs"],"default":"openai"},"model":{"type":"string","description":"TTS model to use for this speaker"}},"required":["voiceId"],"additionalProperties":false}}},"required":["speakers"],"additionalProperties":false,"default":{"speakers":{"Presenter":{"voiceId":"shimmer","displayName":{"en":"Presenter"}}}}},"imageParams":{"type":"object","properties":{"provider":{"type":"string","enum":["openai","google"],"default":"openai"},"model":{"type":"string"},"style":{"type":"string"},"moderation":{"type":"string"},"images":{"type":"object","additionalProperties":{"anyOf":[{"type":"object","properties":{"type":{"type":"string","const":"image"},"source":{"anyOf":[{"type":"object","properties":{"kind":{"type":"string","const":"url"},"url":{"type":"string","format":"uri"}},"required":["kind","url"],"additionalProperties":false},{"type":"object","properties":{"kind":{"type":"string","const":"base64"},"data":{"type":"string"}},"required":["kind","data"],"additionalProperties":false},{"type":"object","properties":{"kind":{"type":"string","const":"text"},"text":{"type":"string"}},"required":["kind","text"],"additionalProperties":false},{"type":"object","properties":{"kind":{"type":"string","const":"path"},"path":{"type":"string"}},"required":["kind","path"],"additionalProperties":false}]}},"required":["type","source"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"imagePrompt"},"prompt":{"type":"string"}},"required":["type","prompt"],"additionalProperties":false}]}}},"additionalProperties":false,"default":{"provider":"openai","images":{}}},"movieParams":{"type":"object","properties":{"provider":{"type":"string","enum":["replicate","google"],"default":"replicate"},"model":{"type":"string"},"transition":{"type":"object","properties":{"type":{"type":"string","enum":["fade","slideout_left"]},"duration":{"type":"number","minimum":0,"maximum":2,"default":0.3}},"required":["type"],"additionalProperties":false},"fillOption":{"type":"object","properties":{"style":{"type":"string","enum":["aspectFit","aspectFill"],"default":"aspectFit"}},"additionalProperties":false,"description":"How to handle aspect ratio differences between image and canvas"}},"additionalProperties":false,"default":{"provider":"replicate"}},"soundEffectParams":{"type":"object","properties":{"provider":{"type":"string","enum":["replicate"],"default":"replicate"},"model":{"type":"string"}},"additionalProperties":false,"default":{"provider":"replicate"}},"lipSyncParams":{"type":"object","properties":{"provider":{"type":"string"},"model":{"type":"string"}},"additionalProperties":false},"htmlImageParams":{"type":"object","properties":{"model":{"type":"string"},"provider":{"type":"string","enum":["openai","anthropic"],"default":"openai"}},"additionalProperties":false},"textSlideParams":{"type":"object","properties":{"cssStyles":{"anyOf":[{"type":"string"},{"type":"array","items":{"type":"string"}}]}},"required":["cssStyles"],"additionalProperties":false},"captionParams":{"type":"object","properties":{"lang":{"type":"string"},"styles":{"type":"array","items":{"type":"string"},"default":[]}},"additionalProperties":false},"audioParams":{"type":"object","properties":{"padding":{"type":"number","default":0.3,"description":"Padding between beats"},"introPadding":{"type":"number","default":1,"description":"Padding at the beginning of the audio"},"closingPadding":{"type":"number","default":0.8,"description":"Padding before the last beat"},"outroPadding":{"type":"number","default":1,"description":"Padding at the end of the audio"},"bgm":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source"},"bgmVolume":{"type":"number","default":0.2,"description":"Volume of the background music"},"audioVolume":{"type":"number","default":1,"description":"Volume of the audio"},"suppressSpeech":{"type":"boolean","default":false,"description":"Suppress speech generation"}},"additionalProperties":false,"default":{"introPadding":1,"padding":0.3,"closingPadding":0.8,"outroPadding":1,"bgmVolume":0.2,"audioVolume":1}},"title":{"type":"string"},"description":{"type":"string"},"references":{"type":"array","items":{"type":"object","properties":{"url":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source/anyOf/0/properties/url"},"title":{"type":"string"},"description":{"type":"string"},"type":{"anyOf":[{"type":"string","enum":["article","paper","image","video","audio"]},{"type":"string"}],"default":"article"}},"required":["url"],"additionalProperties":false}},"lang":{"$ref":"#/properties/captionParams/properties/lang"},"beats":{"type":"array","items":{"type":"object","properties":{"speaker":{"type":"string"},"text":{"type":"string","default":"","description":"Text to be spoken. If empty, the audio is not generated."},"id":{"type":"string","description":"Unique identifier for the beat."},"description":{"type":"string"},"image":{"anyOf":[{"type":"object","properties":{"type":{"type":"string","const":"markdown"},"markdown":{"$ref":"#/properties/textSlideParams/properties/cssStyles"}},"required":["type","markdown"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"web"},"url":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source/anyOf/0/properties/url"}},"required":["type","url"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"pdf"},"source":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source"}},"required":["type","source"],"additionalProperties":false},{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0"},{"type":"object","properties":{"type":{"type":"string","const":"svg"},"source":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source"}},"required":["type","source"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"movie"},"source":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source"}},"required":["type","source"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"textSlide"},"slide":{"type":"object","properties":{"title":{"type":"string"},"subtitle":{"type":"string"},"bullets":{"type":"array","items":{"type":"string"}}},"required":["title"],"additionalProperties":false}},"required":["type","slide"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"chart"},"title":{"type":"string"},"chartData":{"type":"object","additionalProperties":{}}},"required":["type","title","chartData"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"mermaid"},"title":{"type":"string","description":"The title of the diagram"},"code":{"anyOf":[{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source/anyOf/0"},{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source/anyOf/1"},{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source/anyOf/2"},{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source/anyOf/3"}],"description":"The code of the mermaid diagram"},"appendix":{"type":"array","items":{"type":"string"},"description":"The appendix of the mermaid diagram; typically, style information."}},"required":["type","title","code"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"html_tailwind"},"html":{"$ref":"#/properties/textSlideParams/properties/cssStyles"}},"required":["type","html"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"beat"},"id":{"type":"string","description":"Specifies the beat to reference."}},"required":["type"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"voice_over"},"startAt":{"type":"number","description":"The time to start the voice over the video in seconds."}},"required":["type"],"additionalProperties":false}]},"audio":{"anyOf":[{"type":"object","properties":{"type":{"type":"string","const":"audio"},"source":{"$ref":"#/properties/imageParams/properties/images/additionalProperties/anyOf/0/properties/source"}},"required":["type","source"],"additionalProperties":false},{"type":"object","properties":{"type":{"type":"string","const":"midi"},"source":{"type":"string"}},"required":["type","source"],"additionalProperties":false}]},"duration":{"type":"number","description":"Duration of the beat. Used only when the text is empty"},"imageParams":{"$ref":"#/properties/imageParams"},"audioParams":{"type":"object","properties":{"padding":{"type":"number","description":"Padding between beats"},"movieVolume":{"type":"number","default":1,"description":"Audio volume of the imported or generated movie"}},"additionalProperties":false},"movieParams":{"type":"object","properties":{"provider":{"$ref":"#/properties/movieParams/properties/provider"},"model":{"type":"string"},"fillOption":{"$ref":"#/properties/movieParams/properties/fillOption","description":"How to handle aspect ratio differences between image and canvas"},"speed":{"type":"number","description":"Speed of the video. 1.0 is normal speed. 0.5 is half speed. 2.0 is double speed."}},"additionalProperties":false},"soundEffectParams":{"$ref":"#/properties/soundEffectParams"},"lipSyncParams":{"$ref":"#/properties/lipSyncParams"},"htmlImageParams":{"type":"object","properties":{"model":{"$ref":"#/properties/htmlImageParams/properties/model"}},"additionalProperties":false},"speechOptions":{"$ref":"#/properties/speechParams/properties/speakers/additionalProperties/properties/speechOptions"},"textSlideParams":{"$ref":"#/properties/textSlideParams"},"captionParams":{"$ref":"#/properties/captionParams"},"imageNames":{"type":"array","items":{"type":"string"}},"imagePrompt":{"type":"string"},"moviePrompt":{"type":"string"},"soundEffectPrompt":{"type":"string"},"htmlPrompt":{"type":"object","properties":{"systemPrompt":{"type":"string","default":""},"prompt":{"type":"string","default":""},"data":{},"images":{"type":"object","additionalProperties":{}}},"additionalProperties":false},"enableLipSync":{"type":"boolean","description":"Enable lip sync generation for this beat"}},"additionalProperties":false},"minItems":1},"imagePath":{"type":"string"},"__test_invalid__":{"type":"boolean"}},"required":["$mulmocast","beats"],"additionalProperties":false,"$schema":"http://json-schema.org/draft-07/schema#"}\n' +
    "```",
  portrait_movie:
    "Generate a script for a presentation of the given topic. Another AI will generate images for each beat based on the image prompt of that beat. Movie prompts must be written in English. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"movieParams":{"provider":"google"},"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"}],"canvasSize":{"width":1024,"height":1536},"imageParams":{"style":"<style>Photo realistic, cinematic.</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/female_presenter.png"}}}}}\n` +
    "```",
  realistic_movie:
    "Generate a script for a presentation of the given topic. Another AI will generate images for each beat based on the image prompt of that beat. Movie prompts must be written in English. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"movieParams":{"provider":"google"},"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>Photo realistic, cinematic.</style>","images":{"presenter":{"type":"image","source":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/female_presenter.png"}}}}}\n` +
    "```",
  sensei_and_taro:
    "この件について、内容全てを高校生にも分かるように、太郎くん(Student)と先生(Teacher)の会話、という形の台本をArtifactとして作って。ただし要点はしっかりと押さえて。以下に別のトピックに関するサンプルを貼り付けます。このJSONフォーマットに従って。\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"韓国の戒厳令とその日本への影響","description":"韓国で最近発令された戒厳令とその可能性のある影響について、また日本の憲法に関する考慮事項との類似点を含めた洞察に満ちた議論。","lang":"ja","beats":[{"speaker":"Announcer","text":"今日は、韓国で起きた戒厳令について、太郎くんが先生に聞きます。","imagePrompt":"A classroom setting with a curious Japanese student (Taro) and a kind teacher. Calm atmosphere, early morning light coming through the window."},{"speaker":"Student","text":"先生、今日は韓国で起きた戒厳令のことを教えてもらえますか？","imagePrompt":"The student (Taro) sitting at his desk with a serious expression, raising his hand to ask a question. Teacher is slightly surprised but attentive."},{"speaker":"Teacher","text":"もちろんだよ、太郎くん。韓国で最近、大統領が「戒厳令」っていうのを突然宣言したんだ。","imagePrompt":"TV screen showing a breaking news headline in Korean: 'President Declares Martial Law'. Students watching with concern."},{"speaker":"Student","text":"戒厳令ってなんですか？","imagePrompt":"A close-up of the student's puzzled face, with a speech bubble saying '戒厳令って？'"},{"speaker":"Teacher","text":"簡単に言うと、国がすごく危ない状態にあるとき、軍隊を使って人々の自由を制限するためのものなんだ。","imagePrompt":"Illustration of soldiers standing in the street, people being stopped and questioned, with a red 'X' on a protest sign. Moody and serious tone."},{"speaker":"Student","text":"それって怖いですね。なんでそんなことをしたんですか？","imagePrompt":"Student looking anxious, thinking deeply. Background shows a shadowy image of a politician giving orders to the military."},{"speaker":"Teacher","text":"大統領は「国会がうまく機能していないから」と言っていたけど…","imagePrompt":"A tense scene of military personnel entering a national assembly building in Korea, lawmakers looking shocked and resisting."},{"speaker":"Student","text":"ええっ！？国会議員を捕まえようとするなんて、すごく危ないことじゃないですか。","imagePrompt":"The student reacts with shock, comic-style expression with wide eyes and open mouth. Background fades into a dramatic courtroom or parliament chaos."},{"speaker":"Teacher","text":"その通りだよ。もし軍隊が国会を占拠していたら…","imagePrompt":"Dark visual of a locked parliament building with soldiers blocking the entrance, ominous sky in the background."},{"speaker":"Student","text":"韓国ではどうなったんですか？","imagePrompt":"Student leans forward, curious and worried. Background shows a hopeful scene of people holding protest signs with candles at night."},{"speaker":"Teacher","text":"幸い、野党の議員や市民たちが急いで集まって抗議して…","imagePrompt":"Peaceful protest scene in Seoul, citizens holding candles and banners, united. Hopeful tone."},{"speaker":"Student","text":"それは大変なことですね…。日本ではそんなこと起きないんですか？","imagePrompt":"Student looking toward the Japanese flag outside the school window, pensive mood."},{"speaker":"Teacher","text":"実はね、今、日本でも似たような話があるんだよ。","imagePrompt":"Teacher pointing to a newspaper headline: '緊急事態条項の議論進む'. Classroom chalkboard shows a map of Korea and Japan."},{"speaker":"Student","text":"緊急事態宣言って、韓国の戒厳令と同じようなものなんですか？","imagePrompt":"Split screen image: left side shows a soldier in Korea, right side shows a suited Japanese politician giving a press conference."},{"speaker":"Teacher","text":"似ている部分があるね。たとえば、総理大臣が…","imagePrompt":"Diagram-style visual showing the flow of emergency powers from PM to local governments. Simple, clean infographic style."},{"speaker":"Student","text":"それって便利そうですけど、なんだか心配です。","imagePrompt":"Student's concerned expression, behind him a blurry image of a street with emergency sirens glowing in red."},{"speaker":"Teacher","text":"そうだね。もちろん、緊急時には素早い対応が必要だけど…","imagePrompt":"Illustration of a balance scale: one side is 'freedom', the other 'security'. The scale is slightly tilting."},{"speaker":"Student","text":"韓国みたいに、軍隊が政治に口を出してくることもあり得るんですか？","imagePrompt":"Student imagining a military tank next to the Japanese parliament, shown as a thought bubble."},{"speaker":"Teacher","text":"完全にあり得ないとは言えないからこそ、注意が必要なんだ。","imagePrompt":"Japanese citizens reading newspapers and watching news with concerned faces, civic awareness growing."},{"speaker":"Student","text":"ありがとうございます。とても良い勉強になりました。","imagePrompt":"The student bows slightly to the teacher with a grateful expression. The classroom is peaceful again."},{"speaker":"Announcer","text":"ご視聴、ありがとうございました。次回の放送もお楽しみに。","imagePrompt":"Ending screen with soft background music, showing the show's logo and a thank-you message in Japanese."}],"canvasSize":{"width":1536,"height":1024},"imageParams":{"style":"<style>Ghibli style. Student (Taro) is a young teenager with a dark short hair with glasses. Teacher is a middle-aged man with grey hair and moustache.</style>"},"speechParams":{"speakers":{"Announcer":{"provider":"nijivoice","displayName":{"ja":"アナウンサー"},"voiceId":"3708ad43-cace-486c-a4ca-8fe41186e20c"},"Student":{"provider":"nijivoice","displayName":{"ja":"太郎"},"voiceId":"a7619e48-bf6a-4f9f-843f-40485651257f"},"Teacher":{"provider":"nijivoice","displayName":{"ja":"先生"},"voiceId":"bc06c63f-fef6-43b6-92f7-67f919bd5dae"}}}}\n` +
    "```",
  shorts:
    "Generate a script for a Youtube shorts of the given topic. The first beat should be a hook, which describes the topic. Another AI will generate images for each beat based on the image prompt of that beat. Movie prompts must be written in English.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"movieParams":{"provider":"google"},"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"}],"canvasSize":{"width":720,"height":1280},"imageParams":{"style":"<style>Photo realistic, cinematic.</style>"}}\n` +
    "```",
  text_and_image:
    "Generate a script for a presentation of the given topic. Another AI will generate comic strips for each beat based on the imagePrompt of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]","imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]"}]}\n` +
    "```",
  text_only:
    "Generate a script for a presentation of the given topic. Another AI will generate comic strips for each beat based on the text description of that beat. Mention the reference in one of beats, if it exists. Use the JSON below as a template.\n" +
    "```JSON\n" +
    `{"$mulmocast":{"version":"1.1","credit":"closing"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"beats":[{"text":"[OPENING_BEAT: Introduce the topic with a hook. Reference the source material and set up why this topic matters. Usually 2-3 sentences that grab attention and provide context.]"},{"text":"[MAIN_CONCEPT: Define or explain the core concept/idea. This should be the central focus of your narrative. Keep it clear and accessible.]"},{"text":"[SUPPORTING_DETAIL_1: Additional context, examples, or elaboration that helps illustrate the main concept. This could include how it works, why it's important, or real-world applications.]"},{"text":"[SUPPORTING_DETAIL_2: Continue with more examples, deeper explanation, or different aspects of the topic if needed.]"},{"text":"[ADDITIONAL_BEATS: Add more beats as necessary to fully explore the topic. Complex topics may require 6-10+ beats to cover adequately. Each beat should advance the narrative or provide valuable information.]"},{"text":"[CONCLUSION/IMPACT: Wrap up with the significance, implications, or key takeaway. Help the audience understand why this matters to them.]"}]}\n` +
    "```",
  trailer:
    "Generate a script for a movie trailer of the given story. Another AI will generate images for each beat based on the image prompt of that beat. Movie prompts must be written in English.\n" +
    "```JSON\n" +
    '{"$mulmocast":{"version":"1.1"},"title":"[TITLE: Brief, engaging title for the topic]","lang":"en","references":[{"url":"[SOURCE_URL: URL of the source material]","title":"[SOURCE_TITLE: Title of the referenced article, or paper]","type":"[SOURCE_TYPE: article, paper]"}],"movieParams":{"provider":"google"},"beats":[{"duration":5,"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"duration":5,"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"duration":5,"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"duration":5,"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"duration":5,"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"},{"duration":5,"imagePrompt":"[IMAGE_PROMPT: A prompt for the image to be generated for this beat.]","moviePrompt":"[MOVIE_PROMPT: A movie prompt for that image.]"}],"canvasSize":{"width":1280,"height":720},"imageParams":{"style":"<style>Photo realistic, cinematic.</style>"},"audioParams":{"padding":0,"introPadding":0,"closingPadding":0,"outroPadding":2.5,"bgm":{"kind":"url","url":"https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/bgms/trailer_dramatic.mp3"}}}\n' +
    "```",
};
