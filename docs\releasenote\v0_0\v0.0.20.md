# プロンプト
0.0.20 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.20

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
要約の文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は v0.0.16 を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## 今回のリリースに含まれる Pull Request
## What's Changed
* ghibli_image_only template by @snakajima in https://github.com/receptron/mulmocast-cli/pull/549
* docs: Clarify -p option accepts file paths for presentation styles by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/550
* Fix packaging configuration by @kawamataryo in https://github.com/receptron/mulmocast-cli/pull/548


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.19...0.0.20

--- 以下、Generated by Claude Code --- 
## Pull Request Summaries (バイリンガル)

### PR #549: ghibli_image_only template
- **English**: Added a new Ghibli-inspired template specifically designed for image-only presentations without text narration. This template combines the popular Studio Ghibli aesthetic with the image-only presentation format introduced in v0.0.19, enabling creators to produce visually stunning presentations that rely purely on imagery to convey their message. The template includes carefully crafted prompts and styling that evoke the distinctive Ghibli animation style - featuring soft colors, whimsical characters, and dreamlike landscapes. This addition expands the creative possibilities for visual storytelling, particularly useful for artistic portfolios, mood boards, or presentations where the visual impact is more important than textual content.
- **日本語**: テキストナレーションなしの画像のみのプレゼンテーション専用に設計された新しいジブリ風テンプレートを追加しました。このテンプレートは、人気のスタジオジブリの美学とv0.0.19で導入された画像のみのプレゼンテーション形式を組み合わせ、クリエイターが純粋に画像でメッセージを伝える視覚的に魅力的なプレゼンテーションを制作できるようにします。このテンプレートには、独特のジブリアニメーションスタイルを呼び起こす慎重に作られたプロンプトとスタイリングが含まれています - ソフトな色彩、幻想的なキャラクター、夢のような風景が特徴です。この追加により、ビジュアルストーリーテリングの創造的な可能性が拡大され、特にアーティスティックなポートフォリオ、ムードボード、またはテキストコンテンツよりもビジュアルインパクトが重要なプレゼンテーションに有用です。

### PR #550: docs: Clarify -p option accepts file paths for presentation styles
- **English**: Comprehensive documentation update to clarify that the `-p` option accepts file paths for presentation styles, addressing user confusion about this important feature. The update includes multiple documentation improvements: enhanced v0.0.16 release notes with clear file path usage examples, new FAQ sections in both English and Japanese explaining presentation style usage, updated README.md with current `mulmo movie --help` output including the `-p` option, and clear instructions directing users to download styles from `assets/styles/` directory. This documentation overhaul resolves issue #544 and ensures users understand how to properly utilize the presentation style system introduced in v0.0.16, preventing common usage errors and improving the overall user experience.
- **日本語**: プレゼンテーションスタイルに関する重要な機能について、`-p`オプションがファイルパスを受け取る仕様であることを明確化するための包括的なドキュメント更新です。この更新には複数のドキュメント改善が含まれています：明確なファイルパス使用例を含むv0.0.16リリースノートの強化、プレゼンテーションスタイルの使用方法を説明する英語と日本語の新しいFAQセクション、`-p`オプションを含む現在の`mulmo movie --help`出力でのREADME.mdの更新、そして`assets/styles/`ディレクトリからスタイルをダウンロードするようユーザーに指示する明確な説明です。このドキュメントの全面的な見直しは、issue #544を解決し、v0.0.16で導入されたプレゼンテーションスタイルシステムを適切に活用する方法をユーザーが理解できるようにし、一般的な使用エラーを防ぎ、全体的なユーザー体験を向上させます。

### PR #548: Fix packaging configuration
- **English**: Resolved critical packaging and library integration issues that were affecting both TypeScript developers and browser environment users. The fix addresses multiple import and export problems: corrected type errors when importing MulmoCast as a library where exported functions weren't properly accessible, fixed type resolution issues for browser-incompatible code that wasn't throwing appropriate errors, eliminated `https-proxy-agent` errors that occurred when importing schemas in browser environments, and extended library access to include agents directory for more comprehensive API usage. These packaging improvements ensure that MulmoCast can be properly integrated as a dependency in other projects, whether for Node.js applications or browser-based tools, while maintaining proper type safety and error handling across different environments.
- **日本語**: TypeScript開発者とブラウザ環境ユーザーの両方に影響していた重要なパッケージングとライブラリ統合の問題を解決しました。この修正では、複数のインポートとエクスポートの問題に対処しています：MulmoCastをライブラリとしてインポートする際にエクスポートされた関数が適切にアクセスできなかった型エラーの修正、適切なエラーをスローしていなかったブラウザ非互換コードの型解決問題の修正、ブラウザ環境でスキーマをインポートする際に発生していた`https-proxy-agent`エラーの解消、そしてより包括的なAPI使用のためのagentsディレクトリへのライブラリアクセスの拡張です。これらのパッケージング改善により、Node.jsアプリケーションでもブラウザベースのツールでも、MulmoCastが他のプロジェクトの依存関係として適切に統合できるようになり、異なる環境間で適切な型安全性とエラーハンドリングが維持されます。

## Release Notes – Developer-Focused (English)

MulmoCast CLI v0.0.20 is a focused maintenance release that addresses critical packaging issues and enhances documentation clarity while introducing a new creative template:

### Library Integration & Packaging:
- **Fixed TypeScript Library Integration**: Resolved critical type errors when importing MulmoCast as a library dependency, ensuring exported functions are properly accessible to downstream projects
- **Browser Compatibility Improvements**: Eliminated `https-proxy-agent` errors in browser environments and fixed type resolution for browser-incompatible code
- **Extended Agent Access**: Made agents directory available for library usage, enabling more comprehensive API integration in external projects
- **Cross-Environment Support**: Improved package configuration to work seamlessly across Node.js and browser environments with proper type safety

### Documentation & User Experience:
- **Presentation Style Clarification**: Comprehensive documentation update addressing issue #544 - clarified that `-p` option accepts file paths, not just built-in style names
- **Enhanced Help Documentation**: Updated README.md with current `mulmo movie --help` output, including proper `-p` option documentation
- **Multilingual FAQ**: Added FAQ sections in both English and Japanese explaining presentation style usage and file path requirements
- **Clear Download Instructions**: Added explicit guidance for downloading styles from `assets/styles/` directory

### New Creative Features:
- **Ghibli Image-Only Template**: New template combining Studio Ghibli aesthetic with the image-only presentation format introduced in v0.0.19

### Breaking Changes:
- None - all changes maintain backward compatibility

This release significantly improves the developer experience when integrating MulmoCast as a library while resolving user confusion around the presentation style system.

## リリースノート – 開発者向け (日本語)

MulmoCast CLI v0.0.20は、重要なパッケージング問題に対処し、ドキュメントの明確性を向上させ、新しいクリエイティブテンプレートを導入する集中的なメンテナンスリリースです：

### ライブラリ統合・パッケージング:
- **TypeScriptライブラリ統合の修正**: MulmoCastをライブラリ依存関係としてインポートする際の重要な型エラーを解決し、エクスポートされた関数が下流プロジェクトで適切にアクセス可能であることを保証
- **ブラウザ互換性の改善**: ブラウザ環境での`https-proxy-agent`エラーを解消し、ブラウザ非互換コードの型解決を修正
- **エージェントアクセスの拡張**: agentsディレクトリをライブラリ使用可能にし、外部プロジェクトでのより包括的なAPI統合を実現
- **クロス環境サポート**: 適切な型安全性を持つNode.jsとブラウザ環境でシームレスに動作するパッケージ設定を改善

### ドキュメント・ユーザー体験:
- **プレゼンテーションスタイルの明確化**: issue #544に対処する包括的なドキュメント更新 - `-p`オプションが組み込みスタイル名だけでなくファイルパスを受け取ることを明確化
- **ヘルプドキュメントの強化**: 適切な`-p`オプションドキュメントを含む現在の`mulmo movie --help`出力でREADME.mdを更新
- **多言語FAQ**: プレゼンテーションスタイルの使用方法とファイルパス要件を説明する英語と日本語のFAQセクションを追加
- **明確なダウンロード指示**: `assets/styles/`ディレクトリからスタイルをダウンロードするための明示的なガイダンスを追加

### 新しいクリエイティブ機能:
- **ジブリ画像のみテンプレート**: スタジオジブリの美学とv0.0.19で導入された画像のみのプレゼンテーション形式を組み合わせた新しいテンプレート

### 破壊的変更:
- なし - すべての変更は後方互換性を維持

このリリースは、プレゼンテーションスタイルシステムに関するユーザーの混乱を解決しながら、MulmoCastをライブラリとして統合する際の開発者体験を大幅に改善します。

## Release Notes – Creator-Focused (English)

MulmoCast CLI v0.0.20 brings clearer guidance for using presentation styles and introduces a beautiful new artistic template:

### New Creative Options:
- **Ghibli-Style Image Presentations**: New template that combines the enchanting Studio Ghibli aesthetic with image-only presentations - perfect for creating dreamlike, artistic content without narration

### Improved Documentation & Guidance:
- **Clearer Style Usage Instructions**: We've made it much clearer how to use custom presentation styles with the `-p` option - you can now easily download and use style files from our GitHub repository
- **Better Help Documentation**: Updated help text and documentation to show exactly how to use presentation styles with file paths
- **FAQ Sections**: New frequently asked questions sections (in English and Japanese) that explain how to get the most out of presentation styles

### What This Means for You:
- **Easier Style Customization**: No more confusion about how to use custom presentation styles - clear examples and documentation guide you through the process
- **More Artistic Freedom**: The new Ghibli template opens up possibilities for creating magical, artistic presentations that rely purely on visual storytelling
- **Better Learning Resources**: Enhanced documentation helps you understand all the creative possibilities available with MulmoCast

### Getting Started with Custom Styles:
1. Download style files from `assets/styles/` in the MulmoCast repository
2. Use them with `mulmo movie your-script.json -p path/to/style.json`
3. Create presentations that match your artistic vision

This release focuses on making MulmoCast more accessible and easier to use while expanding your creative toolkit.

## リリースノート – クリエイター向け (日本語)

MulmoCast CLI v0.0.20は、プレゼンテーションスタイルの使用に関するより明確なガイダンスを提供し、美しい新しいアーティスティックテンプレートを導入します：

### 新しいクリエイティブオプション:
- **ジブリスタイル画像プレゼンテーション**: 魅惑的なスタジオジブリの美学と画像のみのプレゼンテーションを組み合わせた新しいテンプレート - ナレーションなしで夢のようなアーティスティックなコンテンツを作成するのに最適

### 改善されたドキュメント・ガイダンス:
- **より明確なスタイル使用説明**: `-p`オプションでカスタムプレゼンテーションスタイルを使用する方法をより明確にしました - GitHubリポジトリからスタイルファイルを簡単にダウンロードして使用できます
- **より良いヘルプドキュメント**: ファイルパスでプレゼンテーションスタイルを使用する方法を正確に示すヘルプテキストとドキュメントを更新
- **FAQセクション**: プレゼンテーションスタイルを最大限に活用する方法を説明する新しい よくある質問セクション（英語と日本語）

### これがあなたにとって意味すること:
- **より簡単なスタイルカスタマイズ**: カスタムプレゼンテーションスタイルの使用方法についての混乱がなくなりました - 明確な例とドキュメントがプロセスをガイドします
- **より多くのアーティスティックな自由**: 新しいジブリテンプレートにより、純粋にビジュアルストーリーテリングに依存する魔法的でアーティスティックなプレゼンテーションを作成する可能性が開かれます
- **より良い学習リソース**: 強化されたドキュメントにより、MulmoCastで利用可能なすべての創造的可能性を理解できます

### カスタムスタイルの始め方:
1. MulmoCastリポジトリの`assets/styles/`からスタイルファイルをダウンロード
2. `mulmo movie your-script.json -p path/to/style.json`で使用
3. あなたのアーティスティックビジョンに合ったプレゼンテーションを作成

このリリースは、創造的なツールキットを拡張しながら、MulmoCastをよりアクセスしやすく使いやすくすることに焦点を当てています。