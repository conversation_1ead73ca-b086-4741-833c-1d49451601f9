# v0.0.12 Release Notes

## Pull Request Summaries (バイリンガル)

### PR #446: BGM customization
- **English**: Added support for customizing background music (BGM) in generated media. Introduced an optional "bgm" field in the audio parameters, allowing a music file (by URL or path) to be specified as background music. The audio pipeline now injects the specified or default BGM track into the output.
- **日本語**: 出力メディアにバックグラウンド音楽（BGM）をカスタマイズできる機能を追加しました。音声パラメータにオプションの "bgm" フィールドを導入し、URLまたはパスで指定した音楽ファイルをBGMとして設定できます。オーディオ処理で指定されたBGM（またはデフォルトのBGM）を出力に組み込むようになりました。

### PR #447: Add detailed instruction for Google models
- **English**: Added a detailed documentation page for using Google's image generation models. This includes prerequisites (installing and authenticating gcloud, creating a GCP project with billing, enabling Vertex AI API, etc.) and clarifies authentication commands. The README now links to this new "pre-requisites for Google models" guide.
- **日本語**: Googleの画像生成モデルを使用するための詳細な手順を記載したドキュメントを追加しました。gcloud のインストールと認証、GCPプロジェクトの作成と課金有効化、Vertex AI APIの有効化などの前提条件が含まれ、認証コマンドの使い分けについても説明しています。READMEにこの新しい「Googleモデル利用の事前準備」ガイドへのリンクを追加しました。

### PR #448: ghibli_shorts
- **English**: Introduced a new "Ghibli comic style" template for short-form content. This template is designed to create a short video in a Studio Ghibli-like comic strip style, generating a Japanese script for a YouTube Short with Ghibli-style illustrated beats. It uses a specific narrator voice (Nijivoice) and includes a pre-defined Ghibli-themed presenter image.
- **日本語**: **「ジブリ風コミックスタイル」**の新しいテンプレートを追加しました。スタジオジブリ風のコミック調短編動画を生成するためのテンプレートで、YouTubeショート向けに日本語のスクリプトを作成し、各ビートにジブリ風のイラストを生成します。特定のナレーター音声（Nijivoice）を使用し、ジブリ風テーマのプレゼンター画像が含まれています。

### PR #449: Eleven Labs TTS support
- **English**: Added support for ElevenLabs text-to-speech. A new TTS provider "elevenlabs" is now available, enabling high-quality voices from ElevenLabs. This includes a new agent that calls the ElevenLabs API (requiring the environment variable ELEVENLABS_API_KEY to be set). Voice IDs for ElevenLabs voices (e.g. "David", "Felicity") can be specified in the speech configuration. Concurrency is limited (similar to Nijivoice) to comply with ElevenLabs API usage.
- **日本語**: ElevenLabs の音声合成（TTS）をサポートしました。新たに "elevenlabs" をプロバイダーとして指定でき、高品質なElevenLabsの音声を利用できます。ElevenLabs APIを呼び出すエージェントを追加しており、利用には環境変数 ELEVENLABS_API_KEY の設定が必要です。音声設定でElevenLabsの声のID（例：「David」「Felicity」）を指定できます。ElevenLabs APIの特性に合わせ、Nijivoice同様に音声生成の同時実行数を制限しています。

### PR #450: Media type beat
- **English**: Enhanced the media generation system to allow beat references as an image type. Each beat can now have an id and an image asset of type "beat" that references another beat's output. This means a later beat can reuse a media (image) from an earlier beat instead of generating a new one. The schema was extended (mulmoBeatSchema.id field and a new mulmoBeatReferenceMediaSchema) and an image plugin added to handle these references. (No visual change in output unless templates make use of this feature.)
- **日本語**: メディア生成システムが強化され、画像タイプとしてビート参照を指定できるようになりました。各ビートに id が付与でき、画像アセットに他のビートの出力を参照する "beat" タイプを指定可能になりました。これにより、後のビートで前のビートの画像などを再利用でき、新規生成を省略できます。スキーマが拡張され（mulmoBeatSchema に id フィールド追加、新たに mulmoBeatReferenceMediaSchema 追加）、これらの参照を処理する画像プラグインが導入されています。（テンプレートでこの機能を使用しない限り、出力の見た目への影響はありません。）

### PR #451: clean up agent info
- **English**: Refactored and cleaned up the metadata for various agent functions. Ensured that each TTS agent's info (OpenAI, Google, Nijivoice, ElevenLabs) includes consistent fields such as description, category, author, and samples. This is an internal maintenance change with no effect on functionality, but it improves code clarity and consistency.
- **日本語**: 各種エージェント関数のメタデータ定義を整理・リファクタリングしました。OpenAIやGoogle、Nijivoice、ElevenLabsといった各TTSエージェントの情報に、説明やカテゴリ、作成者、サンプルなどのフィールドを一貫して含めるようにしました。機能に影響はない内部的なメンテナンス変更ですが、コードの明確さと一貫性が向上しています。

### PR #452: Mixed TTS providers
- **English**: Improved the TTS system to allow multiple providers in one script. Different speakers can now use different TTS providers simultaneously. The speechParams configuration for speakers can include a provider override per speaker; if absent, it falls back to the global provider. This means, for example, one character's lines could use ElevenLabs while another's use OpenAI in the same project. The system gathers all providers in use and adjusts processing (e.g., concurrency limits) if a provider like ElevenLabs or Nijivoice is involved.
- **日本語**: TTSシステムが改善され、1つのスクリプト内で複数のプロバイダーを使用可能になりました。話者ごとに異なるTTSプロバイダーを同時に利用できます。speechParams の各話者設定で個別に provider を指定でき、指定がない場合は全体のプロバイダー設定が適用されます。例えば同じプロジェクト内で、あるキャラクターの台詞はElevenLabs、別のキャラクターはOpenAIの音声で合成するといったことが可能です。使用する全プロバイダーを収集し、ElevenLabsやNijivoiceのようなプロバイダーが含まれる場合は処理の並列度などを適切に調整します。

### PR #453: Image_Edit – JPEG support
- **English**: Extended the image editing functionality to support JPEG files in addition to PNG. The system now correctly handles .jpg/.jpeg images (setting appropriate MIME types, etc.) when reading or writing image files. This prevents errors or quality loss when using JPEG assets in image editing workflows. (Previously, image editing assumed PNG format; now JPEGs are equally supported.)
- **日本語**: 画像編集機能でPNGに加えてJPEG形式のファイルもサポートしました。.jpg/.jpeg 画像を読み書きする際に適切なMIMEタイプを設定するなど、JPEG画像を正しく扱えるようになっています。これにより、画像編集ワークフローでJPEG素材を使用してもエラーが発生したり画質が劣化したりしなくなります。（従来は画像編集機能がPNG形式を前提としていましたが、今回の変更でJPEGも同様にサポートされます。）

### PR #454: Clean up – removed unused code
- **English**: Removed unused code from the codebase as a cleanup effort. This refactor eliminates dead or redundant code paths to simplify the codebase, without changing any behavior.
- **日本語**: 未使用のコードを削除するクリーンアップを行いました。使われていない不要なコードを削除することでコードベースを整理しました（動作の変更はありません）。

### PR #455: Removed beat index from audio file
- **English**: Simplified audio file naming by removing the beat index from audio filenames. Audio output files will no longer include the beat number in their name, making file names cleaner. (This was a minor internal change and does not affect functionality except for the file naming convention.)
- **日本語**: オーディオファイル名からビートのインデックス番号を取り除き、命名規則を簡素化しました。生成される音声ファイル名にビート番号が含まれなくなり、ファイル名がよりシンプルになります。（小規模な内部変更で、機能には影響ありません。ファイル名の形式のみの調整です。）

### PR #456: Fix rebuildStudio error
- **English**: Fixed a bug that caused an error in the rebuildStudio process. This fix resolves an issue where rebuilding the studio (likely re-initializing project state or beats) would throw an error, improving stability when re-running or updating a project.
- **日本語**: rebuildStudio 処理中にエラーが発生していたバグを修正しました。プロジェクトの再構築（ビートやスタジオ状態の再初期化）時にエラーが投げられる問題が解消され、プロジェクトの再実行や更新時の安定性が向上しています。

### PR #458: add mulmoScriptTemplateFileSchema
- **English**: Added a new schema definition mulmoScriptTemplateFileSchema for validating mulmo script template files. This provides a formal structure for template JSON files, which helps catch schema errors early and ensure templates conform to expected formats. (It's a developer-focused improvement for template validation.)
- **日本語**: mulmoScriptTemplateFileSchema という新しいスキーマ定義を追加し、Mulmoのスクリプトテンプレートファイルを検証できるようにしました。テンプレートJSONファイルの形式を厳密にチェックする仕組みで、スキーマの不整合を事前に検出し、テンプレートが期待される形式に従っていることを保証します。（テンプレート検証のための開発者向け改善です。）

### PR #459: process.exit(1) move to handler
- **English**: Refactored error handling to move the process.exit(1) call into a centralized handler. Instead of calling process.exit directly in various places on errors, the logic now raises an error that a higher-level handler catches and then exits. This makes the code cleaner and ensures any cleanup or logging can happen before the process exits, improving maintainability.
- **日本語**: エラー処理のリファクタリングを行い、process.exit(1) の呼び出しを集中管理されるハンドラー内に移動しました。エラー発生時に各所で直接 process.exit を呼ぶ代わりに、上位のハンドラーでエラーをキャッチしてから終了処理を行うよう変更しています。これにより、プロセス終了前に後片付けやログ出力を確実に実行でき、コードの保守性が向上します。

### PR #460: Fade transition
- **English**: Added a fade transition effect between visual beats/slides. When generating videos or slideshows, the transition between images now can be a smooth fade, enhancing the visual quality of the output. This new transition effect provides a more polished look to scene changes (as opposed to an abrupt cut).
- **日本語**: ビートやスライド間のフェード・トランジション効果を追加しました。動画やスライドショー生成時に、画像と画像の切り替わりがなめらかなフェードで行われるようになり、出力映像の視覚的な品質が向上します。カットの切替ではなくフェードを用いることで、シーン変更がより洗練された印象になります。

## Categories of Changes (変更のカテゴリ)
- **New Features**: BGM customization, Ghibli-style template, ElevenLabs TTS support, Mixed TTS providers support, Fade transition effect.
- **Enhancements**: Media type "beat" reference (advanced image reuse), JPEG image support, Multiple TTS providers per project (also listed under New Features), and adding a schema for template files (developer enhancement).
- **Bug Fixes**: Fixed rebuildStudio error, removed beat index from audio filenames (to fix naming issues).
- **Refactors/Maintenance**: Clean up of unused code, standardized agent info metadata, moved process exit logic to centralized handler.
- **Documentation**: Added detailed setup guide for Google's models (Google image generation prerequisites).
- **Breaking Changes**: None. (All changes are backwards-compatible; no APIs or commands were removed or altered in a breaking way.)

## Release Notes – Developer-Focused (English)

MulmoCast CLI v0.0.12 comes with significant new capabilities and important fixes. These release notes are tailored for developers and technical users, highlighting underlying changes and technical details:

### New Features:
- **Custom BGM Support**: You can now specify background music for your projects. The audio schema includes a new optional bgm field, allowing injection of a background music track (via URL or file path) into the generated audio. This feature uses a default BGM if none is provided, and integrates into the audio pipeline for seamless background audio.
- **ElevenLabs TTS Integration**: Introduced ElevenLabs as a new text-to-speech provider. A new TTS agent calls the ElevenLabs API (ensure ELEVENLABS_API_KEY is set). You can select "elevenlabs" in speechParams.provider and use specific voice IDs. The system treats ElevenLabs similarly to Nijivoice regarding concurrency (single-threaded processing) to adhere to rate limits.
- **Ghibli-Style Template**: Added a new template (ghibli_shorts.json) for Studio Ghibli-style comic presentations. It generates a Japanese script for a vertical video (e.g., YouTube Short) with a comic strip layout, using a preset Ghibli-themed character image and a fast-paced Nijivoice narrator. This template demonstrates multi-beat image generation with a distinct visual style.
- **Mixed TTS Providers per Script**: The speech configuration now supports multiple TTS providers in one script. You can assign different providers to different speakers (e.g., one speaker uses OpenAI, another uses ElevenLabs). If a speaker-specific provider is not set, it defaults to the global speechParams.provider. The system will gather all used providers and adjust processing (like ensuring only one voice generates at a time if using providers that don't support parallel requests).
- **Fade Transition Effect**: Implemented a fade transition between visual beats. When compiling videos, images now transition with a fade, resulting in smoother scene changes instead of hard cuts. This gives a more professional look to generated videos.

### Enhancements & Improvements:
- **"Beat" Media Reference**: Added support for using a prior beat's image in later beats. Each beat can have an id, and an image asset can reference a beat by its id ({ "type": "beat", "id": "<beat-id>" }). This prevents duplicate image generation when you want to reuse a graphic. Under the hood, a new image plugin handles these references, and the mulmoBeatSchema and related schemas were extended. (Primarily useful for template creators; existing content is unaffected unless this feature is used.)
- **JPEG Image Support**: The image editing module now supports JPEG files in addition to PNG. The system detects .jpg/.jpeg files and processes them with the correct MIME type. This means you can use JPEG assets in your projects (for image overlays/edits) without conversion to PNG, and the output image quality is preserved.
- **mulmoScriptTemplateFileSchema**: Introduced a new JSON schema for script template files. This helps validate and ensure consistency of template JSON files. Developers defining custom templates can benefit from early schema validation, catching errors in template structure at load time.
- **Audio Filename Simplification**: The generated audio files no longer include the beat index in their filenames. This is a minor change that cleans up file naming (e.g., output audio files will have a simpler name without a numeric suffix for beat order). It has no impact on functionality other than the name of the audio files on disk.

### Bug Fixes:
- **Fixed Studio Rebuild Error**: Resolved an issue where rebuildStudio (resetting or rebuilding the project's state) could throw an error. This fix improves stability when rerunning a generation or updating content, as the internal studio state now rebuilds without crashing.
- **Beat Index in Audio Issue**: Removed the beat index from audio file names to fix potential issues with audio processing or confusion in file outputs. This was a small fix ensuring that audio concatenation or playback isn't affected by unexpected file naming.

### Refactor & Maintenance:
- **Agent Metadata Cleanup**: Refactored TTS agent metadata definitions. All TTS agents (OpenAI, Google, Nijivoice, ElevenLabs) now have consistent AgentFunctionInfo with descriptions, categories, authors, and sample usages. This does not change any behavior, but if you introspect or log agent info, you'll see cleaner, more uniform metadata.
- **Error Handling Changes**: Moved process.exit(1) calls into a unified error handler. Rather than exiting in the middle of operations, errors are now thrown and caught by a top-level handler which then exits. This ensures proper cleanup and logging on fatal errors, and makes the codebase easier to maintain and test.
- **Code Cleanup**: Removed unused code and dependencies as part of general maintenance. This has no effect on functionality, but reduces technical debt and potential confusion in the code.

### Documentation:
- **Google Model Setup Guide**: Added comprehensive documentation for setting up Google's image generation models. The README now links to "pre-requisites for Google's image generation model" which details installing the Google Cloud SDK (gcloud), authentication (gcloud auth application-default login), creating a GCP project, enabling billing, and enabling the Vertex AI API. Developers intending to use Google's image model should follow these steps.

No breaking changes in this release. All existing commands and APIs remain compatible with previous versions (0.0.11). Developers can upgrade to 0.0.12 without modifying their code, while gaining access to the new features and fixes.

## リリースノート – 開発者向け (日本語)

MulmoCast CLI v0.0.12 には、新機能の追加および重要な修正が含まれています。これは開発者や技術ユーザー向けのリリースノートで、内部的な変更や技術的詳細に重点を置いています。

### 新機能:
- **BGMカスタマイズ対応**: プロジェクトにバックグラウンド音楽を指定できるようになりました。オーディオのスキーマにオプションフィールドbgmが追加され、URLまたはファイルパスでBGMトラックを指定できます。指定したBGMはオーディオ合成パイプラインに組み込まれ、デフォルトBGMの設定も可能です。
- **ElevenLabs音声合成統合**: 新たなTTSプロバイダーとしてElevenLabsを追加しました。ElevenLabsのAPIを呼び出すTTSエージェントが導入され（利用には環境変数 ELEVENLABS_API_KEY の設定が必要）、speechParams.provider で "elevenlabs" を選択可能です。特定の音声IDを指定して高品質な音声を利用できます。Nijivoiceと同様にElevenLabs使用時は同時実行を1セッションに制限するなど、API仕様に合わせた制御も組み込まれています。
- **ジブリ風コミックテンプレート**: スタジオジブリ風のコミックスタイル動画テンプレート（ghibli_shorts.json）を追加しました。縦長動画（例: YouTubeショート）の形式で、日本語のスクリプトとジブリ風イラスト付きのビートを生成します。高速なNijivoiceナレーションとジブリ風キャラクター画像を組み合わせ、マルチビートの映像を簡単に作成できます。
- **スクリプト内複数TTSプロバイダー**: 一つのスクリプト内で複数のTTSプロバイダーを併用できるようになりました。話者ごとに異なるプロバイダーを割り当てることが可能です（例: ナレーターはOpenAI、キャラクター音声はElevenLabsを使用）。話者設定で個別にproviderを指定しない場合は全体設定が適用されます。利用される全プロバイダーを把握し、ElevenLabsやNijivoiceのような逐次処理が必要なプロバイダーが含まれる場合には自動的に処理を調整します。
- **フェードトランジション効果**: ビート間の画面切り替えにフェード効果を導入しました。動画生成時に画像と画像の切替が滑らかなフェードで行われるため、カット割りよりも自然で洗練されたシーン転換が実現します。

### 機能改善/強化:
- **「beat」メディア参照**: あるビートで生成した画像を、後続のビートで再利用できるようになりました。各ビートにidを設定し、画像アセットとして { "type": "beat", "id": "<他ビートのid>" } を指定することで、以前のビートの画像を再利用できます。この変更により不要な画像生成を省略でき、テンプレート作成者にとって柔軟性が向上します。内部的には、新しい画像プラグインの追加とスキーマ（mulmoBeatSchemaなど）の拡張によって実現しています（この機能を使用しない場合、既存の出力に変化はありません）。
- **JPEG画像サポート**: 画像編集モジュールでJPEG形式をサポートしました。.jpg/.jpegファイルを読み書きする際に正しいMIMEタイプを設定し、問題なく処理できるようになっています。これにより、オーバーレイ画像や編集用素材としてJPEGをそのまま利用可能で、PNGに変換する手間が不要になりました。出力画質も元のJPEG品質で維持されます。
- **mulmoScriptTemplateFileSchema追加**: スクリプトテンプレートファイル検証用の新しいJSONスキーマを導入しました。テンプレートJSONの構造を厳密に定義・検証することで、テンプレート読み込み時にフォーマット不備を早期に検出できます。カスタムテンプレートを定義する開発者に有用な改善です。
- **オーディオファイル名の簡素化**: 出力される音声ファイル名からビート番号のインデックスを除去しました。これによりファイル名が簡潔になり、内部処理上の混乱やユーザーがファイルを見る際の煩雑さが減ります（機能面での変化はなく、ファイル命名規則のみの変更です）。

### バグ修正:
- **Studio再構築エラー修正**: rebuildStudio 実行時に発生していたエラーを修正しました。プロジェクトの再構築（スタジオコンテキストやビートの再初期化）処理が失敗していた問題が解消され、再生成やプロジェクト更新時にエラーで停止しなくなりました。
- **音声ファイル名のビート番号問題**: 音声ファイル名にビート番号が含まれていたことによる問題を修正しました。前述の通り、ファイル名からビートインデックスを除去することで、音声処理時のファイル認識の問題や予期しない動作を防止しています（主に内部的な整理ですが、結果として出力ファイル名がわかりやすくなります）。

### リファクタリング/保守:
- **エージェント情報の整理**: TTSエージェント（OpenAI, Google, Nijivoice, ElevenLabs）のメタ情報定義を整理しました。各エージェントの説明文、カテゴリ、作者、サンプルなどの情報を統一的に定義し、内部的なメタデータの一貫性を向上させています。動作への影響はありませんが、デバッグログや開発時にエージェント情報がより分かりやすくなります。
- **エラー処理の見直し**: process.exit(1) の呼び出し箇所を整理し、集中化されたエラーハンドラーで終了処理を行うように変更しました。深い箇所で直接プロセス終了するのではなく、エラーを上位に投げて最終的に一箇所で捕捉・ログ・終了する設計に改めています。これにより終了前の後処理が確実に行われ、コードの可読性・保守性も向上しました。
- **未使用コードの削除**: 使用されていないコードを削除し、依存関係を整理しました。機能的な変化はありませんが、コードベースが軽量化・単純化され、将来的なメンテナンスが容易になります。

### ドキュメント:
- **Googleモデル設定ガイド**: Googleの画像生成モデルを利用するための詳細なセットアップガイドを追加しました。READMEにリンクされた「Googleの画像生成モデル利用の事前準備」ドキュメントに、Google Cloud SDK（gcloud）のインストールと認証、GCPプロジェクトの作成と課金有効化、Vertex AI APIの有効化手順などが詳述されています。Googleの画像生成機能を使用する際はこの手順に従って環境を整えてください。

互換性に影響する破壊的変更はありません。 このリリースで導入された変更は後方互換性があります。既存のスクリプトやコマンドは変更不要でそのまま動作し、新機能と修正を利用できるようになります。

## Release Notes – Creator-Focused (English)

MulmoCast CLI v0.0.12 brings exciting new features and improvements for content creators. These notes focus on how the updates benefit your creative workflow and media outputs:

- **Customize Background Music**: You can now add your own background music to videos and podcasts! In this version, a background music (BGM) track can be specified for your project, which will play underneath the narration. If you don't specify one, a default music will be used, but now you have the control to choose a track that fits your content's mood and tone.
- **New "Ghibli Comic Style" Template**: We've added a brand-new template inspired by Studio Ghibli's style. This template helps you create short, vertical videos (ideal for YouTube Shorts or social media) with a comic-strip layout and a Ghibli-esque aesthetic. It automatically generates a Japanese story script and uses a Ghibli-themed character illustration and voice. It's perfect for creating whimsical, storybook-like short videos with minimal effort.
- **ElevenLabs Voices for Narration**: High-quality voices from ElevenLabs are now available for your narrations. If you have an ElevenLabs API key, you can use their lifelike voices for your script's narration. This means more voice options (including different accents and styles) to better match the personality of your content or characters. Simply configure your project to use ElevenLabs and pick a voice – the CLI will handle the rest.
- **Mix and Match Voice Providers**: You're no longer limited to one text-to-speech provider per project. In a single video, you could have one character spoken by an ElevenLabs voice while another uses an OpenAI voice, for example. This multi-provider support lets you assign the best-sounding voice for each role or section of your script, all in one go. The tool manages the timing and processing behind the scenes, so you can focus on creative voice casting.
- **Smoother Visual Transitions**: Videos generated with MulmoCast will look more polished thanks to a new fade transition between scenes. Instead of abrupt cuts from one image to the next, the images will fade, making the transition gentler on the eyes. Your slideshows and video presentations will feel more professional and fluid with this subtle change.
- **Images: Now with JPEG Support**: If you use JPEG images in your content, MulmoCast CLI now handles them seamlessly. Previously, it was optimized for PNG images, but now you can import and edit JPEG files without any extra conversion. This gives you more flexibility to use the images you have (in whatever format) when creating your videos.
- **Improved Stability and Fixes**:
  - The tool has been made more robust – a pesky error that sometimes occurred when restarting or updating a project ("rebuilding the studio") has been fixed. You should experience fewer crashes or hiccups during iterative editing.
  - We've cleaned up some behind-the-scenes aspects: removed unused code (making the program run a bit leaner) and simplified audio file names (no more odd numbers in the filenames – just straightforward names for your convenience).
  - Error messages and handling are improved as well, which means if something does go wrong, the tool will handle it more gracefully and give cleaner logs, helping you understand issues without abruptly closing.
- **Better Documentation for Google Image Generation**: If you plan to use Google's image generation in your projects, we've got you covered with a detailed step-by-step setup guide. It's linked in the README and walks you through getting the Google Cloud SDK, setting up your Google Cloud project, enabling the right services, and authenticating properly. We want to make sure setting up advanced features is as easy as possible, so be sure to check this guide if you're venturing into Google's tools.

This release is fully backward-compatible with previous versions. You don't need to change anything in your existing projects – you'll just find new options at your disposal and a smoother experience. Enjoy the new features, and happy creating!

## リリースノート – クリエイター向け (日本語)

MulmoCast CLI v0.0.12 では、クリエイターの皆さんにとって魅力的な新機能や改善が盛りだくさんです。制作ワークフローや生成されるコンテンツにどのようなメリットがあるか、わかりやすくご紹介します。

- **バックグラウンド音楽のカスタマイズ**: 動画やポッドキャストに、お好みのバックグラウンド音楽を追加できるようになりました！プロジェクト設定でBGMトラックを指定すれば、ナレーションの背後で音楽が流れるようになります。指定しない場合はデフォルト音楽が使われますが、今バージョンからはコンテンツの雰囲気やトーンに合った曲を自分で選ぶことができます。
- **新テンプレート「ジブリ風コミック」**: スタジオジブリのような雰囲気の短編動画を簡単に作れるテンプレートが登場しました。縦長動画（YouTubeショートなど）向けのこのテンプレートを使うと、日本語の物語風スクリプトとジブリ調のイラストを自動生成してくれます。優しい絵本のような映像を手軽に作成できるので、ファンタジックなショートコンテンツを作りたいときにピッタリです。
- **ElevenLabsの高品質ボイス**: ナレーションにElevenLabsの高品質な音声が使えるようになりました。もしElevenLabsのAPIキーをお持ちなら、そのリアルな声をコンテンツに利用できます。感情豊かな声や個性的な声など、より多彩なボイスでナレーションできるので、コンテンツのキャラクターに合わせた声の演出が可能になります。設定方法も簡単で、プロジェクトの音声設定でElevenLabsを選び、声の種類を指定するだけです。
- **音声合成プロバイダーを使い分け**: 1つのプロジェクト内で複数の音声合成エンジンを組み合わせて使えるようになりました。例えば、ナレーターの部分はOpenAIの音声、登場人物のセリフはElevenLabsの音声、といった具合に、シーンやキャラクターごとに最適な音声を割り当てられます。難しい設定は不要で、各話者に使いたいプロバイダーを指定するだけ。裏側ではツールが自動的に処理を調整してくれるので、安心してベストな声の組み合わせを追求できます。
- **映像の切り替えがなめらかに**: MulmoCastで生成する動画のシーン切り替えが、よりスムーズになりました。今回のアップデートで、画像から画像への移り変わりにフェード効果が付き、パッと切り替わるよりも柔らかな印象になります。スライドショーや動画プレゼンの雰囲気がぐっとプロらしくなり、視聴者に与える印象も向上します。
- **画像形式の対応強化 (JPEGサポート)**: お持ちの画像がJPEG形式でも大丈夫。MulmoCast CLIがJPEG画像をそのまま扱えるようになりました。これまでは主にPNG形式を想定していましたが、今後はJPEGの写真やイラストも追加の変換なしで取り込めます。お気に入りの画像素材がどの形式でも、そのままプロジェクトに活かせる柔軟性が生まれました。
- **その他の改善点と修正**:
  - 動作の安定性がアップしました。プロジェクトを再実行したり内容を更新したりする際に発生していたエラーが修正され、繰り返し生成するときもスムーズに動作します。
  - 内部的な整理も進めています。使われていないコードを削除して動作を軽くし、音声ファイルの名前もシンプルに整えました（ファイル名に余分な番号が付かないようにしました）。これらの変更により、見えない部分ですがソフトの信頼性と使い勝手が向上しています。
  - エラーの扱いも洗練され、万一問題が起きてもアプリが突然落ちるのではなく、きちんとエラーメッセージを出して処理を終了するようになりました。ログメッセージも分かりやすくなっているので、不具合の原因追跡もしやすくなっています。
- **Google画像生成のセットアップ手順ガイド**: もしMulmoCastでGoogleの画像生成機能を使いたい場合、セットアップ方法を詳しく説明したガイドを用意しました。READMEからアクセスできるこのドキュメントには、Google Cloud SDK（gcloud）の導入、Google Cloudプロジェクトの作成と課金設定、Vertex AI APIの有効化、そして認証方法まで、一つひとつ手順を追って説明されています。高度な機能も安心して使えるよう、ぜひご活用ください。

今回のアップデートにおいて既存の機能に互換性の問題は生じません。 これまでのプロジェクトやコマンドはそのまま使え、新機能や改善点を追加設定で利用できるようになります。MulmoCast CLI 0.0.12 で、ぜひ制作の幅をさらに広げてみてください！