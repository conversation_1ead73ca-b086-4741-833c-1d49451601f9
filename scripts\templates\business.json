{"$mulmocast": {"version": "1.1", "credit": "closing"}, "title": "Sample Title", "references": [{"url": "https://www.somegreatwebsite.com/article/123", "title": "Title of the article we are referencing", "type": "article"}], "lang": "en", "beats": [{"text": "Today we're exploring a fascinating concept that has shaped some of the most innovative companies and leaders of our time: the Reality Distortion Field.", "image": {"type": "textSlide", "slide": {"title": "This is the title of the presentation"}}}, {"text": "This is a sample slide, which just displays the title and the presenter's name of this presentation.", "image": {"type": "textSlide", "slide": {"title": "This is the title of the presentation", "subtitle": "<PERSON>"}}}, {"text": "The evolution of humans is a complex journey that spans millions of years, shaped by biology, environment, and culture. Here's a high-level summary of the key stages in human evolution", "image": {"type": "textSlide", "slide": {"title": "Human Evolution", "bullets": ["Early Primates", "Hominids and Hominins", "Australopithecus", "Genus Homo Emerges", "Homo erectus and Migration", "Neanderthals and Other Archaic Humans", "Homo sapiens"]}}}, {"text": "This is a table of items in the store.", "image": {"type": "markdown", "markdown": ["# Markdown Table Example", "| Item              | In Stock | Price |", "| :---------------- | :------: | ----: |", "| Python Hat        |   True   | 23.99 |", "| SQL Hat           |   True   | 23.99 |", "| Codecademy Tee    |  False   | 19.99 |", "| Codecademy Hoodie |  False   | 42.99 |"]}}, {"text": "This page shows the sales and profits of this company from January 2024 to June 2024.", "image": {"type": "chart", "title": "Sales and Profits (from Jan to June)", "chartData": {"type": "bar", "data": {"labels": ["January", "February", "March", "April", "May", "June"], "datasets": [{"label": "Revenue ($1000s)", "data": [120, 135, 180, 155, 170, 190], "backgroundColor": "rgba(54, 162, 235, 0.5)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}, {"label": "Profit ($1000s)", "data": [45, 52, 68, 53, 61, 73], "backgroundColor": "rgba(75, 192, 192, 0.5)", "borderColor": "rgba(75, 192, 192, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "animation": false}}}}, {"text": "This is a sample pie chart", "image": {"type": "chart", "title": "A sample pie chart", "chartData": {"type": "pie", "data": {"labels": ["OpenAIと投資家の取り分", "マイクロソフトの取り分"], "datasets": [{"data": [90, 10], "backgroundColor": ["rgba(75, 192, 192, 0.5)", "rgba(54, 162, 235, 0.5)"], "borderColor": ["rgba(75, 192, 192, 1)", "rgba(54, 162, 235, 1)"], "borderWidth": 1}]}, "options": {"responsive": true, "animation": false, "plugins": {"legend": {"position": "bottom"}}}}}}, {"text": "Next, let's look at a diagram of our business process flow. This illustrates the key steps from product development to sales.", "image": {"type": "mermaid", "title": "Business Process Flow", "code": {"kind": "text", "text": "graph LR\n    A[Market Research] --> B[Product Planning]\n    B --> C[Development]\n    C --> D[Testing]\n    D --> E[Manufacturing]\n    E --> F[Marketing]\n    F --> G[Sales]\n    G --> H[Customer Support]\n    H --> A"}}}, {"text": "This is a tailwind html format.", "image": {"type": "html_tailwind", "html": ["<main class=\"flex-grow\">", "  <!-- Hero Section -->", "  <section class=\"bg-blue-600 text-white py-20\">", "    <div class=\"container mx-auto px-6 text-center\">", "      <h1 class=\"text-4xl md:text-5xl font-bold mb-4\">Welcome to Mulmocast</h1>", "      <p class=\"text-lg md:text-xl mb-8\">A modern web experience powered by Tailwind CSS</p>", "      <a href=\"#features\" class=\"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold shadow hover:bg-gray-100 transition\">", "        Learn More", "      </a>", "    </div>", "  </section>", "", "  <!-- Features Section -->", "  <section id=\"features\" class=\"py-16 bg-gray-100\">", "    <div class=\"container mx-auto px-6\">", "      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\">", "        <div>", "          <div class=\"text-blue-600 text-4xl mb-2\">⚡</div>", "          <h3 class=\"text-xl font-semibold mb-2\">Fast</h3>", "          <p class=\"text-gray-600\">Built with performance in mind using modern tools.</p>", "        </div>", "        <div>", "          <div class=\"text-blue-600 text-4xl mb-2\">🎨</div>", "          <h3 class=\"text-xl font-semibold mb-2\">Beautiful</h3>", "          <p class=\"text-gray-600\">Styled with Tailwind CSS for clean, responsive design.</p>", "        </div>", "        <div>", "          <div class=\"text-blue-600 text-4xl mb-2\">🚀</div>", "          <h3 class=\"text-xl font-semibold mb-2\">Launch Ready</h3>", "          <p class=\"text-gray-600\">Easy to deploy and extend for your next big idea.</p>", "        </div>", "      </div>", "    </div>", "  </section>", "</main>", "", "<!-- Footer -->", "<footer class=\"bg-white text-gray-500 text-center py-6 border-t\">", "  2025 Mulmocast.", "</footer>"]}}, {"text": "This is the image of the future of enterprise applications.", "image": {"type": "image", "source": {"kind": "url", "url": "https://satoshi.blogs.com/mag2/May2025/enterprise_app.png"}}}]}