{"$mulmocast": {"version": "1.1"}, "movieParams": {"provider": "replicate", "model": "bytedance/seedance-1-lite"}, "audioParams": {"bgmVolume": 0.01, "suppressSpeech": true}, "captionParams": {"lang": "en"}, "lang": "en", "beats": [{"id": "horse_galloping", "text": "A horse galloping", "duration": 10, "imagePrompt": "A wide view of a horse galloping fast in a large field", "moviePrompt": "A horse galloping fast in a large field", "soundEffectPrompt": "galloping"}, {"id": "cat_jumping_into_pool", "text": "cat jumping into a swimming pool", "duration": 10, "imagePrompt": "A televised view of a cat is on a 10m high jumping board, preparing to jump into a swimming pool at the olympics", "moviePrompt": "televised footage of a cat is doing an acrobatic dive into a swimming pool at the olympics, from a 10m high diving board, flips and spins", "soundEffectPrompt": "sound of a cat jumping into a swimming pool"}, {"id": "ice cutting", "text": "Cutting ice with a sharp knife", "duration": 10, "imagePrompt": "Close up of a sharp knife is ready to cut a slice out of a block ofice", "moviePrompt": "Close up of a sharp knife is cutting a slice out of a block of ice", "soundEffectPrompt": "cutting ice"}]}