# リリースノート v1.1.4

## 今回のリリースに含まれる Pull Request

--- 以下、Generated by Claude Code ---

## What's Changed

1. PR #740: template2tsobject - @isamu

Full Changelog: https://github.com/receptron/mulmocast-cli/compare/1.1.3...1.1.4

## Pull Request Summaries

### PR #740: template2tsobject - @isamu (https://github.com/receptron/mulmocast-cli/pull/740)
- **English**: Refactored template data generation and naming conventions for better consistency. Changed the exported constant name from `scriptTemplates` to `templateDataSet` in both the generation script (batch/template2tsobject.ts) and generated file (src/data/templateDataSet.ts). Enhanced the template2tsobject batch script by adding `maxStringLength: null` option to prevent truncation of long template strings. Improved the build process by integrating formatting into the template generation step (updated package.json to run `yarn run format` after template generation) and removed redundant format step from GitHub Actions workflow. Updated prompt templates formatting with consistent double-quote usage and proper code formatting throughout src/data/promptTemplates.ts.
- **日本語**: より良い一貫性のためにテンプレートデータ生成と命名規則をリファクタリングしました。生成スクリプト（batch/template2tsobject.ts）と生成ファイル（src/data/templateDataSet.ts）の両方でエクスポートされる定数名を`scriptTemplates`から`templateDataSet`に変更しました。長いテンプレート文字列の切り詰めを防ぐために`maxStringLength: null`オプションを追加してtemplate2tsobjectバッチスクリプトを強化しました。テンプレート生成ステップにフォーマットを統合し（package.jsonを更新してテンプレート生成後に`yarn run format`を実行）、GitHub Actionsワークフローから冗長なフォーマットステップを削除してビルドプロセスを改善しました。src/data/promptTemplates.ts全体で一貫したダブルクォート使用と適切なコードフォーマットでプロンプトテンプレートフォーマットを更新しました。

---

## Release Notes v1.1.4

### Developer Release Notes (English)

**Template System Improvements**
- **Naming Convention Standardization**: Renamed exported constant from `scriptTemplates` to `templateDataSet` for better consistency across the template system
- **Enhanced Template Generation**: Improved template2tsobject batch script with `maxStringLength: null` option to prevent truncation of long template strings
- **Build Process Optimization**: Streamlined build workflow by integrating code formatting into template generation step and removing redundant GitHub Actions format step

**Code Quality Enhancements**
- **Template Data Formatting**: Updated prompt templates with consistent double-quote usage and proper code formatting throughout src/data/promptTemplates.ts
- **Build Automation**: Automated formatting by adding `yarn run format` to template generation process in package.json

### Developer Release Notes (Japanese)

**テンプレートシステム改善**
- **命名規則の標準化**: テンプレートシステム全体でより良い一貫性のため、エクスポート定数を`scriptTemplates`から`templateDataSet`に変更
- **テンプレート生成の強化**: 長いテンプレート文字列の切り詰めを防ぐ`maxStringLength: null`オプションでtemplate2tsobjectバッチスクリプトを改善
- **ビルドプロセス最適化**: テンプレート生成ステップにコードフォーマットを統合し、冗長なGitHub Actionsフォーマットステップを削除してビルドワークフローを合理化

**コード品質向上**
- **テンプレートデータフォーマット**: src/data/promptTemplates.ts全体で一貫したダブルクォート使用と適切なコードフォーマットでプロンプトテンプレートを更新
- **ビルド自動化**: package.jsonでテンプレート生成プロセスに`yarn run format`を追加してフォーマットを自動化

### Creator Release Notes (English)

**System Improvements**
- **Better Template Consistency**: Improved internal template data handling for more consistent template generation and processing
- **Enhanced Build Reliability**: Streamlined template generation process ensures more reliable template availability during content creation

**Performance Optimization**
- **Template Generation**: Enhanced template processing to handle longer template strings without truncation, improving template quality
- **Build Process**: Optimized development workflow with automated formatting during template generation

### Creator Release Notes (Japanese)

**システム改善**
- **テンプレート一貫性の向上**: より一貫したテンプレート生成と処理のための内部テンプレートデータ処理を改善
- **ビルド信頼性の向上**: テンプレート生成プロセスを合理化し、コンテンツ作成中のより信頼できるテンプレート利用可能性を保証

**パフォーマンス最適化**
- **テンプレート生成**: 切り詰めなしでより長いテンプレート文字列を処理するようにテンプレート処理を強化し、テンプレート品質を向上
- **ビルドプロセス**: テンプレート生成中の自動フォーマットで開発ワークフローを最適化

---

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか
- [x] すべてのリンクがGitHubの完全URL（https://github.com/receptron/mulmocast-cli/blob/バージョン/パス）形式になっているか
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-30
チェック者: Claude Code