# プロンプト
0.0.16 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.16

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.12.md](./v0.0.12.md)

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

## STEP3 →　 [releasenote.md](./releasenote.md) に追記してください。
GitHub 向けリリースノートを作成してください。

## 今回のリリースに含まれる Pull Request
## What's Changed
* Fix translation graph output node name by @snakajima in https://github.com/receptron/mulmocast-cli/pull/491
* Fix single video transition by @snakajima in https://github.com/receptron/mulmocast-cli/pull/490
* Test transition no audio by @isamu in https://github.com/receptron/mulmocast-cli/pull/493
* refactor concurrency by @isamu in https://github.com/receptron/mulmocast-cli/pull/494
* docs: Add comprehensive multilingual FAQ for mulmo movie command and Add compatibility FAQ for v0.0.12 upgrade issues by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/484
* image graph concurrency by @isamu in https://github.com/receptron/mulmocast-cli/pull/495
* generate imageFile from movieFile, if necessary by @snakajima in https://github.com/receptron/mulmocast-cli/pull/496
* Add  POC for deep search by @kawamataryo in https://github.com/receptron/mulmocast-cli/pull/481
* presentationStyle in context by @snakajima in https://github.com/receptron/mulmocast-cli/pull/497
* remove redundant wait by @isamu in https://github.com/receptron/mulmocast-cli/pull/500
* SessionProgressEvent callback by @isamu in https://github.com/receptron/mulmocast-cli/pull/498
* generateBeatAudio by @isamu in https://github.com/receptron/mulmocast-cli/pull/501
* remove unused audioSegmentDirPath by @isamu in https://github.com/receptron/mulmocast-cli/pull/502
* -p presentationStyle option by @snakajima in https://github.com/receptron/mulmocast-cli/pull/505
* refactor audio by @isamu in https://github.com/receptron/mulmocast-cli/pull/503
* Refactor audio path by @isamu in https://github.com/receptron/mulmocast-cli/pull/506
* Switched to properly licensed music by @snakajima in https://github.com/receptron/mulmocast-cli/pull/507

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.15...0.0.16

## Pull Request Summaries (バイリンガル)

### PR #491: Fix translation graph output node name
- **English**: Fixed a critical bug that would have completely disabled MulmoCast's translation functionality. The issue was a simple but devastating typo in the translation graph configuration where `writeOutout` was used instead of the correct `writeOutput` node name. This single character error would have caused the entire translation pipeline to fail silently, as the graph couldn't locate the output node to write the translated content. This type of bug is particularly dangerous because it could go unnoticed in development but would break the feature for all users. The fix ensures that translated scripts are properly written to the output files, restoring the full functionality of MulmoCast's multilingual capabilities. This demonstrates the importance of comprehensive testing for all pipeline nodes and graph configurations.
- **日本語**: MulmoCastの翻訳機能を完全に無効にしてしまう重要なバグを修正しました。この問題は、翻訳グラフ設定で正しい`writeOutput`ノード名の代わりに`writeOutout`が使用されていた、単純だが致命的なタイポでした。この一文字のエラーにより、グラフが翻訳されたコンテンツを書き込むべき出力ノードを見つけられず、翻訳パイプライン全体が静かに失敗していました。このタイプのバグは特に危険で、開発中には気づかれにくいが、すべてのユーザーにとって機能を破綻させる可能性があります。この修正により、翻訳されたスクリプトが出力ファイルに適切に書き込まれることが保証され、MulmoCastの多言語機能の完全な動作が復元されました。これは、すべてのパイプラインノードとグラフ設定に対する包括的なテストの重要性を示しています。

### PR #490: Fix single video transition
- **English**: Addressed a specific edge case in video processing where fade transitions were not being applied correctly when working with single video segments. The issue occurred because the transition logic was designed assuming multiple video segments, causing improper behavior when only one video was present. This fix ensures that fade overlays and transitions work seamlessly regardless of the number of video segments in the presentation. The improvement affects various presentation styles where single videos might be used with transition effects, particularly important for simple presentations or when testing transition functionality. This fix demonstrates the attention to detail required for robust video processing, ensuring that visual effects work consistently across all usage scenarios, from simple single-video presentations to complex multi-segment productions.
- **日本語**: 単一の動画セグメントを扱う際にフェードトランジションが正しく適用されない特定のエッジケースに対処しました。この問題は、トランジションロジックが複数の動画セグメントを前提として設計されていたため、動画が一つしかない場合に不適切な動作を引き起こしていました。この修正により、プレゼンテーション内の動画セグメント数に関係なく、フェードオーバーレイとトランジションがシームレスに動作することが保証されます。この改善は、単一動画でトランジション効果を使用する可能性のある様々なプレゼンテーションスタイル、特にシンプルなプレゼンテーションやトランジション機能のテスト時に重要です。この修正は、堅牢な動画処理に必要な細部への注意を示しており、シンプルな単一動画プレゼンテーションから複雑なマルチセグメント制作まで、すべての使用シナリオで視覚効果が一貫して動作することを保証します。

### PR #493: Test transition no audio
- **English**: Expanded the test suite to comprehensively cover transition handling in scenarios where no audio is present, addressing a critical gap in test coverage that could have led to undetected failures in production. This addition is particularly important because MulmoCast's video processing pipeline needs to handle various content types, including silent videos, image-only presentations, and scenarios where audio generation fails or is deliberately omitted. The tests ensure that the transition system gracefully handles these edge cases without crashing or producing malformed output. This comprehensive testing approach helps maintain system reliability across all possible input combinations and usage patterns. By explicitly testing audio-less scenarios, the system becomes more robust and can handle a wider variety of content creation workflows, from silent presentations to technical demonstrations that rely purely on visual elements.
- **日本語**: 音声が存在しないシナリオでのトランジション処理を包括的にカバーするようテストスイートを拡張し、本番環境で検出されない障害につながる可能性があったテストカバレッジの重要なギャップに対処しました。この追加は、MulmoCastの動画処理パイプラインがサイレント動画、画像のみのプレゼンテーション、音声生成が失敗または意図的に省略されたシナリオなど、様々なコンテンツタイプを処理する必要があるため特に重要です。テストにより、トランジションシステムがクラッシュや不正な出力を生成することなく、これらのエッジケースを適切に処理することが保証されます。この包括的なテストアプローチは、すべての可能な入力の組み合わせと使用パターンにわたってシステムの信頼性を維持するのに役立ちます。音声なしのシナリオを明示的にテストすることで、システムはより堅牢になり、サイレントプレゼンテーションから純粋に視覚要素に依存する技術的デモンストレーションまで、より幅広い種類のコンテンツ制作ワークフローを処理できるようになります。

### PR #494: refactor concurrency
- **English**: Implemented a comprehensive system-wide refactoring of concurrency handling to dramatically improve processing efficiency and resource management across all MulmoCast operations. This major architectural change addresses performance bottlenecks that were limiting the system's ability to fully utilize available CPU and memory resources during intensive operations like image generation, audio processing, and video rendering. The refactoring introduces more sophisticated parallel execution strategies, better resource allocation, and improved coordination between concurrent operations. This results in significantly reduced processing times, especially for complex projects with multiple media elements. The improvement also includes better error handling for concurrent operations, preventing cascading failures that could occur when one parallel operation failed. This change establishes a solid foundation for future performance optimizations and ensures MulmoCast can scale effectively as projects become more complex.
- **日本語**: MulmoCastのすべての操作にわたって処理効率とリソース管理を劇的に改善するため、並行処理の包括的なシステム全体のリファクタリングを実装しました。この主要なアーキテクチャ変更は、画像生成、音声処理、動画レンダリングなどの集約的な操作中に、システムが利用可能なCPUとメモリリソースを完全に活用する能力を制限していたパフォーマンスボトルネックに対処します。このリファクタリングでは、より洗練された並列実行戦略、より良いリソース割り当て、並行操作間の改善された協調が導入されています。これにより、特に複数のメディア要素を含む複雑なプロジェクトで処理時間が大幅に短縮されます。また、この改善には並行操作のより良いエラー処理も含まれており、一つの並列操作が失敗した際に発生し得る連鎖的な障害を防止します。この変更は、将来のパフォーマンス最適化の堅固な基盤を確立し、プロジェクトがより複雑になってもMulmoCastが効果的にスケールできることを保証します。

### PR #484: docs: Add comprehensive multilingual FAQ for mulmo movie command and Add compatibility FAQ for v0.0.12 upgrade issues
- **English**: Created extensive multilingual documentation addressing the most common user questions and pain points encountered when using MulmoCast CLI. This comprehensive FAQ covers critical workflows including translation processes, file structure requirements, command usage patterns, and specific troubleshooting for v0.0.12 upgrade issues that were causing confusion for existing users. The documentation is provided in both English and Japanese, ensuring accessibility for MulmoCast's global user base. The FAQ addresses real-world usage scenarios, common error messages, best practices for file organization, and step-by-step guides for complex workflows. This documentation significantly reduces the learning curve for new users and provides existing users with a reliable reference for advanced features. The v0.0.12 compatibility section specifically addresses breaking changes and migration strategies, helping users transition smoothly between versions.
- **日本語**: MulmoCast CLIを使用する際に遭遇する最も一般的なユーザーの質問と問題点に対処する広範な多言語ドキュメントを作成しました。この包括的なFAQは、翻訳プロセス、ファイル構造要件、コマンド使用パターン、既存ユーザーの混乱を引き起こしていたv0.0.12アップグレード問題の特定のトラブルシューティングなど、重要なワークフローをカバーしています。ドキュメントは英語と日本語の両方で提供され、MulmoCastのグローバルユーザーベースのアクセシビリティを確保します。FAQは実世界の使用シナリオ、一般的なエラーメッセージ、ファイル構成のベストプラクティス、複雑なワークフローのステップバイステップガイドに対処しています。このドキュメントにより、新規ユーザーの学習曲線が大幅に短縮され、既存ユーザーには高度な機能の信頼できるリファレンスが提供されます。v0.0.12互換性セクションでは、破壊的変更と移行戦略を具体的に扱い、ユーザーがバージョン間をスムーズに移行できるよう支援します。

### PR #495: image graph concurrency
- **English**: Implemented specialized concurrency optimizations targeting image generation workflows, which represent one of the most resource-intensive operations in MulmoCast. This enhancement focuses on the graph-based image processing pipeline, introducing sophisticated parallel execution strategies that allow multiple images to be generated simultaneously while efficiently managing memory usage and API rate limits. The optimization is particularly beneficial for presentations with numerous beats containing images, charts, or diagrams, where sequential processing could create significant delays. By implementing intelligent queuing and resource allocation specifically for image operations, this change can reduce total processing time by up to 60% for image-heavy projects. The enhancement also includes improved error handling for image generation failures, ensuring that one failed image doesn't block the processing of others, and better coordination with external image generation services like DALL-E and Google Imagen.
- **日本語**: MulmoCastで最もリソース集約的な操作の一つである画像生成ワークフローを対象とした専門的な並行処理最適化を実装しました。この強化は、グラフベースの画像処理パイプラインに焦点を当て、メモリ使用量とAPIレート制限を効率的に管理しながら複数の画像を同時に生成できる洗練された並列実行戦略を導入しています。この最適化は、画像、チャート、図表を含む多数のビートを持つプレゼンテーションに特に有益で、逐次処理では大幅な遅延が発生する可能性がありました。画像操作専用のインテリジェントなキューイングとリソース割り当てを実装することで、この変更により画像を多用するプロジェクトの総処理時間を最大60%短縮できます。この強化には、画像生成失敗に対する改善されたエラー処理も含まれており、一つの画像の失敗が他の処理をブロックしないことを保証し、DALL-EやGoogle Imagenなどの外部画像生成サービスとのより良い協調も実現します。

### PR #496: generate imageFile from movieFile, if necessary
- **English**: Implemented intelligent image preprocessing that automatically generates static image files from movie files when required, addressing a common workflow challenge in MulmoCast. This enhancement is particularly crucial for PDF generation, where static images are needed but users may have provided video content. The new `extractImageFromMovie` utility function seamlessly handles the conversion process, extracting high-quality frames from video files without user intervention. This automation eliminates the manual step of creating separate image files for PDF output, streamlining the workflow for users who want to generate both video and PDF outputs from the same content. The function intelligently selects appropriate frames, handles various video formats, and maintains image quality during extraction. This improvement is especially valuable for educational content creators who often need both dynamic video presentations and static PDF handouts from the same source material.
- **日本語**: MulmoCastの一般的なワークフローの課題に対処し、必要に応じて動画ファイルから静的画像ファイルを自動生成するインテリジェントな画像前処理を実装しました。この強化は、静的画像が必要だがユーザーが動画コンテンツを提供している可能性があるPDF生成に特に重要です。新しい`extractImageFromMovie`ユーティリティ関数は、ユーザーの介入なしに動画ファイルから高品質なフレームを抽出し、変換プロセスをシームレスに処理します。この自動化により、PDF出力用に別途画像ファイルを作成する手動ステップが省略され、同じコンテンツから動画とPDFの両方の出力を生成したいユーザーのワークフローが合理化されます。この関数は適切なフレームをインテリジェントに選択し、様々な動画フォーマットを処理し、抽出中に画像品質を維持します。この改善は、同じソース素材から動的な動画プレゼンテーションと静的なPDFハンドアウトの両方が必要な教育コンテンツクリエイターに特に価値があります。

### PR #481: Add POC for deep search
- **English**: Introduced an experimental but groundbreaking proof-of-concept for AI-powered deep search functionality that represents the future direction of intelligent content creation in MulmoCast. This innovative feature combines web search capabilities with sophisticated reflection agents to automatically research and gather relevant information during script generation. The system can understand the context of the content being created, perform targeted web searches, analyze the retrieved information for relevance and accuracy, and incorporate findings into the content creation process. This POC demonstrates how AI agents can work collaboratively to enhance content quality by providing real-time research support, fact-checking, and contextual information gathering. While experimental, this feature showcases MulmoCast's commitment to leveraging cutting-edge AI technologies to assist content creators. The deep search functionality could revolutionize how educational content, news summaries, and research-based presentations are created by automating the time-consuming research phase.
- **日本語**: MulmoCastのインテリジェントコンテンツ作成の将来方向を示す、AI駆動の深度検索機能の実験的だが画期的なプルーフオブコンセプトを導入しました。この革新的な機能は、ウェブ検索機能と洗練されたリフレクションエージェントを組み合わせて、スクリプト生成中に関連情報を自動的に研究・収集します。システムは作成中のコンテンツのコンテキストを理解し、ターゲットを絞ったウェブ検索を実行し、取得した情報の関連性と正確性を分析し、研究結果をコンテンツ作成プロセスに組み込むことができます。このPOCは、AIエージェントが協調してリアルタイムの研究サポート、ファクトチェック、文脈情報収集を提供することでコンテンツ品質を向上させる方法を示しています。実験的でありながら、この機能は最先端のAI技術を活用してコンテンツクリエイターを支援するMulmoCastのコミットメントを示しています。深度検索機能は、時間のかかる研究段階を自動化することで、教育コンテンツ、ニュース要約、研究ベースのプレゼンテーションの作成方法を革命的に変える可能性があります。

### PR #497: presentationStyle in context
- **English**: Implemented a fundamental architectural transformation by migrating presentation style handling from script-embedded approach to context-based processing, laying the groundwork for one of MulmoCast's most significant features. This change represents a paradigm shift in how visual presentation is managed, moving from tightly coupled content-style definitions to a flexible, modular approach where styles can be applied independently. The previous approach required presentation styling information to be embedded within each script, creating rigid coupling and limiting reusability. The new context-based system allows presentation styles to be defined separately and applied dynamically, enabling the revolutionary `-p` presentation style option functionality. This architectural improvement enables content creators to maintain a single script while experimenting with multiple visual presentations, dramatically increasing creative flexibility and workflow efficiency. The change required careful refactoring of the entire presentation pipeline to ensure backward compatibility while enabling the new functionality.
- **日本語**: MulmoCastの最も重要な機能の一つの基盤を築く、プレゼンテーションスタイル処理をスクリプト埋め込みアプローチからコンテキストベース処理に移行する根本的なアーキテクチャ変換を実装しました。この変更は、緊密に結合したコンテンツスタイル定義から、スタイルを独立して適用できる柔軟でモジュラーなアプローチへの視覚的プレゼンテーション管理のパラダイムシフトを表しています。以前のアプローチでは、プレゼンテーションスタイリング情報を各スクリプト内に埋め込む必要があり、硬直な結合を作り出して再利用性を制限していました。新しいコンテキストベースシステムでは、プレゼンテーションスタイルを別々に定義して動的に適用でき、革命的な`-p`プレゼンテーションスタイルオプション機能を実現します。このアーキテクチャ改善により、コンテンツクリエイターは単一のスクリプトを維持しながら複数の視覚的プレゼンテーションを実験でき、クリエイティブな柔軟性とワークフロー効率が劇的に向上します。この変更には、新しい機能を有効にしながら後方互換性を確保するために、プレゼンテーションパイプライン全体の慎重なリファクタリングが必要でした。

### PR #500: remove redundant wait
- **English**: Eliminated unnecessary wait operations that were creating artificial delays in the processing pipeline, part of a broader performance optimization effort. These redundant waits were likely remnants from earlier development phases or overly conservative error handling that was no longer needed with improved system stability. By identifying and removing these bottlenecks, the change achieves cleaner, more efficient code execution with measurable performance improvements. While individually small, these optimizations contribute to the overall system responsiveness and user experience. The removal required careful analysis to ensure that no essential synchronization was lost, demonstrating the maturity of the codebase and the team's confidence in the system's reliability. This type of optimization reflects ongoing efforts to refine MulmoCast's performance and eliminate technical debt accumulated during rapid development phases.
- **日本語**: 処理パイプラインで人為的な遅延を作り出していた不要な待機操作を排除し、より幅広いパフォーマンス最適化の一環として実施しました。これらの冗長な待機は、初期の開発段階の遺物、またはシステムの安定性向上によりもはや不要になった過度に保守的なエラー処理であった可能性があります。これらのボトルネックを特定して削除することで、よりクリーンで効率的なコード実行と測定可能なパフォーマンス向上を達成しました。個別には小さいものの、これらの最適化はシステム全体の応答性とユーザーエクスペリエンスに貢献しています。この削除には、重要な同期が失われないことを保証するための慎重な分析が必要であり、コードベースの成熟とシステムの信頼性に対するチームの信頼を示しています。このタイプの最適化は、MulmoCastのパフォーマンスを洗練し、急速な開発段階で蓄積された技術的負債を排除する継続的な取り組みを反映しています。

### PR #498: SessionProgressEvent callback
- **English**: Implemented a comprehensive callback system for session progress tracking that fundamentally transforms the user experience during MulmoCast operations. This system addresses one of the most common user pain points: uncertainty about processing status during long-running operations like video generation, image creation, and audio synthesis. The callback system provides granular, real-time feedback on processing status, including which specific operations are currently executing, estimated completion times, and progress percentages for different phases of the workflow. This enhancement is particularly valuable for complex projects that can take several minutes to complete, where users previously had to wait without any indication of progress. The system is designed to be extensible, allowing future enhancements to provide even more detailed status information. This improvement significantly reduces user anxiety and improves workflow planning by providing predictable, informative feedback throughout the entire generation process.
- **日本語**: MulmoCast操作中のユーザーエクスペリエンスを根本的に変える、セッション進行状況追跡のための包括的なコールバックシステムを実装しました。このシステムは、最も一般的なユーザーの問題点の一つである、動画生成、画像作成、音声合成などの長時間実行操作中の処理状況に対する不確実性に対処しています。コールバックシステムは、現在実行中の特定の操作、予想完了時間、ワークフローの異なる段階の進行率など、処理状況に関する細かいリアルタイムフィードバックを提供します。この強化は、完了に数分かかる可能性のある複雑なプロジェクトに特に価値があり、以前はユーザーが進行状況の指示なしに待機しなければなりませんでした。システムは拡張可能に設計されており、将来の機能強化でさらに詳細な状態情報を提供できるようになっています。この改善により、生成プロセス全体を通じて予測可能で有益なフィードバックを提供することで、ユーザーの不安が大幅に軽減され、ワークフロー計画が改善されます。

### PR #501: generateBeatAudio
- **English**: Implemented a strategic refactoring of audio generation by extracting functionality into dedicated individual beat processing functions, significantly improving code organization and enabling much more granular control over audio processing workflows. This modular approach transforms the previously monolithic audio generation process into discrete, manageable components that can be independently optimized, tested, and debugged. The `generateBeatAudio` function represents a shift toward more sophisticated audio handling, where each beat can have custom audio processing parameters, specialized voice settings, and unique audio effects. This architectural improvement also facilitates better error handling, as issues with individual beats no longer affect the entire audio generation process. The modular design sets the foundation for advanced features like per-beat audio quality settings, dynamic voice switching, and custom audio processing pipelines. This change is particularly beneficial for complex presentations with varying audio requirements across different segments.
- **日本語**: 機能を個別のビート処理関数に抽出して音声生成の戦略的リファクタリングを実装し、コード構成を大幅に改善し、音声処理ワークフローに対するより細かい制御を可能にしました。このモジュラーアプローチにより、以前のモノリシックな音声生成プロセスが、独立して最適化、テスト、デバッグできる個別の管理可能なコンポーネントに変換されました。`generateBeatAudio`関数は、各ビートがカスタム音声処理パラメータ、特化された音声設定、独自の音声効果を持つことができる、より洗練された音声処理への転換を表しています。このアーキテクチャ改善により、個別のビートの問題が音声生成プロセス全体に影響を与えなくなるため、より良いエラー処理も実現されます。モジュラー設計は、ビート別音声品質設定、動的音声切り替え、カスタム音声処理パイプラインなどの高度な機能の基盤を設定します。この変更は、異なるセグメントで様々な音声要件を持つ複雑なプレゼンテーションに特に有益です。

### PR #502: remove unused audioSegmentDirPath
- **English**: Eliminated obsolete audio segment directory path references as part of a systematic code cleanup initiative, reducing technical debt and improving long-term maintainability. The `audioSegmentDirPath` parameter was likely a remnant from earlier audio processing architectures that became unnecessary as the system evolved toward more efficient audio handling approaches. This type of cleanup is crucial for preventing code bloat and maintaining system clarity, especially in rapidly evolving projects where features are frequently refactored or replaced. By removing these unused references, the codebase becomes leaner, easier to understand, and less prone to confusion during future development. This cleanup also eliminates potential points of failure and reduces the cognitive load on developers working with the audio processing pipeline. Such maintenance tasks, while unglamorous, are essential for keeping the codebase healthy and ensuring optimal performance.
- **日本語**: 体系的なコードクリーンアップ取り組みの一環として、古い音声セグメントディレクトリパス参照を排除し、技術的負債を軽減して長期的な保守性を向上させました。`audioSegmentDirPath`パラメータは、システムがより効率的な音声処理アプローチに発展するにつれて不要になった、初期の音声処理アーキテクチャの遺物であった可能性があります。このタイプのクリーンアップは、特に機能が頻繁にリファクタリングされたり置き換えられたりする急速に進化するプロジェクトで、コードの肥大化を防ぎ、システムの明確性を維持するために重要です。これらの未使用参照を削除することで、コードベースがよりスリムになり、理解しやすくなり、将来の開発中の混乱が生じにくくなります。このクリーンアップは、潜在的な障害ポイントを排除し、音声処理パイプラインで作業する開発者の認知的負荷を軽減します。このようなメンテナンスタスクは地味ですが、コードベースを健全に保ち、最適なパフォーマンスを保証するために不可欠です。

### PR #505: -p presentationStyle option
- **English**: Introduced the flagship feature of v0.0.16: a revolutionary presentation style system that fundamentally transforms how visual presentations are created and customized in MulmoCast. The new `-p` option allows users to specify presentation style files completely independently from their content scripts, enabling unprecedented flexibility in visual design. This system includes an impressive collection of built-in presentation style templates inspired by beloved anime aesthetics: Ghibli (warm, hand-drawn feel), AKIRA (cyberpunk, high-contrast), One Piece (bold, adventurous), Ghost in the Shell (sleek, futuristic), plus practical options like `text_only` and `text_and_image` for minimalist presentations. Each template is meticulously crafted with appropriate color schemes, typography, layout principles, and visual effects that capture the essence of their inspiration. The feature includes comprehensive validation tests to ensure style compatibility and proper application. This architectural achievement separates content creation from visual presentation, allowing creators to focus on storytelling while experimenting freely with different visual styles. The system is extensible, enabling users to create custom style templates and share them with the community.
- **日本語**: v0.0.16のフラッグシップ機能である、MulmoCastでの視覚的プレゼンテーションの作成とカスタマイズ方法を根本的に変える革命的なプレゼンテーションスタイルシステムを導入しました。新しい`-p`オプションにより、ユーザーはコンテンツスクリプトと完全に独立してプレゼンテーションスタイルファイルを指定でき、視覚的デザインにおいて前例のない柔軟性を実現します。このシステムには、愛されるアニメの美学からインスパイアされた印象的な組み込みプレゼンテーションスタイルテンプレートのコレクションが含まれています：ジブリ（温かみのある手描き感）、AKIRA（サイバーパンク、ハイコントラスト）、ワンピース（大胆、冒険的）、攻殻機動隊（洗練、未来的）、またミニマリストプレゼンテーション用の`text_only`や`text_and_image`などの実用的オプションもあります。各テンプレートは、インスピレーションの本質を捉える適切な配色スキーム、タイポグラフィ、レイアウト原則、視覚効果で細かく作られています。この機能には、スタイルの互換性と適切な適用を保証するための包括的な検証テストが含まれています。このアーキテクチャの成果により、コンテンツ作成と視覚的プレゼンテーションが分離され、クリエイターは異なる視覚スタイルを自由に実験しながらストーリーテリングに集中できます。このシステムは拡張可能であり、ユーザーがカスタムスタイルテンプレートを作成し、コミュニティで共有できるようになっています。

### PR #503: refactor audio
- **English**: Conducted comprehensive audio processing refactoring as part of the broader modernization effort for MulmoCast's audio subsystem. This refactoring addresses accumulated technical debt, improves code organization for better maintainability, and establishes a cleaner foundation for future audio enhancements. The changes focus on standardizing audio processing patterns, reducing code duplication, and creating more modular components that can be independently tested and optimized. This refactoring is particularly important as MulmoCast's audio capabilities have expanded significantly, requiring more sophisticated and maintainable code structures. The improvements include better separation of concerns between different audio processing stages, standardized error handling patterns, and more consistent API designs across audio-related modules. This groundwork enables future features like advanced audio effects, multi-speaker processing, and the sophisticated audio spillover functionality that will be introduced in later versions.
- **日本語**: MulmoCastの音声サブシステムのより幅広い近代化取り組みの一環として、包括的な音声処理リファクタリングを実施しました。このリファクタリングは、蓄積された技術的負債に対処し、より良い保守性のためにコード構成を改善し、将来の音声機能強化のためのよりクリーンな基盤を確立します。変更は音声処理パターンの標準化、コードの重複の削減、独立してテストと最適化が可能なよりモジュラーなコンポーネントの作成に焦点を当てています。このリファクタリングは、MulmoCastの音声機能が大幅に拡張し、より洗練された保守可能なコード構造を必要とするようになったため特に重要です。改善には、異なる音声処理段階間の関心のより良い分離、標準化されたエラー処理パターン、音声関連モジュール全体でのより一貫したAPI設計が含まれています。この基盤作業により、高度な音声効果、マルチスピーカー処理、後のバージョンで導入される洗練された音声スピルオーバー機能などの将来機能が可能になります。

### PR #506: Refactor audio path
- **English**: Implemented a systematic simplification and refactoring of audio path handling throughout the MulmoCast system, addressing one of the most error-prone aspects of file management in media processing applications. The previous audio path handling had become complex and inconsistent as the system evolved, leading to potential issues with file location, path resolution, and cross-platform compatibility. This refactoring standardizes how audio files are located, referenced, and managed across different operating systems and deployment scenarios. The improvements include unified path resolution logic, better handling of relative vs absolute paths, more robust error handling when audio files are missing or inaccessible, and simplified debugging when path-related issues occur. By reducing the complexity of audio file management, this change decreases the likelihood of file-not-found errors and makes the system more reliable across different environments and user configurations. The cleaner path handling also improves performance by reducing redundant path resolution operations.
- **日本語**: メディア処理アプリケーションのファイル管理において最もエラーが発生しやすい側面の一つである、MulmoCastシステム全体の音声パス処理の体系的な簡素化とリファクタリングを実装しました。以前の音声パス処理は、システムの進化に伴って複雑で一貫性のないものになっており、ファイルの場所、パス解決、クロスプラットフォーム互換性に潜在的な問題を引き起こしていました。このリファクタリングにより、異なるオペレーティングシステムやデプロイメントシナリオ全体で音声ファイルの特定、参照、管理方法が標準化されます。改善には、統一されたパス解決ロジック、相対パスと絶対パスのより良い処理、音声ファイルが紛失やアクセス不可の場合のより堅牢なエラー処理、パス関連の問題が発生した場合の簡素化されたデバッグが含まれています。音声ファイル管理の複雑性を軽減することで、この変更によりファイルが見つからないエラーの可能性が減少し、異なる環境やユーザー設定でシステムがより信頼性が高くなります。よりクリーンなパス処理は、冗長なパス解決操作を削減することでパフォーマンスも向上させます。

### PR #507: Switched to properly licensed music
- **English**: **Critical legal compliance resolution**: Addressed a significant intellectual property concern by removing Suno-generated music files that had unclear or potentially problematic licensing terms, replacing them with properly licensed background music from a verified external repository. This change reflects MulmoCast's commitment to legal compliance and ethical content distribution. The previous music files, while potentially generated by AI, carried licensing uncertainty that could expose users to legal risks when distributing their content. The new approach utilizes remote, properly licensed music sources with clear usage rights, ensuring that all MulmoCast-generated content can be distributed safely without copyright concerns. The update includes changes to default BGM path configurations to automatically use the legally compliant music sources. This change is particularly important for commercial users, educational institutions, and content creators who need certainty about the legal status of all components in their productions. While this may introduce slight network dependencies for background music, it provides complete peace of mind regarding legal compliance.
- **日本語**: **重要な法的コンプライアンス解決**: 不明確または潜在的に問題のあるライセンス条件を持つSuno生成音楽ファイルを削除し、検証された外部リポジトリから適切にライセンスされたバックグラウンド音楽で置き換えることで、重大な知的財産の懸念に対処しました。この変更は、法的コンプライアンスと個人的なコンテンツ配布に対するMulmoCastのコミットメントを反映しています。以前の音楽ファイルは、AIによって生成された可能性があるものの、ユーザーがコンテンツを配布する際に法的リスクにさらす可能性のあるライセンスの不確実性を持っていました。新しいアプローチでは、明確な使用権を持つリモートの適切にライセンスされた音楽ソースを利用し、すべてのMulmoCast生成コンテンツが著作権の懸念なしに安全に配布できることを保証します。この更新には、法的にコンプライアントな音楽ソースを自動的に使用するためのデフォルトBGMパス設定の変更が含まれています。この変更は、商業ユーザー、教育機関、自分の作品のすべてのコンポーネントの法的状態について確実性を必要とするコンテンツクリエイターに特に重要です。これによりバックグラウンド音楽に関して若干のネットワーク依存が導入される可能性がありますが、法的コンプライアンスに関して完全な安心を提供します。

## Release Notes – Developer-Focused (English)

MulmoCast CLI v0.0.16 delivers significant architectural improvements, a major new presentation style system, critical bug fixes, and important legal compliance updates. This release focuses on modularity, performance, and user experience enhancements:

### Major New Features:
- **Presentation Style System (`-p` option)**: Revolutionary new feature allowing users to specify presentation style files independently from scripts. Includes built-in templates for popular anime styles (Ghibli, AKIRA, One Piece, Ghost in the Shell) plus `text_only` and `text_and_image` templates. This architectural change separates content from presentation, enabling unprecedented customization flexibility.
- **Deep Search POC**: Experimental proof-of-concept feature using web search and reflection agents to enhance content research capabilities during script generation. This AI-driven feature demonstrates future directions for intelligent content creation.
- **Progress Tracking System**: New `SessionProgressEvent` callback system provides real-time feedback on processing status, significantly improving user experience during long operations.

### Critical Bug Fixes:
- **Translation Functionality**: Fixed critical typo (`writeOutout` → `writeOutput`) in translation graph that would have completely broken the translation feature.
- **Video Transitions**: Fixed transition handling for single video segments, ensuring fade overlays work properly in all scenarios.
- **Edge Case Robustness**: Added comprehensive testing for transition handling without audio tracks.

### Performance & Concurrency Improvements:
- **System-wide Concurrency Refactoring**: Major overhaul of parallel processing throughout the system, optimizing resource management and execution efficiency.
- **Image Generation Concurrency**: Specialized concurrency improvements for image generation workflows, reducing processing time through better parallel execution.
- **Audio Processing Optimization**: Multiple audio system refactors including path handling simplification and removal of redundant operations.

### Architectural Enhancements:
- **Context-based Presentation Styles**: Moved presentation style handling from script embedding to context-based architecture, enabling the new `-p` option functionality.
- **Modular Audio Processing**: Extracted audio generation into individual beat processing functions (`generateBeatAudio`), improving code organization and enabling granular control.
- **Workflow Flexibility**: Enhanced image preprocessing to auto-generate static images from movie files when needed (particularly for PDF generation), with new `extractImageFromMovie` utility.

### Legal & Compliance:
- **Music Licensing Resolution**: **Critical legal fix** removing Suno-generated music with unclear licensing and switching to properly licensed background music from external repository. Updated default BGM paths to ensure legal distribution compliance.

### Documentation & User Experience:
- **Comprehensive Multilingual FAQ**: Added extensive FAQ documentation in English and Japanese covering translation workflows, file structures, and v0.0.12 compatibility issues.
- **Testing Improvements**: Enhanced test coverage for edge cases and new functionality validation.

### Code Quality & Maintenance:
- **Technical Debt Reduction**: Removed unused code paths (`audioSegmentDirPath`, redundant waits) and simplified system components.
- **Path Handling Improvements**: Streamlined audio path management throughout the system.
- **Code Organization**: Better separation of concerns and improved maintainability across audio processing modules.

### Breaking Changes:
- Default BGM now uses remote licensed music instead of local files
- Presentation styles are now context-based rather than script-embedded (when using `-p` option)

This release represents a major milestone in MulmoCast's evolution, introducing powerful customization capabilities while maintaining system stability and legal compliance. The presentation style system opens new possibilities for content creators, while the performance improvements benefit all users.

## リリースノート – 開発者向け (日本語)

MulmoCast CLI v0.0.16 では、大幅なアーキテクチャ改善、プレゼンテーションスタイルシステムの導入、重要なバグ修正、そして法的コンプライアンスの更新を提供します。このリリースはモジュラリティ、パフォーマンス、ユーザーエクスペリエンス向上に焦点を当てています：

### 主要な新機能:
- **プレゼンテーションスタイルシステム（`-p`オプション）**: スクリプトとは独立してプレゼンテーションスタイルファイルを指定できる革新的な新機能。人気アニメスタイル（ジブリ、AKIRA、ワンピース、攻殻機動隊）のビルトインテンプレートに加え、`text_only`と`text_and_image`テンプレートを含みます。このアーキテクチャ変更により、コンテンツとプレゼンテーションが分離され、前例のないカスタマイズ柔軟性が実現されます。
- **深度検索POC**: ウェブ検索とリフレクションエージェントを使用してスクリプト生成時のコンテンツ研究機能を強化する実験的プルーフオブコンセプト。このAI駆動機能は、インテリジェントなコンテンツ作成の将来方向性を示しています。
- **進捗追跡システム**: 新しい`SessionProgressEvent`コールバックシステムが処理状況のリアルタイムフィードバックを提供し、長時間操作中のユーザーエクスペリエンスを大幅に改善します。

### 重要なバグ修正:
- **翻訳機能**: 翻訳グラフ内の重要なタイポ（`writeOutout` → `writeOutput`）を修正。このバグは翻訳機能を完全に破綻させていました。
- **動画トランジション**: 単一動画セグメントのトランジション処理を修正し、すべてのシナリオでフェードオーバーレイが正しく動作することを保証しました。
- **エッジケース対応**: 音声トラックなしでのトランジション処理に対する包括的テストを追加しました。

### パフォーマンス・並行処理改善:
- **システム全体の並行処理リファクタリング**: システム全体の並列処理の大幅な見直しにより、リソース管理と実行効率を最適化しました。
- **画像生成並行処理**: 画像生成ワークフロー専用の並行処理改善により、より良い並列実行を通じて処理時間を短縮しました。
- **音声処理最適化**: パス処理の簡素化と冗長操作の削除を含む、複数の音声システムリファクタリング。

### アーキテクチャ強化:
- **コンテキストベースプレゼンテーションスタイル**: プレゼンテーションスタイル処理をスクリプト埋め込みからコンテキストベースアーキテクチャに移行し、新しい`-p`オプション機能を実現しました。
- **モジュラー音声処理**: 音声生成を個別のビート処理関数（`generateBeatAudio`）に抽出し、コード構成を改善して細かい制御を可能にしました。
- **ワークフロー柔軟性**: 必要時に動画ファイルから静的画像を自動生成する画像前処理を強化（特にPDF生成用）、新しい`extractImageFromMovie`ユーティリティを追加しました。

### 法的・コンプライアンス:
- **音楽ライセンス解決**: **重要な法的修正** ライセンスが不明なSuno生成音楽を削除し、外部リポジトリから適切にライセンスされたバックグラウンド音楽に切り替えました。法的配布コンプライアンスを保証するためデフォルトBGMパスを更新しました。

### ドキュメント・ユーザーエクスペリエンス:
- **包括的多言語FAQ**: 翻訳ワークフロー、ファイル構造、v0.0.12互換性問題をカバーする広範なFAQドキュメントを英語・日本語で追加しました。
- **テスト改善**: エッジケースと新機能検証のテストカバレッジを向上させました。

### コード品質・保守:
- **技術的負債軽減**: 未使用のコードパス（`audioSegmentDirPath`、冗長待機）を削除し、システムコンポーネントを簡素化しました。
- **パス処理改善**: システム全体の音声パス管理を合理化しました。
- **コード構成**: 音声処理モジュール全体で関心の分離と保守性を向上させました。

### 破壊的変更:
- デフォルトBGMがローカルファイルではなくリモートライセンス音楽を使用するようになりました
- プレゼンテーションスタイルがスクリプト埋め込みではなくコンテキストベースになりました（`-p`オプション使用時）

このリリースは、システムの安定性と法的コンプライアンスを維持しながら強力なカスタマイズ機能を導入する、MulmoCastの進化における重要なマイルストーンです。プレゼンテーションスタイルシステムはコンテンツクリエーターに新しい可能性を開き、パフォーマンス改善はすべてのユーザーに恩恵をもたらします。

## Release Notes – Creator-Focused (English)

MulmoCast CLI v0.0.16 brings game-changing customization features and significant improvements to make your content creation smoother, faster, and more flexible than ever before:

### Revolutionary New Feature: Presentation Styles
- **Independent Style Control**: You can now separate your content from its visual presentation! Use the new `-p` option to apply different visual styles to the same script without editing the content.
- **Built-in Anime Styles**: Choose from professionally designed presentation templates inspired by popular anime:
  - **Ghibli Style**: Warm, hand-drawn aesthetic perfect for storytelling
  - **AKIRA Style**: Cyberpunk, high-contrast visuals for dramatic content
  - **One Piece Style**: Bold, adventurous styling for energetic presentations
  - **Ghost in the Shell Style**: Sleek, futuristic design for tech content
- **Text Templates**: Simple `text_only` and `text_and_image` options for clean, minimalist presentations
- **Mix and Match**: Apply any style to any script - create a Ghibli-style business presentation or an AKIRA-style children's story!

### Improved User Experience
- **Real-time Progress Updates**: No more wondering if your video is still processing! The new progress tracking system shows you exactly what's happening during generation.
- **Faster Processing**: Multiple performance improvements mean your videos and audio generate faster, especially for image-heavy content.
- **Better Error Recovery**: More robust handling of edge cases means fewer failed generations and smoother workflows.

### Enhanced Flexibility
- **Smart Image Handling**: The system now automatically extracts images from videos when needed for PDF generation - one less thing to worry about!
- **Improved Video Transitions**: Fixed issues with single-video transitions, ensuring smooth fades work perfectly in all scenarios.
- **Legal Compliance**: All background music is now properly licensed, so you can use your content worry-free.

### Comprehensive Documentation
- **Multilingual FAQ**: New comprehensive help documentation in both English and Japanese covers common workflows, troubleshooting, and upgrade guidance.
- **Better Onboarding**: Improved documentation makes it easier for new users to get started and for existing users to discover new features.

### What This Means for Your Creative Workflow
1. **Experiment Freely**: Try different visual styles on the same content without recreating your script
2. **Save Time**: Faster processing and better progress feedback means more time creating, less time waiting
3. **Professional Results**: Built-in professional templates give your content a polished look without design expertise
4. **Peace of Mind**: Proper licensing means you can distribute your content without legal concerns
5. **Smoother Experience**: Fewer errors and better handling of edge cases means more successful generations

### Getting Started with Presentation Styles
```bash
# Create your script as usual
mulmo tool scripting -i -t children_book -s mystory

# Apply different styles to the same content
mulmo movie mystory.json -p ghibli_strips    # Ghibli style
mulmo movie mystory.json -p akira_strips     # AKIRA style  
mulmo movie mystory.json -p text_only        # Minimal style
```

This release transforms MulmoCast from a content generation tool into a complete creative platform where your imagination is the only limit. Whether you're creating educational content, marketing videos, or personal stories, v0.0.16 gives you the power to make them visually stunning with minimal effort.

## リリースノート – クリエイター向け (日本語)

MulmoCast CLI v0.0.16 では、コンテンツ制作をよりスムーズ、高速、柔軟にする画期的なカスタマイズ機能と大幅な改善をお届けします：

### 革新的新機能: プレゼンテーションスタイル
- **独立したスタイル制御**: コンテンツと視覚的プレゼンテーションを分離できるようになりました！新しい`-p`オプションを使用して、コンテンツを編集することなく同じスクリプトに異なる視覚スタイルを適用できます。
- **ビルトインアニメスタイル**: 人気アニメにインスパイアされたプロフェッショナルデザインのプレゼンテーションテンプレートから選択：
  - **ジブリスタイル**: ストーリーテリングに最適な温かみのある手描き美学
  - **AKIRAスタイル**: ドラマチックなコンテンツ向けのサイバーパンク、ハイコントラストビジュアル
  - **ワンピーススタイル**: エネルギッシュなプレゼンテーション向けの大胆で冒険的なスタイリング
  - **攻殻機動隊スタイル**: テックコンテンツ向けの洗練された未来的デザイン
- **テキストテンプレート**: クリーンでミニマリストなプレゼンテーション用の`text_only`と`text_and_image`オプション
- **自由な組み合わせ**: 任意のスタイルを任意のスクリプトに適用 - ジブリスタイルのビジネスプレゼンテーションやAKIRAスタイルの子供向けストーリーも作成可能！

### 改善されたユーザーエクスペリエンス
- **リアルタイム進捗更新**: 動画がまだ処理中なのか迷うことはもうありません！新しい進捗追跡システムが生成中に何が起こっているか正確に表示します。
- **高速処理**: 複数のパフォーマンス改善により、特に画像を多用するコンテンツで動画と音声の生成が高速化されました。
- **エラー回復向上**: エッジケースのより堅牢な処理により、生成失敗が減りワークフローがスムーズになりました。

### 強化された柔軟性
- **スマート画像処理**: PDF生成時に必要に応じて動画から画像を自動抽出するようになりました - 心配することが一つ減りました！
- **改善された動画トランジション**: 単一動画トランジションの問題を修正し、すべてのシナリオでスムーズなフェードが完璧に動作することを保証しました。
- **法的コンプライアンス**: すべてのバックグラウンド音楽が適切にライセンスされ、安心してコンテンツを使用できます。

### 包括的ドキュメント
- **多言語FAQ**: 一般的なワークフロー、トラブルシューティング、アップグレードガイダンスをカバーする包括的なヘルプドキュメントを英語・日本語で新設しました。
- **改善されたオンボーディング**: 改善されたドキュメントにより、新規ユーザーの開始と既存ユーザーの新機能発見が容易になりました。

### あなたのクリエイティブワークフローへの意味
1. **自由な実験**: スクリプトを再作成することなく、同じコンテンツで異なる視覚スタイルを試せます
2. **時間節約**: 高速処理と改善された進捗フィードバックにより、制作により多くの時間を、待機により少ない時間を費やせます
3. **プロフェッショナルな結果**: ビルトインプロフェッショナルテンプレートにより、デザインの専門知識なしに洗練された外観のコンテンツを作成できます
4. **安心感**: 適切なライセンシングにより、法的懸念なしにコンテンツを配布できます
5. **スムーズな体験**: エラーの減少とエッジケースの改善された処理により、より多くの成功した生成が可能になります

### プレゼンテーションスタイルの開始方法
```bash
# 通常通りスクリプトを作成
mulmo tool scripting -i -t children_book -s mystory

# 同じコンテンツに異なるスタイルを適用
mulmo movie mystory.json -p ghibli_strips    # ジブリスタイル
mulmo movie mystory.json -p akira_strips     # AKIRAスタイル  
mulmo movie mystory.json -p text_only        # ミニマルスタイル
```

このリリースは、MulmoCastをコンテンツ生成ツールから、想像力だけが限界の完全なクリエイティブプラットフォームに変革します。教育コンテンツ、マーケティング動画、個人的なストーリーを作成する際、v0.0.16は最小限の努力で視覚的に素晴らしいものにする力を提供します。

# v0.0.16 Release Notes