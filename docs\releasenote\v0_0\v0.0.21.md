# プロンプト
0.0.21 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.21

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
要約の文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は v0.0.16 を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## 今回のリリースに含まれる Pull Request
## What's Changed
* Safety message by @snakajima in https://github.com/receptron/mulmocast-cli/pull/551
* add release info 0.0.18 to 0.0.20 by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/553
* Refactor type by @kawamataryo in https://github.com/receptron/mulmocast-cli/pull/552
* docs: add info about beat.imageNames by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/554
* docs: Add TTS configuration and 429 error troubleshooting to FAQ by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/555
* docs: update FAQ & add sound.md by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/556
* fix typo by @isamu in https://github.com/receptron/mulmocast-cli/pull/558

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.20...0.0.21

--- 以下、Generated by Claude Code --- 
## Pull Request Summaries

### [PR #551: Safety message](https://github.com/receptron/mulmocast-cli/pull/551)
**Author**: @snakajima

**English Summary**:
This PR improves error handling and visibility when image/movie generation fails due to safety reasons (content policy violations). The changes include reducing retry attempts for image generation from 3 to 2 (total 3 attempts), adding better error logging in both image and movie generation agents using GraphAILogger for clearer user feedback, and implementing try-catch blocks to properly handle and display safety-related errors. These improvements help users understand when their content requests are rejected due to content policy violations.

**日本語要約**:
画像・動画生成時にセーフティ（コンテンツポリシー違反）の理由で失敗した場合のエラー処理と表示を改善しました。画像生成のリトライ回数を3回から2回（合計3回の試行）に削減し、imageOpenaiAgentとmovieGoogleAgentの両方でGraphAILoggerを使用したより良いエラーログ出力を追加しました。try-catchブロックを実装してセーフティ関連のエラーを適切に処理・表示するようにし、ユーザーがコンテンツポリシー違反で拒否された理由を理解しやすくなりました。

### [PR #552: Refactor type](https://github.com/receptron/mulmocast-cli/pull/552)
**Author**: @kawamataryo

**English Summary**:
This PR is a type refactoring that improves code organization by moving session-related type definitions (SessionType, BeatSessionType, SessionProgressEvent, SessionProgressCallback) from src/methods/mulmo_studio_context.ts to the centralized src/types/type.ts file. Additionally, it adds these type exports to src/index.browser.ts to ensure availability in browser environments. This is purely a structural improvement with no functional changes.

**日本語要約**:
コードの構造を改善するための型定義のリファクタリングです。セッション関連の型定義（SessionType、BeatSessionType、SessionProgressEvent、SessionProgressCallback）をsrc/methods/mulmo_studio_context.tsから中央集約型のsrc/types/type.tsファイルに移動しました。さらに、ブラウザ環境でも利用可能にするため、src/index.browser.tsにこれらの型エクスポートを追加しました。機能的な変更はなく、純粋に構造的な改善です。

### [PR #553: add release info 0.0.18 to 0.0.20](https://github.com/receptron/mulmocast-cli/pull/553)
**Author**: @ystknsh

**English Summary**:
This PR adds comprehensive release documentation for MulmoCast CLI versions 0.0.18, 0.0.19, and 0.0.20. It updates docs/releasenote/index.md with concise GitHub-style release notes and creates three new detailed release note files (v0.0.18.md, v0.0.19.md, v0.0.20.md). Each file contains bilingual PR summaries and four types of release notes (Developer EN/JA, Creator EN/JA). Key features documented include bug fixes in v0.0.18, major audio/video features in v0.0.19, and library integration improvements in v0.0.20.

**日本語要約**:
MulmoCast CLIのバージョン0.0.18、0.0.19、0.0.20の包括的なリリースドキュメントを追加しました。docs/releasenote/index.mdにGitHubスタイルの簡潔なリリースノートを追加し、3つの新しい詳細なリリースノートファイル（v0.0.18.md、v0.0.19.md、v0.0.20.md）を作成しました。各ファイルにはバイリンガルのPR要約と4種類のリリースノート（開発者向け英語/日本語、クリエイター向け英語/日本語）が含まれています。主な機能として、v0.0.18のバグ修正、v0.0.19の主要な音声・動画機能、v0.0.20のライブラリ統合改善が文書化されています。

### [PR #554: docs: add info about beat.imageNames](https://github.com/receptron/mulmocast-cli/pull/554)
**Author**: @ystknsh

**English Summary**:
This PR adds 76 lines of documentation to docs/image.md explaining the beat.imageNames feature. The documentation covers how to use this feature for character control in multi-modal presentations, allowing selective display of pre-defined characters in specific scenes. It includes configuration examples, usage scenarios (showing teacher only, student only, or both), and technical workflow details about how the feature processes reference images for consistent character representation.

**日本語要約**:
docs/image.mdにbeat.imageNames機能を説明する76行のドキュメントを追加しました。このドキュメントでは、マルチモーダルプレゼンテーションでのキャラクター制御にこの機能を使用する方法を説明し、特定のシーンで事前定義されたキャラクターを選択的に表示できることを示しています。設定例、使用シナリオ（先生のみ、生徒のみ、または両方を表示）、一貫したキャラクター表現のための参照画像処理の技術的なワークフローの詳細が含まれています。

### [PR #555: docs: Add TTS configuration and 429 error troubleshooting to FAQ](https://github.com/receptron/mulmocast-cli/pull/555)
**Author**: @ystknsh

**English Summary**:
This PR enhances both English and Japanese FAQ documentation by adding comprehensive TTS configuration instructions and 429 error troubleshooting. It documents two methods for configuring TTS providers (global and per-speaker), provides JSON configuration examples, explains priority rules, and adds references to test files. Additionally, it creates a new Troubleshooting section addressing the common OpenAI 429 RateLimitError, recommending users upgrade to Tier 2 or higher for better rate limits.

**日本語要約**:
英語と日本語の両方のFAQドキュメントを強化し、包括的なTTS設定手順と429エラーのトラブルシューティングを追加しました。TTSプロバイダーの設定方法（グローバルおよびスピーカーごと）を文書化し、JSON設定例を提供し、優先順位ルールを説明し、テストファイルへの参照を追加しました。さらに、一般的なOpenAI 429 RateLimitErrorに対処する新しいトラブルシューティングセクションを作成し、より良いレート制限のためにTier 2以上へのアップグレードを推奨しています。

### [PR #556: docs: update FAQ & add sound.md](https://github.com/receptron/mulmocast-cli/pull/556)  
**Author**: @ystknsh

**English Summary**:
This PR improves documentation maintainability by removing explicit TTS provider lists from FAQ files and redirecting users to README.md's Configuration section. Additionally, it adds a new Japanese-language sound.md file documenting the audio spillover feature, which allows sharing a single audio narration across multiple beats. The documentation includes detailed examples of basic spillover, multiple spillover with custom durations, and automatic duration distribution, with references to test_spillover.json.

**日本語要約**:
このPRは、FAQファイルから明示的なTTSプロバイダーリストを削除し、ユーザーをREADME.mdのConfigurationセクションにリダイレクトすることで、ドキュメントの保守性を向上させました。さらに、複数のBeatで1つの音声ナレーションを共有できる音声スピルオーバー機能を説明する新しい日本語のsound.mdファイルを追加しました。ドキュメントには、基本的なスピルオーバー、カスタム期間を持つ複数のスピルオーバー、自動期間配分の詳細な例が含まれており、test_spillover.jsonへの参照があります。

### [PR #558: fix typo](https://github.com/receptron/mulmocast-cli/pull/558)
**Author**: @isamu

**English Summary**:
A simple typo fix in src/agents/combine_audio_files_agent.ts that corrects the misspelled property name "hasMadia" to "hasMedia" in two locations (lines 44 and 88). This ensures consistency in the codebase for the property that checks whether a beat has audio or video content.

**日本語要約**:
src/agents/combine_audio_files_agent.tsの単純なタイポ修正で、2箇所（44行目と88行目）でスペルミスのプロパティ名「hasMadia」を「hasMedia」に修正しました。これにより、ビートが音声または動画コンテンツを持っているかをチェックするプロパティのコードベース全体での一貫性が確保されます。

## Release Notes

### For Developers (English)

**MulmoCast CLI v0.0.21** brings important improvements focused on documentation and error handling.

**Error Handling Improvements**:
- Enhanced visibility of content policy violations when image/movie generation fails (PR #551)
- Reduced retry attempts for image generation to prevent excessive API calls
- Clearer error messages using GraphAILogger for better debugging

**Documentation Enhancements**:
- Added comprehensive documentation for `beat.imageNames` feature for character control (PR #554)
- Enhanced FAQ with TTS configuration guides and 429 error troubleshooting (PR #555)
- Added new documentation for audio spillover feature (PR #556)
- Completed release notes for versions 0.0.18-0.0.20 (PR #553)

**Code Quality**:
- Refactored session types to centralized location for better organization (PR #552)
- Fixed typo in audio combination agent (PR #558)

This release significantly improves the developer experience through better documentation and clearer error messages.

### 開発者向け（日本語）

**MulmoCast CLI v0.0.21** はドキュメントとエラー処理に焦点を当てた重要な改善をもたらします。

**エラー処理の改善**：
- 画像・動画生成失敗時のコンテンツポリシー違反の可視性を向上（PR #551）
- 過度なAPI呼び出しを防ぐため、画像生成のリトライ回数を削減
- GraphAILoggerを使用したより明確なエラーメッセージでデバッグを改善

**ドキュメントの強化**：
- キャラクター制御のための`beat.imageNames`機能の包括的なドキュメントを追加（PR #554）
- TTS設定ガイドと429エラーのトラブルシューティングでFAQを強化（PR #555）
- 音声スピルオーバー機能の新しいドキュメントを追加（PR #556）
- バージョン0.0.18-0.0.20のリリースノートを完成（PR #553）

**コード品質**：
- より良い整理のためにセッション型を中央集約型の場所にリファクタリング（PR #552）
- 音声結合エージェントのタイポを修正（PR #558）

このリリースは、より良いドキュメントとより明確なエラーメッセージを通じて開発者体験を大幅に改善します。

### For Creators (English)

**MulmoCast CLI v0.0.21** makes your content creation experience smoother and more reliable.

**Better Error Messages**:
- When your content is rejected for safety reasons, you'll now see clearer explanations
- Less confusing error messages when something goes wrong

**New Documentation**:
- Learn how to control which characters appear in each scene of your presentation
- Discover how to share one narration across multiple slides (audio spillover)
- Find solutions for the common "429 rate limit" error
- Clear guides on configuring different voice providers

**Improved Stability**:
- Fixed various small bugs for more reliable content generation

This update focuses on making MulmoCast easier to understand and use through better documentation and clearer error messages.

### クリエイター向け（日本語）

**MulmoCast CLI v0.0.21** はコンテンツ作成体験をよりスムーズで信頼性の高いものにします。

**より良いエラーメッセージ**：
- セーフティ上の理由でコンテンツが拒否された場合、より明確な説明が表示されます
- エラー発生時の混乱を招くメッセージを削減

**新しいドキュメント**：
- プレゼンテーションの各シーンに登場するキャラクターを制御する方法を学べます
- 複数のスライドで1つのナレーションを共有する方法（音声スピルオーバー）を発見
- よくある「429レート制限」エラーの解決策を見つけられます
- 異なる音声プロバイダーの設定に関する明確なガイド

**安定性の向上**：
- より信頼性の高いコンテンツ生成のために様々な小さなバグを修正

このアップデートは、より良いドキュメントとより明確なエラーメッセージを通じて、MulmoCastをより理解しやすく使いやすくすることに焦点を当てています。