flowchart TD
    S_WebPage["Web-Page"] --> MulmoStoryboard(["MulmoStoryboard"])
    S_PDF["PDF"] --> MulmoStoryboard
    S_AIChat["AI Chat"] --> MulmoStoryboard
    S_TextEditor["Text-Editor"] --> MulmoStoryboard
    MulmoStoryboard --> MulmoScript(["MulmoScript"])
    S_Markdown("Markdown") --> MulmoScript
    S_Keynote("Keynote") --> MulmoScript
    S_Powerpoint("Powerpoint") --> MulmoScript
    S_AIChat2("AI Chat") --> MulmoScript
    S_TextEditor2("Text-Editor") --> MulmoScript
    MulmoScript --> MulmoCast["MulmoCast"]
    MulmoCast --> O_Video("Video") & O_Podcast("Podcast") & O_Slideshow("Slide-Show") & O_PDF("PDF") & O_Manga("Manga") & O_SwipeAnime("Swipe Anime")

    MulmoCast@{ shape: hex}
     S_WebPage:::StoryInput
     S_PDF:::StoryInput
     S_AIChat:::StoryInput
     S_TextEditor:::StoryInput
     S_Markdown:::ScriptInput
     S_Keynote:::ScriptInput
     S_Powerpoint:::ScriptInput
     S_AIChat2:::ScriptInput
     S_TextEditor2:::ScriptInput
     O_Video:::ScriptOutput
     O_Podcast:::ScriptOutput
     O_Slideshow:::ScriptOutput
     O_PDF:::ScriptOutput
     O_Manga:::ScriptOutput
     O_SwipeAnime:::ScriptOutput
    classDef StoryInput stroke-width:1px, stroke-dasharray:none, stroke:#46EDC8, fill:#DEFFF8, color:#378E7A
    classDef ScriptInput stroke-width:1px, stroke-dasharray:none, stroke:#374D7C, fill:#E2EBFF, color:#374D7C
    classDef ScriptOutput stroke-width:1px, stroke-dasharray:none, stroke:#FBB35A, fill:#FFEFDB, color:#8F632D

