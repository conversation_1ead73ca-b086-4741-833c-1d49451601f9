# プロンプト
0.0.24 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.24

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
絵文字は使わないでください

## STEP1 →　 このファイルに追記してください。
すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
要約の文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。

## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は v0.0.16 を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

## 今回のリリースに含まれる Pull Request
## What's Changed
* update TaskManager import path by @isamu in https://github.com/receptron/mulmocast-cli/pull/577
* settings2GraphAIConfig by @isamu in https://github.com/receptron/mulmocast-cli/pull/578
* Caption styles by @snakajima in https://github.com/receptron/mulmocast-cli/pull/579
* htmlImageSystemPrompt by @isamu in https://github.com/receptron/mulmocast-cli/pull/580


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.23...0.0.24

--- 以下、Generated by Claude Code --- 
## Pull Request Summaries

### PR #577: update TaskManager import path
- **English**: Refactored GraphAI imports by consolidating TaskManager import path into the main graphai module. This change improves code organization and follows GraphAI framework updates by removing the need for separate import paths. The modification affects audio and image generation modules, making imports more consistent and reducing import complexity across the codebase.
- **日本語**: TaskManagerのインポートパスをメインのgraphaiモジュールに統合することで、GraphAIインポートをリファクタリングしました。この変更により、別々のインポートパスの必要性を排除し、GraphAIフレームワークの更新に従ってコード構成が改善されます。この修正は音声と画像生成モジュールに影響し、コードベース全体でインポートをより一貫性があり、インポートの複雑さを軽減します。

### PR #578: settings2GraphAIConfig
- **English**: Implemented a centralized API key management and configuration system through the new `settings2GraphAIConfig()` utility function. This enhancement enables dynamic API key configuration for multiple AI providers (OpenAI, Anthropic, Replicate, Nijivoice, ElevenLabs) instead of relying on hardcoded environment variables. The change introduces configuration dependency injection patterns, making the system more flexible and testable. All audio and image generation functions now accept optional settings parameters, improving separation of concerns and enabling better multi-provider support.
- **日本語**: 新しい`settings2GraphAIConfig()`ユーティリティ関数を通じて、中央集権的なAPIキー管理と設定システムを実装しました。この強化により、ハードコードされた環境変数に依存するのではなく、複数のAIプロバイダー（OpenAI、Anthropic、Replicate、Nijivoice、ElevenLabs）の動的なAPIキー設定が可能になります。この変更により設定依存性注入パターンが導入され、システムがより柔軟でテスト可能になります。すべての音声と画像生成関数がオプションの設定パラメータを受け入れるようになり、関心の分離が改善され、より良いマルチプロバイダーサポートが可能になります。

### PR #579: Caption styles
- **English**: Revolutionized the caption styling system by replacing fixed caption properties with a flexible CSS-based approach. This major enhancement allows unlimited customization through a `styles` array that supports any CSS styling, including animations, shadows, gradients, and advanced typography. The system now supports both script-level and beat-level caption styling, removing previous limitations of hardcoded properties like textColor, backgroundColor, fontSize, and others. Caption styles are directly injected into the HTML template, giving creators full control over visual presentation.
- **日本語**: 固定されたキャプションプロパティを柔軟なCSSベースのアプローチに置き換えることで、キャプションスタイリングシステムを革新しました。この大きな強化により、アニメーション、シャドウ、グラデーション、高度なタイポグラフィを含む任意のCSSスタイリングをサポートする`styles`配列を通じて無制限のカスタマイズが可能になります。システムは現在、スクリプトレベルとビートレベルの両方のキャプションスタイリングをサポートし、textColor、backgroundColor、fontSizeなどのハードコードされたプロパティの以前の制限を取り除きます。キャプションスタイルはHTMLテンプレートに直接注入され、クリエイターが視覚的プレゼンテーションを完全に制御できます。

### PR #580: htmlImageSystemPrompt
- **English**: Refactored HTML image generation system by extracting hardcoded system prompts into a reusable `htmlImageSystemPrompt()` function. This improvement centralizes prompt management in `src/utils/prompt.ts`, making the system more maintainable and eliminating code duplication. The change also makes canvas size parameters dynamic instead of hardcoded, providing better flexibility for different output formats and following the project's pattern of centralizing prompts in utility functions.
- **日本語**: ハードコードされたシステムプロンプトを再利用可能な`htmlImageSystemPrompt()`関数に抽出することで、HTML画像生成システムをリファクタリングしました。この改善により`src/utils/prompt.ts`でプロンプト管理が集中化され、システムがより保守しやすくなり、コードの重複が排除されます。この変更により、キャンバスサイズパラメータもハードコードではなく動的になり、異なる出力形式に対してより良い柔軟性が提供され、ユーティリティ関数でプロンプトを集中化するプロジェクトのパターンに従います。

## Release Notes

### 1. Developer-Focused Release Notes (English)

# MulmoCast v0.0.24 Release Notes

This maintenance release focuses on code organization improvements and introduces flexible caption styling capabilities.

## Major Enhancements

**Caption Styling Revolution**
- Replaced fixed caption properties with flexible CSS-based styling (#579)
- Support for unlimited customization through `styles` array
- Enable animations, shadows, gradients, and advanced typography
- Both script-level and beat-level caption styling support

**Configuration System Improvements**
- Centralized API key management with `settings2GraphAIConfig()` (#578)
- Dynamic configuration for multiple AI providers
- Improved separation of concerns and testability
- Configuration dependency injection pattern implementation

## Code Organization

**Import Path Consolidation**
- Unified GraphAI TaskManager imports for consistency (#577)
- Simplified import structure across audio and image modules

**Prompt Management Enhancement**
- Centralized HTML image system prompts in utility functions (#580)
- Dynamic canvas size parameters
- Eliminated hardcoded prompts for better maintainability

## Technical Details

The caption styling refactoring (#579) represents the most significant user-facing improvement, transforming caption customization from a limited set of properties to unlimited CSS-based styling. This change enables creators to implement sophisticated caption designs that were previously impossible.

The configuration system enhancement (#578) lays important groundwork for better multi-provider support and testing capabilities, making the system more robust and flexible for future development.

### 2. Developer-Focused Release Notes (Japanese)

# MulmoCast v0.0.24 リリースノート

このメンテナンスリリースでは、コード構成の改善に焦点を当て、柔軟なキャプションスタイリング機能を導入しています。

## 主要な強化

**キャプションスタイリングの革新**
- 固定されたキャプションプロパティを柔軟なCSSベースのスタイリングに置き換え (#579)
- `styles`配列による無制限のカスタマイズサポート
- アニメーション、シャドウ、グラデーション、高度なタイポグラフィを有効化
- スクリプトレベルとビートレベルの両方のキャプションスタイリングサポート

**設定システムの改善**
- `settings2GraphAIConfig()`による中央集権的なAPIキー管理 (#578)
- 複数のAIプロバイダーの動的設定
- 関心の分離とテスト可能性の向上
- 設定依存性注入パターンの実装

## コード構成

**インポートパスの統合**
- 一貫性のためのGraphAI TaskManagerインポートの統一 (#577)
- 音声と画像モジュール全体でのインポート構造の簡素化

**プロンプト管理の強化**
- ユーティリティ関数でのHTML画像システムプロンプトの集中化 (#580)
- 動的なキャンバスサイズパラメータ
- より良い保守性のためのハードコードされたプロンプトの排除

## 技術詳細

キャプションスタイリングのリファクタリング (#579) は最も重要なユーザー向けの改善を表しており、キャプションのカスタマイズを限定されたプロパティセットから無制限のCSSベースのスタイリングに変換します。この変更により、クリエイターは以前は不可能だった洗練されたキャプションデザインを実装できるようになります。

設定システムの強化 (#578) は、より良いマルチプロバイダーサポートとテスト機能の重要な基盤を築き、将来の開発のためにシステムをより堅牢で柔軟にします。

### 3. Creator-Focused Release Notes (English)

# MulmoCast v0.0.24 - What's New for Creators

## Unlimited Caption Styling

Transform your captions with complete creative freedom! The new caption styling system lets you use any CSS styling you can imagine.

**What You Can Do Now:**
- Custom colors, fonts, and sizes
- Animations and transitions
- Drop shadows and glows
- Gradients and background effects
- Advanced typography effects

**How to Use:**
Instead of fixed properties, use the new `styles` array in your caption settings:

```json
{
  "captionParams": {
    "styles": [
      "color: #FF6B6B;",
      "font-family: 'Arial Black', sans-serif;",
      "text-shadow: 2px 2px 4px rgba(0,0,0,0.5);",
      "background: linear-gradient(45deg, #FF6B6B, #4ECDC4);"
    ]
  }
}
```

**Individual Beat Styling:**
You can also style captions for specific beats:
```json
{
  "speaker": "Host",
  "text": "Special announcement!",
  "captionParams": {
    "styles": ["font-size: 32px;", "animation: pulse 2s infinite;"]
  }
}
```

## Improved Reliability

Behind the scenes improvements ensure more stable video generation and better handling of different AI providers.

### 4. Creator-Focused Release Notes (Japanese)

# MulmoCast v0.0.24 - クリエイター向け新機能

## 無制限のキャプションスタイリング

完全な創作の自由でキャプションを変身させましょう！新しいキャプションスタイリングシステムでは、想像できるあらゆるCSSスタイリングを使用できます。

**今できること:**
- カスタムカラー、フォント、サイズ
- アニメーションとトランジション
- ドロップシャドウとグロー効果
- グラデーションと背景効果
- 高度なタイポグラフィ効果

**使用方法:**
固定されたプロパティの代わりに、キャプション設定で新しい`styles`配列を使用します：

```json
{
  "captionParams": {
    "styles": [
      "color: #FF6B6B;",
      "font-family: 'Arial Black', sans-serif;",
      "text-shadow: 2px 2px 4px rgba(0,0,0,0.5);",
      "background: linear-gradient(45deg, #FF6B6B, #4ECDC4);"
    ]
  }
}
```

**個別のビートスタイリング:**
特定のビートのキャプションもスタイリングできます：
```json
{
  "speaker": "Host",
  "text": "特別なお知らせ！",
  "captionParams": {
    "styles": ["font-size: 32px;", "animation: pulse 2s infinite;"]
  }
}
```

## 信頼性の向上

舞台裏の改善により、より安定したビデオ生成と異なるAIプロバイダーのより良い処理が保証されます。