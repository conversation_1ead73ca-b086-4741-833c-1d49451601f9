<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body{
      margin: 0;
      background: transparent;
    }
    .container {
      /* Box styling */
      width: ${width}px;
      height: ${height}px;
      position: relative;
      margin: 0;
      box-sizing: border-box;
      background: transparent;
    }
    .caption {
      /* Text positioned at the bottom */
      width: 80%;
      position: absolute;
      bottom: 0px;
      /* Enable text wrapping */
      word-wrap: break-word;
      overflow-wrap: break-word;
      /* Optional styling */
      font-family: Arial, sans-serif;
      font-size: 32px;
      text-align: center;
      color: white;
      text-shadow: 0px 0px 20px rgba(0, 0, 0, 1.0);
      padding-left: 10%;
      padding-right: 10%;
      padding-top: 4px;
      background: rgba(0, 0, 0, 0.4);
      ${styles}
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="caption">${caption}</div>
  </div>
</body>
</html>