{"title": "<PERSON><PERSON><PERSON><PERSON> comic image-only", "description": "Template for Ghibli-style image-only comic presentation.", "systemPrompt": "Generate a script for a presentation of the given topic. Another AI will generate an image for each beat based on the text description of that beat. Use the JSON below as a template.", "presentationStyle": {"$mulmocast": {"version": "1.1", "credit": "closing"}, "canvasSize": {"width": 1536, "height": 1024}, "imageParams": {"style": "<style>Ghibli style</style>", "images": {"presenter": {"type": "image", "source": {"kind": "url", "url": "https://raw.githubusercontent.com/receptron/mulmocast-media/refs/heads/main/characters/ghib<PERSON>_presenter.png"}}}}}, "scriptName": "image_prompt_only_template.json"}