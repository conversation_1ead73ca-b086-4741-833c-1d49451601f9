{"$mulmocast": {"version": "1.1", "credit": "closing"}, "canvasSize": {"width": 1536, "height": 1024}, "speechParams": {"speakers": {"Presenter": {"displayName": {"en": "Presenter"}, "voiceId": "shimmer"}}}, "imageParams": {"provider": "openai", "style": "<style>90年代の日本のミステリーアニメに見られる、シャープな線画とセル画風の彩色で表現された作画スタイル</style>", "images": {"presenter": {"type": "imagePrompt", "prompt": "アニメ風の少年キャラクター。大きな青い瞳と、少しツリ目気味の目の形。前髪は重めで、左右に分かれたシャープな黒髪。頭頂部から一房だけピョンと跳ねた毛がある。細い縁の丸メガネをかけている。服装は濃い青のスーツに赤い蝶ネクタイ、白いワイシャツ。全体的にデフォルメされたアニメスタイル（日本の90年代〜2000年代初頭アニメ風）、シャープな線画と鮮やかな発色。"}}}, "movieParams": {"provider": "replicate"}, "soundEffectParams": {"provider": "replicate"}, "audioParams": {"padding": 0.3, "introPadding": 1, "closingPadding": 0.8, "outroPadding": 1, "bgmVolume": 0.2, "audioVolume": 1, "suppressSpeech": false}, "title": "探偵ジュニアの大冒険：ミステリアスな掛け算の謎", "references": [{"url": "https://example.com/multiplication-story-lesson", "title": "ストーリーテリングで学ぶかけ算", "type": "article"}], "lang": "ja", "beats": [{"text": "探偵ジュニアは、隠された地下図書館への秘密の入り口を発見しました。10列の魔法の本棚があり、それぞれの本棚に7冊の本が並んでいます。全部で何冊あるでしょう？", "id": "3d2ff6a3-b47d-4c65-9986-3ab671ed582b", "imagePrompt": "地下図書館に立つ探偵ジュニア、光る本棚が10列並んでいる"}, {"text": "虹色の光が輝く不思議な花園に足を踏み入れたジュニア。花園には6種類の魔法の花があり、各種類に8本の花が咲いています。全部で何本でしょう？", "id": "a8131d5f-3499-46f7-b104-c5ef728e31f4", "imagePrompt": "虹色に光る花園、6種類の魔法の花が風に揺れている"}, {"text": "謎のキャンディ工場に潜入したジュニア。工場では一度に4袋のキャンディが作られ、それを1日に9回繰り返します。1日で何袋になるでしょう？", "id": "d4ae73cc-a7f8-4f1d-bd36-49a38af8e325", "imagePrompt": "カラフルなキャンディ工場でジュニアがキャンディを見ている"}, {"text": "魔法の絵画を発見したジュニア。絵画には5つの塔があり、それぞれに3階の秘密の部屋があります。合計で何階でしょう？", "id": "ac686caa-3eed-4524-810f-2e4c8846a2ab", "imagePrompt": "額縁から飛び出すような魔法の絵画、5つの塔が立っている"}, {"text": "忘れ去られた古城を調査するジュニア。城には12の部屋があり、それぞれに2つの窓があります。全部で何枚の窓でしょう？", "id": "de899259-0dc3-4d33-b387-9551fd5f608c", "imagePrompt": "苔むした古城、12の部屋から光る窓が見える"}, {"text": "古びた宝箱の鍵コレクションを手に入れたジュニア。8つの箱があり、各箱に6本の鍵があります。全部で何本でしょう？", "id": "e42c1c42-a891-4c69-b31a-9b42114d2f3f", "imagePrompt": "8つの宝箱の中に金色の鍵が並んでいる"}, {"text": "月明かりの舞踏会に参加したジュニア。舞踏会には10組のカップルが踊っています。全部で何人でしょう？", "id": "bf0ad1f7-deae-4fa7-9654-366c82066167", "imagePrompt": "月明かりの下、優雅に踊る10組のカップルを見つめるジュニア"}, {"text": "伝説の海賊船に乗り込んだジュニア。船には7つの部屋があり、それぞれに5つの宝箱があります。いくつ見つけられるでしょう？", "id": "d39dc46a-7b95-4354-a1d3-0bca20f885ff", "imagePrompt": "海賊船の甲板、7つの部屋と輝く宝箱"}, {"text": "古時計を調べるジュニア。この時計は1時間に3回鐘を鳴らします。1日で何回鳴るでしょう？", "id": "2a316115-b6a0-4f1b-ad39-44db24d7d7a3", "imagePrompt": "大きな古時計、鐘の音が響く"}, {"text": "満月の夜、魔法の果樹園を訪れたジュニア。9本の木があり、それぞれに4つの黄金のリンゴがなっています。全部で何個でしょう？", "id": "eff12361-ea8c-4c49-9b7f-cf031c963dd3", "imagePrompt": "月明かりに照らされる果樹園、黄金のリンゴが輝いている"}, {"text": "ミイラが守る迷路に挑むジュニア。迷路には11の通路があり、それぞれに2つの秘密のドアがあります。ドアは何枚でしょう？", "id": "96ea16ce-881c-4d4f-b40c-bdb64a5533fa", "imagePrompt": "暗い迷路、11本の通路と秘密のドアが光っている"}, {"text": "地図にもない謎の島にたどり着いたジュニア。島には5種類の動物がいて、それぞれ9匹ずついます。全部で何匹でしょう？", "id": "e2800b23-cda5-4ec9-bfeb-52c3e26162f1", "imagePrompt": "謎の島で探偵ジュニアが5種類の動物に囲まれている"}]}