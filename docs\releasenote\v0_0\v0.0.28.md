# プロンプト
0.0.28 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.0.28

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
- 絵文字は使わないでください
- **事実ベースの記述を徹底してください**：
  - **推測・憶測の禁止**: PRの実際の変更内容のみを記述し、「将来の可能性」や「〜につながる」といった推測的表現は避ける
  - **具体的な変更内容**: ファイル追加、機能実装、設定変更など、実際に行われた変更を具体的に記述
  - **過大解釈の回避**: PRタイトルや説明文から過度に意味を汲み取らず、コード変更の事実に基づいて記述
  - **未来予測の排除**: 「〜の基盤となる」「将来のAI機能」「より洗練された〜」などの表現は使用しない

**例**:
- ❌ 「将来のAI機能の基盤を築く実験的な実装」
- ✅ 「実験的なMCPサーバー実装とJSONスキーマを追加」
- ❌ 「より知的なワークフロー最適化への重要な一歩」
- ✅ 「設定管理のための標準化されたインターフェースを提供」

## STEP1 →　 このファイルに追記してください。
**重要**: 作業開始前に必ず [v0.0.17.md](./v0.0.17.md) を読んで、PRサマリーの詳細レベルと文章量を確認してください。

すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
各PRサマリーは v0.0.17.md の形式に合わせて：
- 英語: 技術的詳細、影響、実装理由を含む150-300語程度
- 日本語: 英語版と同等の詳細レベルで翻訳
- 単なる機能説明ではなく、WHYとIMPACTを重視した解説


## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。


## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は [v0.0.16.md](./v0.0.16.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

### セクション分類ガイドライン
リリースノートでは以下のようにセクション分けしてください：
- **New Features / メイン機能**: 新機能や重要な機能強化
- **Others**: 以下の項目をまとめる
  - サンプルスクリプトやテンプレートの追加
  - ドキュメントの更新・追加
  - リリースノートの追加
  - メンテナンス・依存関係の更新
  - 小さなバグ修正
  - コードのリファクタリング（内部的な改善）


## 今回のリリースに含まれる Pull Request
## What's Changed
* add setFfmpegPath by @isamu in https://github.com/receptron/mulmocast-cli/pull/596
* add setFfprobePath by @isamu in https://github.com/receptron/mulmocast-cli/pull/597


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.27...0.0.28

--- 以下、Generated by Claude Code --- 

## Pull Request Summaries / PRサマリー

### PR #596: add setFfmpegPath - @isamu
- **English**: Implemented a utility function `setFfmpegPath` to allow customization of the FFmpeg executable path. This addition addresses deployment scenarios where FFmpeg is installed in non-standard locations or where users need to specify a particular FFmpeg version. The function is exposed through the library's public API via `src/index.ts`, making it accessible for both library consumers and CLI usage. This enhancement improves flexibility in enterprise environments, containerized deployments, and situations where system-wide FFmpeg installation is not available or where multiple FFmpeg versions coexist. The implementation wraps the fluent-ffmpeg library's native path setting functionality, maintaining consistency with the existing FFmpeg utilities architecture.
- **日本語**: FFmpeg実行可能ファイルのパスをカスタマイズできるユーティリティ関数`setFfmpegPath`を実装しました。この追加により、FFmpegが標準以外の場所にインストールされている環境や、特定のFFmpegバージョンを指定する必要がある場合に対応できます。この関数は`src/index.ts`を通じてライブラリの公開APIとして公開され、ライブラリ利用者とCLI使用の両方でアクセス可能です。この機能強化により、エンタープライズ環境、コンテナ化されたデプロイメント、システム全体のFFmpegインストールが利用できない状況、または複数のFFmpegバージョンが共存する環境での柔軟性が向上します。実装はfluent-ffmpegライブラリのネイティブパス設定機能をラップし、既存のFFmpegユーティリティアーキテクチャとの一貫性を維持しています。

### PR #597: add setFfprobePath - @isamu
- **English**: Added a complementary utility function `setFfprobePath` to customize the FFprobe executable path, completing the FFmpeg toolchain path configuration capability. FFprobe is essential for media file analysis and metadata extraction operations used throughout MulmoCast's media processing pipeline. This addition ensures that both FFmpeg and FFprobe paths can be configured consistently, preventing mismatches between the tools and avoiding runtime errors when custom FFmpeg installations include FFprobe in the same directory. The function follows the same pattern as `setFfmpegPath`, maintaining API consistency and making it straightforward for users to configure both tools together. This is particularly important for Docker containers, serverless environments, and restricted systems where executables must be bundled with the application.
- **日本語**: FFprobe実行可能ファイルのパスをカスタマイズする補完的なユーティリティ関数`setFfprobePath`を追加し、FFmpegツールチェーンのパス設定機能を完成させました。FFprobeは、MulmoCastのメディア処理パイプライン全体で使用されるメディアファイル分析とメタデータ抽出操作に不可欠です。この追加により、FFmpegとFFprobeの両方のパスを一貫して設定でき、ツール間の不一致を防ぎ、カスタムFFmpegインストールが同じディレクトリにFFprobeを含む場合の実行時エラーを回避できます。この関数は`setFfmpegPath`と同じパターンに従い、APIの一貫性を維持し、ユーザーが両方のツールを一緒に設定することを簡単にします。これは、Dockerコンテナ、サーバーレス環境、および実行可能ファイルをアプリケーションにバンドルする必要がある制限されたシステムで特に重要です。

---

## Developer Release Notes (English)

### New Utility Functions

**FFmpeg Path Configuration**
- **setFfmpegPath** (#596): Added function to set custom FFmpeg executable path
- **setFfprobePath** (#597): Added function to set custom FFprobe executable path

### Technical Details

These utility functions enable:
- Custom FFmpeg/FFprobe installation paths for non-standard environments
- Support for bundled executables in containerized deployments
- Flexibility in enterprise environments with specific FFmpeg versions
- Consistent path configuration for both FFmpeg and FFprobe tools

### Usage

```typescript
import { setFfmpegPath, setFfprobePath } from 'mulmocast';

// Set custom paths
setFfmpegPath('/custom/path/to/ffmpeg');
setFfprobePath('/custom/path/to/ffprobe');
```

### Impact

This release improves deployment flexibility without changing existing functionality. All current workflows remain unchanged unless custom paths are explicitly set.

---

## Developer Release Notes (Japanese)

### 新しいユーティリティ関数

**FFmpegパス設定**
- **setFfmpegPath** (#596): カスタムFFmpeg実行可能ファイルパスを設定する関数を追加
- **setFfprobePath** (#597): カスタムFFprobe実行可能ファイルパスを設定する関数を追加

### 技術詳細

これらのユーティリティ関数により以下が可能になります：
- 非標準環境でのカスタムFFmpeg/FFprobeインストールパス
- コンテナ化されたデプロイメントでのバンドルされた実行可能ファイルのサポート
- 特定のFFmpegバージョンを持つエンタープライズ環境での柔軟性
- FFmpegとFFprobeツールの両方に対する一貫したパス設定

### 使用方法

```typescript
import { setFfmpegPath, setFfprobePath } from 'mulmocast';

// カスタムパスを設定
setFfmpegPath('/custom/path/to/ffmpeg');
setFfprobePath('/custom/path/to/ffprobe');
```

### 影響

このリリースは既存の機能を変更することなくデプロイメントの柔軟性を向上させます。カスタムパスが明示的に設定されない限り、現在のワークフローはすべて変更されません。

---

## Creator Release Notes (English)

### System Compatibility

**Enhanced Environment Support**
- MulmoCast now works better in specialized environments where FFmpeg is installed in custom locations
- No changes to your current workflow - this update ensures MulmoCast works in more deployment scenarios

### What This Means for Creators

This technical update improves system compatibility:
- MulmoCast can now run in more environments without FFmpeg installation issues
- Your existing scripts and workflows continue to work exactly as before
- No action required on your part

---

## Creator Release Notes (Japanese)

### システム互換性

**環境サポートの強化**
- FFmpegがカスタムロケーションにインストールされている特殊な環境でMulmoCastがより良く動作するようになりました
- 現在のワークフローへの変更はありません - このアップデートはMulmoCastがより多くのデプロイメントシナリオで動作することを保証します

### クリエイターにとっての意味

この技術的アップデートはシステム互換性を向上させます：
- FFmpegインストールの問題なしに、MulmoCastがより多くの環境で実行可能になりました
- 既存のスクリプトとワークフローは以前と全く同じように動作し続けます
- あなたの側での対応は必要ありません