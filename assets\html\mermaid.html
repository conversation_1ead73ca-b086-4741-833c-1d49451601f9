<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Diagram</title>
    <style>
        ${style}
        .container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .mermaid {
            width: 100%;
            height: 100%;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            overflow: auto;
        }
        .mermaid svg {
            max-width: 100%;
            max-height: 80%;
            height: auto;
            width: auto;
        }
    </style>
    <!-- Include Mermaid from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true
            }
        });
        document.addEventListener("DOMContentLoaded", () => {
            mermaid.init(undefined, ".mermaid");
            document.querySelector(".mermaid").dataset.ready = "true";
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>${title}</h1>
        <div class="mermaid">
            ${diagram_code}
        </div>
    </div>
</body>
</html> 
