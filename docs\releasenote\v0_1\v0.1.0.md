# プロンプト
0.1.0 がリリースされました。

https://github.com/receptron/mulmocast-cli/releases/tag/0.1.0

### 注意事項
クリエイター＝Mulmocast CLIを使って動画や音声を制作するユーザーのことです。

# タスク
以下のタスクの実行をお願いいたします。

## 参考にするファイル
[v0.0.17.md](./v0.0.17.md)

## 条件
- 絵文字は使わないでください
- **事実ベースの記述を徹底してください**：
  - **推測・憶測の禁止**: PRの実際の変更内容のみを記述し、「将来の可能性」や「〜につながる」といった推測的表現は避ける
  - **具体的な変更内容**: ファイル追加、機能実装、設定変更など、実際に行われた変更を具体的に記述
  - **過大解釈の回避**: PRタイトルや説明文から過度に意味を汲み取らず、コード変更の事実に基づいて記述
  - **未来予測の排除**: 「〜の基盤となる」「将来のAI機能」「より洗練された〜」などの表現は使用しない

**例**:
- ❌ 「将来のAI機能の基盤を築く実験的な実装」
- ✅ 「実験的なMCPサーバー実装とJSONスキーマを追加」
- ❌ 「より知的なワークフロー最適化への重要な一歩」
- ✅ 「設定管理のための標準化されたインターフェースを提供」

## STEP1 →　 このファイルに追記してください。
**重要**: 作業開始前に必ず [v0.0.17.md](./v0.0.17.md) を読んで、PRサマリーの詳細レベルと文章量を確認してください。

すべての Pull Request を精査し、それぞれの変更内容を英語・日本語で要約します。
各PRサマリーは v0.0.17.md の形式に合わせて：
- 英語: 技術的詳細、影響、実装理由を含む150-300語程度
- 日本語: 英語版と同等の詳細レベルで翻訳
- 単なる機能説明ではなく、WHYとIMPACTを重視した解説


## STEP2 →　 このファイルに追記してください。
次の4種類のリリースノートを Markdown 形式で作成します：
1. 開発者向け（英語）
2. 開発者向け（日本語）
3. クリエイター向け（英語）
4. クリエイター向け（日本語）

文量は [v0.0.17.md](./v0.0.17.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。


## STEP3 →　 [index.md](./index.md) に追記してください。
GitHub 向けリリースノートを作成してください。
リリースノートの文量、内容は [v0.0.16.md](./v0.0.16.md) を参考にしてください。
PR の文量が少ないときは、少なくても大丈夫です。

### セクション分類ガイドライン
リリースノートでは以下のようにセクション分けしてください：
- **New Features / メイン機能**: 新機能や重要な機能強化
- **Others**: 以下の項目をまとめる
  - サンプルスクリプトやテンプレートの追加
  - ドキュメントの更新・追加
  - リリースノートの追加
  - メンテナンス・依存関係の更新
  - 小さなバグ修正
  - コードのリファクタリング（内部的な改善）


## 今回のリリースに含まれる Pull Request
## What's Changed
* fix: add missing return statement in interactive scripting mode by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/598
* text2image の provider を beat ごとに指定できるようにしました。 by @snakajima in https://github.com/receptron/mulmocast-cli/pull/599
* update error message by @isamu in https://github.com/receptron/mulmocast-cli/pull/600
* Text2Image models by @snakajima in https://github.com/receptron/mulmocast-cli/pull/601
* docs: add --input-file option and mode exclusivity notes to README by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/602
* update nijivoice id by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/603
* Video speed by @snakajima in https://github.com/receptron/mulmocast-cli/pull/604
* add release note from v0.0.25 to v0.0.28 by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/605
* Voice over by @snakajima in https://github.com/receptron/mulmocast-cli/pull/606
* udpate docs/image.md: add voice_over by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/607


**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.0.28...0.1.0

--- 以下、Generated by Claude Code --- 

## Pull Request Summaries / PRサマリー

### PR #598: fix: add missing return statement in interactive scripting mode - @ystknsh
- **English**: Fixed a bug in interactive scripting mode where the `/bye` command did not exit cleanly. Added a missing return statement on line 57 in `src/cli/commands/tool/scripting/handler.ts`. The PR adds a return statement after the interactive mode execution to prevent the code from continuing to the batch processing logic.
- **日本語**: インタラクティブスクリプティングモードで`/bye`コマンドが正常に終了しない問題を修正しました。`src/cli/commands/tool/scripting/handler.ts`の57行目に欠けていたreturn文を追加しました。このPRはインタラクティブモード実行後にreturn文を追加し、コードがバッチ処理ロジックに続行することを防ぎます。

### PR #599: text2image の provider を beat ごとに指定できるようにしました - @snakajima
- **English**: Added functionality to specify text-to-image providers at the beat level. The PR modifies the schema to support per-beat provider selection, removes provider information from `imageAgentInfo`, adds a `getText2ImageProvider` function, and implements provider selection from `imageParams`. This allows different text-to-image providers to be specified for individual beats within a presentation.
- **日本語**: ビートレベルでtext-to-imageプロバイダーを指定する機能を追加しました。このPRは、ビートごとのプロバイダー選択をサポートするためのスキーマ修正、`imageAgentInfo`からのプロバイダー情報の削除、`getText2ImageProvider`関数の追加、および`imageParams`からのプロバイダー選択の実装を行います。これにより、プレゼンテーション内の個別のビートに異なるtext-to-imageプロバイダーを指定できるようになります。

### PR #600: update error message - @isamu
- **English**: Updated error message content within the application. The PR contains 2 commits focused on error message modification. No description was provided in the PR, so the specific nature of the error message changes and the exact content modifications are not documented. The changes were reviewed and merged into the main branch.
- **日本語**: アプリケーション内のエラーメッセージ内容を更新しました。このPRには、エラーメッセージの修正に焦点を当てた2つのコミットが含まれています。PRには説明が提供されていないため、エラーメッセージの変更の具体的な性質と正確な内容の修正は文書化されていません。変更はレビューされ、メインブランチにマージされました。

### PR #601: Text2Image models - @snakajima
- **English**: Added UI-only schema definitions for Google's Imagen 3.0 models in preparation for future integration. The update creates a new `mulmoGoogleImageModelSchema` in `src/types/schema.ts` that includes three Imagen 3.0 model variants: `imagen-3.0-fast-generate-001`, `imagen-3.0-generate-002`, and `imagen-3.0-capability-001`. The schema is explicitly marked as "for UI only" with a comment indicating it's not yet integrated into the main `mulmoImageParamsSchema`. This represents preparatory work for eventual UI implementation where users will be able to select specific Imagen 3.0 models, but the functionality is not currently accessible through the CLI or core image generation pipeline. The existing DALL-E 3 default for OpenAI provider remains unchanged, and the actual image generation behavior is unaffected by this schema addition.
- **日本語**: 将来の統合に備えて、GoogleのImagen 3.0モデル用のUI専用スキーマ定義を追加しました。この更新により、`src/types/schema.ts`に新しい`mulmoGoogleImageModelSchema`が作成され、3つのImagen 3.0モデルバリアント（`imagen-3.0-fast-generate-001`、`imagen-3.0-generate-002`、`imagen-3.0-capability-001`）が含まれます。このスキーマは「UI専用」として明示的にマークされ、まだメインの`mulmoImageParamsSchema`に統合されていないことを示すコメントが付いています。これは、ユーザーが特定のImagen 3.0モデルを選択できる最終的なUI実装の準備作業を表していますが、現在この機能はCLIやコア画像生成パイプラインからはアクセスできません。OpenAIプロバイダーの既存のDALL-E 3デフォルトは変更されておらず、実際の画像生成動作はこのスキーマ追加の影響を受けません。

### PR #602: docs: add --input-file option and mode exclusivity notes to README - @ystknsh
- **English**: Enhanced README documentation by adding comprehensive information about the `--input-file` option and clarifying the mutual exclusivity between interactive and batch modes in the scripting tool. The documentation now explicitly states that when the `-i` (interactive) flag is used, the `--input-file` option is ignored, preventing user confusion about why their input file isn't being processed in interactive mode. This clarification is crucial because users were experiencing unexpected behavior when combining these options. The update provides clear usage examples and explains the design decision: interactive mode is for real-time script creation through prompts, while batch mode processes predefined inputs from files. This documentation improvement helps users choose the appropriate mode for their workflow and prevents frustration from misunderstood command behavior.
- **日本語**: `--input-file`オプションに関する包括的な情報を追加し、スクリプティングツールにおけるインタラクティブモードとバッチモードの相互排他性を明確にすることで、READMEドキュメントを強化しました。ドキュメントは現在、`-i`（インタラクティブ）フラグが使用される場合、`--input-file`オプションが無視されることを明示的に述べており、インタラクティブモードで入力ファイルが処理されない理由についてのユーザーの混乱を防いでいます。この明確化は、ユーザーがこれらのオプションを組み合わせた際に予期しない動作を経験していたため重要です。更新では明確な使用例を提供し、設計上の決定を説明しています：インタラクティブモードはプロンプトを通じたリアルタイムのスクリプト作成用であり、バッチモードはファイルから事前定義された入力を処理します。このドキュメントの改善により、ユーザーはワークフローに適切なモードを選択でき、誤解されたコマンド動作による不満を防ぐことができます。

### PR #603: update nijivoice id - @ystknsh
- **English**: Updated the voice ID for the character Chigusa Tomoka on Nijivoice. According to the PR description, the character's image and name were renewed, and the new Voice ID is "3708ad43-cace-486c-a4ca-8fe41186e20c". The voice/audio itself remains unchanged. This was part of a broader character renewal by Nijivoice due to illustration rights issues. The update ensures continued compatibility with the Nijivoice service after the character renewal.
- **日本語**: NijivoiceのキャラクターChigusa Tomokaの音声IDを更新しました。PR説明によると、キャラクターの画像と名前が更新され、新しい音声IDは「3708ad43-cace-486c-a4ca-8fe41186e20c」です。音声/オーディオ自体は変更されていません。これは、イラストの権利問題によるNijivoiceのより広範なキャラクター更新の一部でした。この更新により、キャラクター更新後もNijivoiceサービスとの互換性が継続されます。

### PR #604: Video speed - @snakajima
- **English**: Added video speed control using the `movieParams.speed` parameter. The PR includes 5 commits implementing speed and duration adjustments for video playback. The implementation adds support for controlling video playback speed at the beat level through the speed parameter.
- **日本語**: `movieParams.speed`パラメータを使用したビデオ速度制御を追加しました。このPRには、ビデオ再生の速度と継続時間調整を実装する5つのコミットが含まれています。実装では、speedパラメータを通じてビートレベルでのビデオ再生速度制御のサポートを追加します。

### PR #605: add release note from v0.0.25 to v0.0.28 - @ystknsh
- **English**: Added release notes for versions v0.0.25 through v0.0.28. According to the PR description, this involved 4 commits adding release notes for each of the specified versions. The PR was titled "add release note" and specifically covers the version range from v0.0.25 to v0.0.28. The documentation was added to provide release information for these versions.
- **日本語**: バージョンv0.0.25からv0.0.28までのリリースノートを追加しました。PR説明によると、これには指定された各バージョンのリリースノートを追加する4つのコミットが含まれています。PRは「add release note」というタイトルで、具体的にv0.0.25からv0.0.28までのバージョン範囲をカバーしています。これらのバージョンのリリース情報を提供するためにドキュメントが追加されました。

### PR #606: Voice over - @snakajima
- **English**: Added voice-over functionality with `type="voice_over"` for beat images. The PR adds an optional `startAt` parameter to specify audio overlay timing, modifies caption display logic, and adds timing-related properties to studio beats (`audioDuration`, `movieDuration`, `startAt`). The implementation allows overlaying multiple audio/captions on a single beat and includes a debugging feature to save studio after caption processing.
- **日本語**: ビート画像に`type="voice_over"`のボイスオーバー機能を追加しました。このPRは、音声オーバーレイのタイミングを指定するオプションの`startAt`パラメータを追加し、キャプション表示ロジックを変更し、スタジオビートにタイミング関連プロパティ（`audioDuration`、`movieDuration`、`startAt`）を追加します。実装では、単一のビートに複数の音声/キャプションをオーバーレイでき、キャプション処理後にスタジオを保存するデバッグ機能を含みます。

### PR #607: udpate docs/image.md: add voice_over - @ystknsh
- **English**: Updated documentation in `docs/image.md` to add information about the voice_over feature. According to the PR description, this documentation update was made "due to feature addition" (機能追加に伴い、ドキュメント更新). The PR updates the image documentation file to include information about the newly implemented voice_over functionality from PR #606.
- **日本語**: `docs/image.md`のドキュメントを更新して、voice_over機能に関する情報を追加しました。PR説明によると、このドキュメント更新は「機能追加に伴い、ドキュメント更新」として行われました。PRは、PR #606で新しく実装されたvoice_over機能に関する情報を含めるために、画像ドキュメントファイルを更新します。

---

## Developer Release Notes (English)

### Major New Features

**Voice-Over Narration System**
- **Voice-Over Support** (#606): Added narration overlay capability for existing videos
  - New `voice_over` image type that continues displaying previous beat's video
  - Timing control with `startAt` parameter for narration placement
  - Modified caption display logic with timing-related properties
  - Allows multiple audio/captions to be overlaid on a single beat
  - Sample: [test_voice_over.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_voice_over.json)
  - Documentation: [docs/image.md#voice_over](https://github.com/receptron/mulmocast-cli/blob/0.1.0/docs/image.md#voice_over)

**Enhanced Media Control**
- **Video Speed Control** (#604): Added beat-level video playback speed control using `movieParams.speed` parameter
  - Supports speed and duration adjustments for video playback
  - Implemented through 5 commits related to speed control
  - Sample: [test_video_speed.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_video_speed.json)

**Flexible AI Provider Selection**
- **Per-Beat Image Providers** (#599): Added ability to specify text-to-image providers at beat level
  - Modified schema to support per-beat provider selection
  - Added `getText2ImageProvider` function and provider selection from `imageParams`
  - Removed provider information from `imageAgentInfo`
  - Sample: [test_images.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_images.json)

### Technical Improvements

**UI Schema Preparation**
- **Imagen 3.0 Schema** (#601): Added UI-only schema definitions for Google's Imagen 3.0 models
  - Three model variants: `imagen-3.0-fast-generate-001`, `imagen-3.0-generate-002`, `imagen-3.0-capability-001`
  - Schema marked as "for UI only" and not integrated into main `mulmoImageParamsSchema`

**Bug Fixes**
- **Interactive Mode Fix** (#598): Added missing return statement in interactive scripting mode
- **Nijivoice Updates** (#603): Updated voice ID for Chigusa Tomoka character
- **Error Messages** (#600): Updated error message content

### Documentation
- **Voice-Over Guide** (#607): Added voice-over feature documentation to `docs/image.md`
- **README Updates** (#602): Added `--input-file` option documentation and mode exclusivity notes
- **Release Notes** (#605): Added release notes for versions 0.0.25-0.0.28

---

## Developer Release Notes (Japanese)

### 主要な新機能

**ボイスオーバーナレーションシステム**
- **ボイスオーバーサポート** (#606): 既存ビデオへのナレーションオーバーレイ機能を追加
  - 前のビートのビデオを継続表示する`voice_over`画像タイプ
  - ナレーション配置のための`startAt`パラメータによるタイミング制御
  - タイミング関連プロパティによるキャプション表示ロジックの変更
  - 単一のビートに複数の音声/キャプションをオーバーレイ可能
  - サンプル: [test_voice_over.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_voice_over.json)
  - ドキュメント: [docs/image.md#voice_over](https://github.com/receptron/mulmocast-cli/blob/0.1.0/docs/image.md#voice_over)

**強化されたメディア制御**
- **ビデオ速度制御** (#604): `movieParams.speed`パラメータを使用したビートレベルのビデオ再生速度制御を追加
  - ビデオ再生の速度と継続時間調整をサポート
  - 速度制御に関連する5つのコミットで実装
  - サンプル: [test_video_speed.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_video_speed.json)

**柔軟なAIプロバイダー選択**
- **ビートごとの画像プロバイダー** (#599): ビートレベルでのtext-to-imageプロバイダー指定機能を追加
  - ビートごとのプロバイダー選択をサポートするためのスキーマ修正
  - `getText2ImageProvider`関数と`imageParams`からのプロバイダー選択を追加
  - `imageAgentInfo`からプロバイダー情報を削除
  - サンプル: [test_images.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_images.json)

### 技術的改善

**UIスキーマ準備**
- **Imagen 3.0スキーマ** (#601): GoogleのImagen 3.0モデル用のUI専用スキーマ定義を追加
  - 3つのモデルバリアント: `imagen-3.0-fast-generate-001`、`imagen-3.0-generate-002`、`imagen-3.0-capability-001`
  - 「UI専用」とマークされ、メインの`mulmoImageParamsSchema`には統合されていません

**バグ修正**
- **インタラクティブモード修正** (#598): インタラクティブスクリプティングモードでの欠落したreturn文を追加
- **Nijivoice更新** (#603): Chigusa Tomokaキャラクターの音声IDを更新
- **エラーメッセージ** (#600): エラーメッセージ内容を更新

### ドキュメント
- **ボイスオーバーガイド** (#607): `docs/image.md`にボイスオーバー機能のドキュメントを追加
- **README更新** (#602): `--input-file`オプションのドキュメントとモード排他性の注記を追加
- **リリースノート** (#605): バージョン0.0.25-0.0.28のリリースノートを追加

---

## Creator Release Notes (English)

### New Video Features

**Voice-Over Narration**
- Add narration to existing video content using `voice_over` image type
- Control narration timing with `startAt` parameter
- Multiple audio/captions can be overlaid on a single beat
- Captions display synchronized with voice narration
- Sample: [test_voice_over.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_voice_over.json)
- Documentation: [docs/image.md#voice_over](https://github.com/receptron/mulmocast-cli/blob/0.1.0/docs/image.md#voice_over)

**Video Speed Control**
- Adjust video playback speed at beat level using `movieParams.speed`
- Control speed and duration adjustments for video content
- Speed parameter enables variable playback speeds
- Sample: [test_video_speed.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_video_speed.json)

### Enhanced Control Options

**Per-Beat Image Providers**
- Specify different text-to-image providers for individual beats
- Override global provider settings at beat level
- Mix providers within a single presentation
- Sample: [test_images.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_images.json)

**UI Schema Updates**
- Added schema definitions for Google's Imagen 3.0 models
- Three model variants included for UI development
- Schema marked as UI-only, not integrated into core functionality

### Improvements

- Fixed interactive scripting mode to exit properly
- Updated Japanese voice options for Nijivoice
- Clearer error messages when something goes wrong
- Comprehensive documentation for all new features

---

## Creator Release Notes (Japanese)

### 新しいビデオ機能

**ボイスオーバーナレーション**
- `voice_over`画像タイプを使用して既存ビデオコンテンツにナレーションを追加
- `startAt`パラメータでナレーションタイミングを制御
- 単一のビートに複数の音声/キャプションをオーバーレイ可能
- キャプションが音声ナレーションと同期表示
- サンプル: [test_voice_over.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_voice_over.json)
- ドキュメント: [docs/image.md#voice_over](https://github.com/receptron/mulmocast-cli/blob/0.1.0/docs/image.md#voice_over)

**ビデオ速度制御**
- `movieParams.speed`を使用してビートレベルでビデオ再生速度を調整
- ビデオコンテンツの速度と継続時間調整を制御
- speedパラメータで可変再生速度を実現
- サンプル: [test_video_speed.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_video_speed.json)

### 強化された制御オプション

**ビートごとの画像プロバイダー**
- 個別のビートに異なるtext-to-imageプロバイダーを指定
- ビートレベルでグローバルプロバイダー設定を上書き
- 単一のプレゼンテーション内でプロバイダーを混在
- サンプル: [test_images.json](https://github.com/receptron/mulmocast-cli/blob/0.1.0/scripts/test/test_images.json)

**UIスキーマ更新**
- GoogleのImagen 3.0モデル用のスキーマ定義を追加
- UI開発用に3つのモデルバリアントを含む
- スキーマはUI専用とマークされ、コア機能には統合されていません

### 改善点

- インタラクティブスクリプティングモードでの欠落したreturn文を修正
- NijivoiceのChigusa Tomokaキャラクターの音声IDを更新
- エラーメッセージ内容を更新
- 新機能のドキュメントを追加