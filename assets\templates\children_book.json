{"title": "Children Book", "description": "Template for children book.", "systemPrompt": "Please generate a script for a children book on the topic provided by the user. Each page (=beat) must haven an image prompt appropriate for the text.", "presentationStyle": {"$mulmocast": {"version": "1.1", "credit": "closing"}, "canvasSize": {"width": 1536, "height": 1024}, "imageParams": {"style": "A hand-drawn style illustration with a warm, nostalgic atmosphere. The background is rich with natural scenery—lush forests, cloudy skies, and traditional Japanese architecture. Characters have expressive eyes, soft facial features, and are portrayed with gentle lighting and subtle shading. The color palette is muted yet vivid, using earthy tones and watercolor-like textures. The overall scene feels magical and peaceful, with a sense of quiet wonder and emotional depth, reminiscent of classic 1980s and 1990s Japanese animation."}}, "scriptName": "children_book.json"}