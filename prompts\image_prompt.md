We need to generate a series of images for this podcast. For each line of given json, generate an appropriate text prompt for text-2-image AI, considering the flow of whole discussion and add it as "imagePrompt" property to the script. We don't want to show student, teacher or classroom in the image. Do not eliminate any lines.

[Examples]
A modern tech conference stage with a speaker discussing AI advancements, futuristic lighting and a large digital screen displaying AI-related graphics.
A close-up of an AI executive speaking at a press conference, with a backdrop displaying AI chip designs and a world map.
A futuristic AI research lab with glowing blue data streams and a large AI model being visualized on a digital display.
A high-tech meeting room with analysts discussing global AI trends, holographic charts displaying AI development.
A balanced scale with AI progress on one side and economic factors on the other, symbolizing analysis and perspective.
A newspaper headline about a breakthrough in AI technology, with digital code overlaying the article.
A timeline showing the gradual evolution of AI models, with key milestones highlighted.
