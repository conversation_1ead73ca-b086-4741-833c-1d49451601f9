$mulmocast:
  version: "1.0"
beats:
  - text: >
      MulmoScript is a scripting language designed for creating multi-modal presentations.
      It plays a central role in MulmoCast, a platform that transforms MulmoScript into rich media experiences.
    image:
      type: image
      source:
        kind: url
        url: "https://raw.githubusercontent.com/receptron/mulmocast-cli/refs/heads/main/assets/images/mulmocast_credit.png"
    textSlideParams:
      cssStyles: "h1 { margin-top: 340px }"
  - text: >
      MulmoScript is the equivalent of a movie script.
      Just like a director creates a based on a screenplay, 
      MulmoCast generates a presentation from a MulmoScript.
      The final output can be a video, podcast, PDF document, slideshow, or other formats.
    image:
      type: mermaid
      title: "Role of MulmoScript"
      code:
        kind: text
        text: |
          flowchart TD
          MulmoScript --> MulmoCast["MulmoCast"]
          MulmoCast --> O_Video("Video") & O_Podcast("Podcast") & O_PDF("PDF Document") & O_Slideshow("Slide-Show") & <PERSON><PERSON>Man<PERSON>("Manga") & O_SwipeAnime("Swipe Anime")
          MulmoCast@{ shape: hex}
          O_Video:::ScriptOutput
          O_Podcast:::ScriptOutput
          O_Slideshow:::ScriptOutput
          O_PDF:::ScriptOutput
          O_Manga:::ScriptOutput
          O_SwipeAnime:::ScriptOutput
          classDef ScriptOutput stroke-width:1px, stroke-dasharray:none, stroke:#FBB35A, fill:#FFEFDB, color:#8F632D
  - text: >
      While it's' possible to write MulmoScript manually in a text editor,
      it is primarily intended to be generated by large language models. 
      To support this process, we provide MulmoTool, which uses LLMs to analyze webpages, 
      PDF documents, storyboards, memos, and other media—automatically converting them into MulmoScripts.
    image:
      type: mermaid
      title: "Generating MulmoScript"
      code:
        kind: text
        text: |
          flowchart TD
          S_WebPage("Web Page") --> S_Tool
          S_PDF("PDF Document") --> S_Tool
          S_Storyboard("Storyboard") --> S_Tool
          S_Memo("Memo") --> S_Tool
          S_TextEditor("Text Editor") --> MulmoScript("MulmoScript")
          S_LLM("ChatGPT/Claude") --> MulmoScript
          S_Tool("MulmoTool") --> MulmoScript
          S_Tool@{ shape: hex}
          S_TextEditor:::ScriptInput
          S_LLM:::ScriptInput
          S_Tool:::ScriptInput
          S_WebPage:::StoryInput
          S_PDF:::StoryInput
          S_Storyboard:::StoryInput
          S_Memo:::StoryInput
          classDef StoryInput stroke-width:1px, stroke-dasharray:none, stroke:#46EDC8, fill:#DEFFF8, color:#378E7A
          classDef ScriptInput stroke-width:1px, stroke-dasharray:none, stroke:#374D7C, fill:#E2EBFF, color:#374D7C
          classDef ScriptOutput stroke-width:1px, stroke-dasharray:none, stroke:#FBB35A, fill:#FFEFDB, color:#8F632D
  - text: >
      MulmoScript is a structured data format which can be written either in JSON or YAML.
      Here is the "Hello World" in MulmoScript in both formats.
    image:
      type: markdown
      markdown: |
        # Hello World in MulmoScript
        ### JSON
        ```json
        {
          "$mulmocast": {
            "version": "1.0",
          },
          "beats": [{
            "text": "Hello World." 
          }]
        }
        ```
        ### YAML 
        ```yaml
        $mulmocast:
          version: "1.0"
        beats:
          - text: "Hello World."
        ```
  - text: >
      One key component of MulmoScript is the beats property. 
      Beats are the fundamental building blocks of a MulmoCast.
      Each beat is similar to a slide in a presentation or a cut in a movie—it can contain text, 
      images, audio, video, and more. 
      The sample script here includes just one beat, containing the text "Hello World."
      As a result, MulmoCast will generate a short video like this:
    image:
      type: markdown
      markdown: |
        # "beats" in MulmoScript
        ### JSON
        ```json
        {
          "$mulmocast": {
            "version": "1.0",
          },
          "beats": [{
            "text": "Hello World." 
          }]
        }
        ```
  - text: >
      Hello World
    image:
      type: image
      source:
        kind: url
        url: https://github.com/receptron/mulmocast-media/raw/refs/heads/main/tutorial/helloworld_auto.png
  - text: >
      Notice that MulmoCast not only generates audio but also creates an image based on the text.
    image:
      type: markdown
      markdown: |
        # Image Generation in MulmoScript
        ### JSON
        ```json
        {
          "$mulmocast": {
            "version": "1.0",
          },
          "beats": [{
            "text": "Hello World." 
          }]
        }
        ```
  - text: >
      Optionally, you can provide a custom image prompt for each beat.
      MulmoCast will then generate an image based on that prompt.
    image:
      type: markdown
      markdown: |
        # Image Prompt in MulmoScript
        ### JSON
        ```json
        {
          "$mulmocast": {
            "version": "1.0",
          },
          "beats": [{
            "text": "Hello World.",
            "imagePrompt": "A globe with the word 'Hello' on it."
          }]
        }
        ```
  - text: >
      For instance, here is an image generated with the prompt: 
      "a globe with the word 'Hello' on it."
    image:
      type: image
      source:
        kind: url
        url: https://github.com/receptron/mulmocast-media/raw/refs/heads/main/tutorial/helloworld_prompted.png
  - text: >
      You can also define a consistent image style across all beats.
      This is especially useful for maintaining visual coherence.
      For example, setting the style to "Ghibli-style anime" will apply that aesthetic to all generated images.
    image:
      type: markdown
      markdown: |
        # Image Style in MulmoScript
        ### JSON
        ```json
        {
          "$mulmocast": { "version": "1.0" },
          "imageParams": {
            "style": "Ghibli style anime"
          },
          "beats": [
            { 
              "text": "Hello World.",
              "imagePrompt": "A female model with business suit, holding a globe, saying hello"
            }
          ]
        }
        ```
  - text: >
      Here is another example with a more detailed prompt and style:
      "A female model in a business suit, holding a globe, saying hello" in Ghibli-style anime.
    image:
      type: image
      source:
        kind: url
        url: https://github.com/receptron/mulmocast-media/raw/refs/heads/main/tutorial/helloworld_ghibli.png
  - text: >
      Voice customization is supported through the speechParams property.
      You can specify the voice ID and voice provider, such as "nijivoice" or "openai".
      Here's an example using a Nijivoice-generated voice:
    image:
      type: markdown
      markdown: |
        # Speech Provider and Voice-Id
        ### JSON
        ```json
        {
          "$mulmocast": { "version": "1.0" },
          "imageParams": {
            "style": "Ghibli style anime"
          },
          "speechParams": {
            "provider": "nijivoice",
            "speakers": {
              "Presenter": {
                "voiceId": "3708ad43-cace-486c-a4ca-8fe41186e20c"
              }
            }
          },
          "beats": [
            { 
              "text": "こんにちは、世界の皆さん！",
              "imagePrompt": "A female model with business suit, holding a globe, saying hello"
            }
          ]
        }        
        ```
  - text: >
      こんにちは、世界の皆さん。
    audio:
      type: audio
      source:
        kind: url
        url: https://github.com/receptron/mulmocast-media/raw/refs/heads/main/tutorial/helloworld_nijivoice.mp3
    image:
      type: image
      source:
        kind: url
        url: https://github.com/receptron/mulmocast-media/raw/refs/heads/main/tutorial/helloworld_ghibli.png
  - text: >
      MulmoScript also supports PowerPoint-like slide decks.
      To create one, set the image type to "textSlide" and provide slide-specific properties.
    image:
      type: markdown
      markdown: |
        # Powerpoint-like Slide Deck in MulmoScript
        ### JSON
        ```json
        {
          "$mulmocast": {
            "version": "1.0",
          },
          "beats": [{
            "text": "Hello World.",
            "image": {
              "type": "textSlide",
              "slide": {
                "title": "Powerpoint-like Slide Desk",
                "bullets": ["Something", "Something else", "Something more", "Something even more"]
              }
            }
          }]
        }
        ```
  - text: >
      Here is an example of a Powerpoint-like slide deck.
    image:
      type: textSlide
      slide:
        title: "Powerpoint-like Slide Desk"
        bullets: ["Something", "Something else", "Something more", "Something even more"]
  - text: >
      You can also create diagrams like this using Mermaid.
    image:
      type: mermaid
      title: "Mermaid of MulmoScript"
      code:
        kind: text
        text: |
          flowchart TD
          MulmoScript --> MulmoCast["MulmoCast"]
          MulmoCast --> O_Video("Video") & O_Podcast("Podcast") & O_PDF("PDF Document") & O_Slideshow("Slide-Show") & O_Manga("Manga") & O_SwipeAnime("Swipe Anime")
          MulmoCast@{ shape: hex}
          O_Video:::ScriptOutput
          O_Podcast:::ScriptOutput
          O_Slideshow:::ScriptOutput
          O_PDF:::ScriptOutput
          O_Manga:::ScriptOutput
          O_SwipeAnime:::ScriptOutput
          classDef ScriptOutput stroke-width:1px, stroke-dasharray:none, stroke:#FBB35A, fill:#FFEFDB, color:#8F632D
  - text: >
      Charts are supported as well:
    image:
      type: chart
      title: Sales and Profits (from Jan to June)
      chartData:
        type: bar
        data:
          labels:
            - January
            - February
            - March
            - April
            - May
            - June
          datasets:
            - label: Revenue ($1000s)
              data: [120, 135, 180, 155, 170, 190]
              backgroundColor: rgba(54, 162, 235, 0.5)
              borderColor: rgba(54, 162, 235, 1)
              borderWidth: 1
            - label: Profit ($1000s)
              data: [45, 52, 68, 53, 61, 73]
              backgroundColor: rgba(75, 192, 192, 0.5)
              borderColor: rgba(75, 192, 192, 1)
              borderWidth: 1
        options:
          responsive: true
          animation: false
  - text: >
      Code snippets can be presented using Markdown formatting:
    image:
      type: markdown
      markdown: |
        # Hello World in MulmoScript
        ### Sample C++ code
        ```CPP
        #include <iostream>

        int main() {
            std::cout << "Hello, world!" << std::endl;

            int a = 5;
            int b = 7;
            int sum = a + b;

            std::cout << "The sum of " << a << " and " << b << " is " << sum << "." << std::endl;

            return 0;
        }
        ```
  - text: >
      MulmoScript is more than a tool—it's a bridge between AI and human communication.
      By giving large language models a structured, expressive format for presenting ideas, 
      MulmoScript unlocks new possibilities for storytelling, education, business, and creativity. 
      Whether you're generating videos, podcasts, documents, or interactive slides, 
      MulmoScript ensures that your content is not only machine-friendly but also engaging, coherent, 
      and beautifully delivered. Welcome to the future of AI-native presentations.
    image:
      type: image
      source:
        kind: url
        url: "https://raw.githubusercontent.com/receptron/mulmocast-cli/refs/heads/main/assets/images/mulmocast_credit.png"
    textSlideParams:
      cssStyles: "h1 { margin-top: 340px }"
