<!DOCTYPE html>
<html lang="${lang}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;700&display=swap');
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 16px;
      line-height: 1.6;
      color: #333;
      background: #fff;
    }
    
    @page {
      size: ${page_size};
      margin: 0;
    }
    
    .page {
      page-break-after: always;
      width: 100%;
      height: 100vh;
      position: relative;
      overflow: hidden;
      padding: 15px;
      display: ${page_layout};
      ${page_direction}
      gap: 15px;
      background: #fff;
    }
    
    .page:last-child {
      page-break-after: avoid;
    }
    
    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    
    .handout-item {
      display: flex;
      flex-direction: ${flex_direction};
      border: 1px solid #ddd;
      overflow: hidden;
      ${item_flex}
    }
    
    .handout-image {
      ${image_size}
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f9f9f9;
      padding: 5px;
    }
    
    .handout-text {
      ${text_size}
      padding: 8px;
      font-size: 14px;
      overflow: hidden;
      background: #fff;
    }
    
    .handout-text p {
      margin: 0.3em 0;
    }
  </style>
</head>
<body>
  ${pages}
</body>
</html>
