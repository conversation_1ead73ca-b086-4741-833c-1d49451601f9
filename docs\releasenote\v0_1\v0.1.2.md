# リリースノート v0.1.2

## 今回のリリースに含まれる Pull Request
## What's Changed
* update packages by @isamu in https://github.com/receptron/mulmocast-cli/pull/625
* htmlPrompt: Cache html by @snakajima in https://github.com/receptron/mulmocast-cli/pull/626
* udpate packages by @isamu in https://github.com/receptron/mulmocast-cli/pull/627

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.1.1...0.1.2

--- 以下、Generated by <PERSON> Code --- 

## Pull Request Summaries / PRサマリー

### PR #625: update packages - @isamu (https://github.com/receptron/mulmocast-cli/pull/625)
- **English**: Updated multiple package dependencies including Puppeteer (^24.11.2 → ^24.12.0), Zod (^3.25.74 → ^3.25.75), and TypeScript ESLint (^8.35.1 → ^8.36.0). This routine maintenance update ensures compatibility with latest package versions and includes security and bug fixes from upstream dependencies. The changes were automatically merged with all checks passing.
- **日本語**: Puppeteer（^24.11.2 → ^24.12.0）、Zod（^3.25.74 → ^3.25.75）、TypeScript ESLint（^8.35.1 → ^8.36.0）を含む複数のパッケージ依存関係を更新しました。この定期メンテナンス更新により、最新パッケージバージョンとの互換性を確保し、上流依存関係からのセキュリティ修正とバグ修正を含みます。すべてのチェックが通過して自動的にマージされました。

### PR #626: htmlPrompt: Cache html - @snakajima (https://github.com/receptron/mulmocast-cli/pull/626)
- **English**: Implemented HTML caching optimization to resolve performance issue where HTML was being regenerated unnecessarily when images were already cached. Modified `filterCacheAgentFilter` to cache text content, added `htmlReader` node with `defaultValue: {}` for caching `codeBlockOrRaw`, and extended `mulmoSessionStateSchema` with `html` field for tracking HTML generation state. This optimization prevents redundant HTML generation during image processing workflows.
- **日本語**: 画像が既にキャッシュされているにも関わらずHTMLが不必要に再生成されるパフォーマンス問題を解決するため、HTMLキャッシュ最適化を実装しました。`filterCacheAgentFilter`をテキストコンテンツもキャッシュするよう修正し、`codeBlockOrRaw`をキャッシュするための`defaultValue: {}`を持つ`htmlReader`ノードを追加し、HTML生成状態を追跡するための`html`フィールドで`mulmoSessionStateSchema`を拡張しました。この最適化により、画像処理ワークフロー中の冗長なHTML生成を防ぎ、htmlPromptベースのコンテンツ作成の全体的なパフォーマンスを向上させます。

### PR #627: udpate packages - @isamu (https://github.com/receptron/mulmocast-cli/pull/627)
- **English**: Updated package dependencies including dotenv (^17.0.1 → ^17.1.0), GraphAI (^2.0.11 → ^2.0.12), and Zod (^3.25.75 → ^3.25.76). This routine maintenance update brings latest versions of core dependencies with bug fixes and improvements. All automated checks passed successfully before merging.
- **日本語**: dotenv（^17.0.1 → ^17.1.0）、GraphAI（^2.0.11 → ^2.0.12）、Zod（^3.25.75 → ^3.25.76）を含むパッケージ依存関係を更新しました。この定期メンテナンス更新により、バグ修正と改善を含むコア依存関係の最新バージョンをもたらします。マージ前にすべての自動チェックが正常に通過しました。

---

## Developer Release Notes (English)

### Technical Improvements

- **HTML Caching Optimization** (#626): Implemented caching for HTML generation in htmlPrompt workflows to prevent unnecessary regeneration when images are already cached
- **Package Updates** (#625, #627): Updated core dependencies including GraphAI (2.0.12), Puppeteer (24.12.0), Zod (3.25.76), dotenv (17.1.0), and TypeScript ESLint (8.36.0)

This maintenance release focuses on performance optimization and dependency updates.

---

## Developer Release Notes (Japanese)

### 技術的改善

- **HTMLキャッシュ最適化** (#626): 画像が既にキャッシュされている場合の不必要な再生成を防ぐため、htmlPromptワークフローでのHTML生成キャッシュを実装
- **パッケージ更新** (#625, #627): GraphAI（2.0.12）、Puppeteer（24.12.0）、Zod（3.25.76）、dotenv（17.1.0）、TypeScript ESLint（8.36.0）を含むコア依存関係を更新

このメンテナンスリリースは、パフォーマンス最適化と依存関係更新に焦点を当て、最新パッケージバージョンとの互換性を維持しながらHTML生成効率を改善します。

---

## Creator Release Notes (English)

### Performance Improvements

**Faster HTML Content Generation**
- HTML-based slides and content now load faster when images are already cached
- Reduced processing time for projects using htmlPrompt features
- No changes needed to existing scripts - optimization works automatically

### System Updates
- Updated internal dependencies
- Updated compatibility with latest development tools

This release improves the performance of HTML-based content creation without requiring any changes to your existing MulmoScript files.

---

## Creator Release Notes (Japanese)

### パフォーマンス改善

**より高速なHTMLコンテンツ生成**
- 画像が既にキャッシュされている場合、HTMLベースのスライドとコンテンツの読み込みが高速化
- htmlPrompt機能を使用するプロジェクトの処理時間を短縮
- 既存のスクリプトに変更は不要 - 最適化は自動的に動作

### システム更新
- 内部依存関係を更新
- 最新の開発ツールとの互換性を更新

このリリースは、既存のMulmoScriptファイルに変更を必要とすることなく、HTMLベースのコンテンツ作成を最適化します。

## 品質チェック記録

**PRサマリーの品質確認**：
- [x] 各PRについて、タイトルだけでなくPR本文（説明文）を確認したか
- [x] PRのコメント欄をチェックし、作者による詳細説明を活用したか
- [x] 実際のコード変更内容を確認したか
- [x] 推測や誇張表現を避け、事実ベースの記述になっているか
- [x] 禁止表現（「革新的」「画期的」「プロフェッショナル」等）を使用していないか

**リリースノートの品質確認**：
- [x] 新機能に適切なサンプルファイルやドキュメントのリンクを追加したか（今回は該当なし）
- [x] リンク先ファイルの内容を確認し、機能との関連性を検証したか（今回は該当なし）
- [x] 開発者向け・クリエイター向けの4種類のリリースノートを作成したか
- [x] GitHub向けリリースノートをindex.mdに追加したか
- [x] 文量と詳細レベルがv0.0.17.mdを参考にして適切か

**最終チェック**：
- [x] prompt.mdの全ての条件と指示に従って作業したか
- [x] 各セクションが適切に分類されているか
- [x] 日本語の誤字脱字がないか（特に技術用語）
- [x] 全体的な整合性と一貫性が保たれているか

チェック完了日: 2025-07-09
チェック者: Claude Code