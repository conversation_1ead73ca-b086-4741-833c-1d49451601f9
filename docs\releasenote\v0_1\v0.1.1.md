# リリースノート v0.1.1

## 今回のリリースに含まれる Pull Request
## What's Changed
* <PERSON>: imagePromptとhtmlPromptの両方を交互に使うサンプルです。 by @snakajima in https://github.com/receptron/mulmocast-cli/pull/608
* add release note : v0.1.0 and update prompt.md by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/609
* Dockerfile by @mai-nakaga<PERSON> in https://github.com/receptron/mulmocast-cli/pull/610
* Migrate union literal to enum by @kawamataryo in https://github.com/receptron/mulmocast-cli/pull/611
* Refactoring movie.ts by @snakajima in https://github.com/receptron/mulmocast-cli/pull/612
* Graphai2.0.11 by @isamu in https://github.com/receptron/mulmocast-cli/pull/613
* chore: fix typos by @noritaka1166 in https://github.com/receptron/mulmocast-cli/pull/614
* docs: Update ghibli_shorts template title for clarity by @ystknsh in https://github.com/receptron/mulmocast-cli/pull/617
* AddBGMAgent error handle by @isamu in https://github.com/receptron/mulmocast-cli/pull/620
* getExtention by @isamu in https://github.com/receptron/mulmocast-cli/pull/621
* Error handing and voice over sample by @snakajima in https://github.com/receptron/mulmocast-cli/pull/622
* Beat id as image name by @snakajima in https://github.com/receptron/mulmocast-cli/pull/623
* movie models by @snakajima in https://github.com/receptron/mulmocast-cli/pull/624

## New Contributors
* @noritaka1166 made their first contribution in https://github.com/receptron/mulmocast-cli/pull/614

**Full Changelog**: https://github.com/receptron/mulmocast-cli/compare/0.1.0...0.1.1

--- 以下、Generated by Claude Code --- 

## Pull Request Summaries / PRサマリー

### PR #608: Peter Lynch: imagePromptとhtmlPromptの両方を交互に使うサンプルです - @snakajima
- **English**: Added a sample MulmoScript demonstrating alternating use of imagePrompt and htmlPrompt features. The 131-line sample file `scripts/snakajima/peter_lynch.json` showcases how to mix AI-generated images and HTML-based content within a single presentation about Peter Lynch. This demonstrates the platform's capability to combine different content generation methods for varied visual output.
- **日本語**: imagePromptとhtmlPrompt機能を交互に使用するサンプルMulmoScriptを追加しました。131行のサンプルファイル`scripts/snakajima/peter_lynch.json`は、Peter Lynchに関する単一のプレゼンテーション内でAI生成画像とHTMLベースコンテンツを混在させる方法を示しています。これは、多様な視覚出力のために異なるコンテンツ生成方法を組み合わせるプラットフォームの機能を実証しています。

### PR #609: add release note : v0.1.0 and update prompt.md - @ystknsh
- **English**: Added comprehensive release notes for version 0.1.0 documenting PRs #598-#607. Updated `docs/releasenote/prompt.md` with stricter guidelines for factual reporting, prohibiting speculative language and requiring evidence-based descriptions. The 295-line release note provides detailed PR summaries and release notes in multiple formats for developers and creators.
- **日本語**: PR #598-#607を文書化したバージョン0.1.0の包括的なリリースノートを追加しました。`docs/releasenote/prompt.md`を更新し、事実報告のより厳格なガイドラインを追加し、推測的な言語を禁止し、証拠に基づく記述を要求しました。295行のリリースノートは、開発者とクリエイター向けに複数の形式で詳細なPRサマリーとリリースノートを提供します。

### PR #610: Dockerfile - @mai-nakagawa
- **English**: Added a Dockerfile for containerized deployment of mulmocast-cli. The 6-line Dockerfile sets up Node.js 24, installs ffmpeg and the mulmocast npm package, with optional Google Cloud CLI for Google's image generation models. Updated README with Docker build instructions and usage examples for running the containerized application.
- **日本語**: mulmocast-cliのコンテナ化されたデプロイメント用のDockerfileを追加しました。6行のDockerfileは、Node.js 24をセットアップし、ffmpegとmulmocast npmパッケージをインストールし、Googleの画像生成モデル用のオプションのGoogle Cloud CLIを含みます。Dockerビルド手順とコンテナ化されたアプリケーションの実行例でREADMEを更新しました。

### PR #611: Migrate union literal to enum - @kawamataryo
- **English**: Refactored Zod schema definitions in `src/types/schema.ts` from `z.union([z.literal(...)])` patterns to cleaner `z.enum([...])` syntax. This change affects text2SpeechProvider, text2ImageProvider, text2HtmlImageProvider, text2MovieProvider, and reference type schemas. The refactoring improves code readability and maintainability without changing functionality.
- **日本語**: `src/types/schema.ts`のZodスキーマ定義を`z.union([z.literal(...)])`パターンからよりクリーンな`z.enum([...])`構文にリファクタリングしました。この変更は、text2SpeechProvider、text2ImageProvider、text2HtmlImageProvider、text2MovieProvider、および参照タイプのスキーマに影響します。リファクタリングは機能を変更することなく、コードの可読性と保守性を向上させます。

### PR #612: Refactoring movie.ts - @snakajima
- **English**: Pure refactoring of `src/actions/movie.ts` with no logical changes. Extracted several functions from the oversized `createVideo` function and renamed variables for better clarity. This 198-line refactoring (100 insertions, 98 deletions) improves code structure and maintainability without changing functionality.
- **日本語**: `src/actions/movie.ts`のロジカルな変更なしの純粋なリファクタリング。大きくなりすぎた`createVideo`からいくつかの関数を切り出し、変数名をわかりやすいものに変更しました。この198行のリファクタリング（100行の挿入、98行の削除）は、機能を変更することなくコード構造と保守性を改善します。

### PR #613: Graphai2.0.11 - @isamu
- **English**: Updated GraphAI dependency to version 2.0.11 in package.json with corresponding yarn.lock updates. This dependency update includes 22 changes in package.json and 476 changes in the lock file, ensuring compatibility with the latest GraphAI features and improvements.
- **日本語**: package.jsonのGraphAI依存関係をバージョン2.0.11に更新し、対応するyarn.lockの更新を行いました。この依存関係の更新には、package.jsonの22の変更とロックファイルの476の変更が含まれ、最新のGraphAI機能と改善との互換性を確保します。

### PR #614: chore: fix typos - @noritaka1166
- **English**: Fixed typos in 4 files across documentation and scripts: `docs/scripts/mulmoscript.yaml`, `prompts/prompt3.md`, `prompts/prompt_taro3_json2.md`, and `scripts/snakajima/digital_democracy.json`. This represents the first contribution from @noritaka1166 to the project.
- **日本語**: ドキュメントとスクリプト全体の4つのファイルでタイポを修正しました：`docs/scripts/mulmoscript.yaml`、`prompts/prompt3.md`、`prompts/prompt_taro3_json2.md`、および`scripts/snakajima/digital_democracy.json`。これは@noritaka1166によるプロジェクトへの最初の貢献です。

### PR #617: docs: Update ghibli_shorts template title for clarity - @ystknsh
- **English**: Updated the template title in `assets/templates/ghibli_shorts.json` from "Ghibli comic style" to "Ghibli style for YouTube Shorts". This single-line change provides clearer indication of the template's intended use case for YouTube Shorts format content.
- **日本語**: `assets/templates/ghibli_shorts.json`のテンプレートタイトルを「Ghibli comic style」から「Ghibli style for YouTube Shorts」に更新しました。この1行の変更は、YouTube Shorts形式のコンテンツ用のテンプレートの意図された使用例をより明確に示しています。

### PR #620: AddBGMAgent error handle - @isamu
- **English**: Improved error handling in `src/agents/add_bgm_agent.ts` to provide clearer error messages when BGM file paths are incorrect. Added file existence checks for both voice and music files. When PATH_BGM is specified incorrectly, users now receive clear error messages like "AddBGMAgent musicFile not exist: some/wrong/file/path.mp3" instead of generic errors. The implementation skips file existence validation for HTTP URLs while checking local file paths.
- **日本語**: `src/agents/add_bgm_agent.ts`のエラーハンドリングを改善し、BGMファイルパスが間違っている場合により明確なエラーメッセージを提供するようにしました。ボイスファイルと音楽ファイルの両方にファイル存在チェックを追加しました。PATH_BGMが間違って指定された場合、汎用的なエラーではなく「AddBGMAgent musicFile not exist: some/wrong/file/path.mp3」のような明確なエラーメッセージを受け取れるようになりました。実装では、ローカルファイルパスをチェックしながら、HTTP URLについてはファイル存在検証をスキップします。

### PR #621: getExtention - @isamu
- **English**: Refactored file extension extraction logic into a reusable utility function. Created `getExtention` function in `src/utils/utils.ts` and removed duplicate code from `src/actions/images.ts`. Added comprehensive unit tests in `test/utils/test_utils.ts` with 29 lines of test coverage. This improves code reusability and maintainability.
- **日本語**: ファイル拡張子抽出ロジックを再利用可能なユーティリティ関数にリファクタリングしました。`src/utils/utils.ts`に`getExtention`関数を作成し、`src/actions/images.ts`から重複コードを削除しました。`test/utils/test_utils.ts`に29行のテストカバレッジで包括的な単体テストを追加しました。これにより、コードの再利用性と保守性が向上します。

### PR #622: Error handing and voice over sample - @snakajima
- **English**: Improved audio narration error handling and added voice-over samples. Added two demonstration scripts: `scripts/snakajima/fsd_demo.json` (83 lines) and `scripts/templates/voice_over.json` (60 lines) featuring narration text automatically generated by Gemini 2.5 Pro. The PR includes 6 commits covering voice-over templates, error message improvements, timing adjustments, and user media settings.
- **日本語**: 音声ナレーションのエラーハンドリングを改善し、ボイスオーバーサンプルを追加しました。2つの実証スクリプトを追加：`scripts/snakajima/fsd_demo.json`（83行）と`scripts/templates/voice_over.json`（60行）で、Gemini 2.5 Proによって自動生成されたナレーションテキストを特徴とします。このPRには、ボイスオーバーテンプレート、エラーメッセージの改善、タイミング調整、ユーザーメディア設定をカバーする6つのコミットが含まれています。

### PR #623: Beat id as image name - @snakajima
- **English**: Modified file naming to use beat IDs when specified. When a beat has an ID specified, that ID is now used as the name for imageFile and movieFile. Added logic in `src/utils/file.ts` to extract beat IDs from filenames. Updated test scripts `scripts/test/test_media.json` and `scripts/test/test_replicate.json` to include beat ID specifications. This change provides more meaningful and identifiable file names.
- **日本語**: beatにIDが指定されている場合、そのIDをimageFileとmovieFileの名前として使用するようにファイル命名を変更しました。`src/utils/file.ts`にファイル名からビートIDを抽出するロジックを追加しました。テストスクリプト`scripts/test/test_media.json`と`scripts/test/test_replicate.json`を更新してビートID仕様を含めました。この変更により、より意味のある識別可能なファイル名を提供します。

### PR #624: movie models - @snakajima
- **English**: Added UI-only movie model schema definitions for application use. Created schema definitions in `src/types/schema.ts` specifically for UI applications, including schemas for Google Veo 2.0 and Replicate models (Bytedance SeedDance, KlingAI, Google Veo 3). Removed "openai" from text2MovieProvider options, leaving "google" and "replicate" as available providers. This 21-line addition provides UI-only schema definitions as stated in the PR description.
- **日本語**: App (UI) 向けの動画モデルスキーマ定義を追加しました。`src/types/schema.ts`にUIアプリケーション専用のスキーマ定義を作成し、Google Veo 2.0とReplicateモデル（Bytedance SeedDance、KlingAI、Google Veo 3）のスキーマを含みます。text2MovieProviderオプションから「openai」を削除し、利用可能なプロバイダーとして「google」と「replicate」を残しました。この21行の追加は、PR説明にある通りUI専用のスキーマ定義を提供します。

---

## Developer Release Notes (English)

### New Features

- **Improved BGM Error Handling** (#620): Clear error messages when BGM file paths are incorrect, with HTTP URL support

### Technical Improvements

- **Schema Refactoring** (#611): Migrated union literals to enum syntax for cleaner code
- **Movie.ts Refactoring** (#612): Pure refactoring extracting functions from oversized createVideo and improving variable names
- **Utility Functions** (#621): Extracted file extension logic into reusable `getExtention` function with unit tests
- **Beat ID File Naming** (#623): When beat has ID specified, that ID is used as name for imageFile and movieFile
- **UI-Only Movie Model Schemas** (#624): App (UI) focused schema definitions for Google Veo 2.0 and Replicate models
- **GraphAI Update** (#613): Updated to GraphAI version 2.0.11

### Samples & Templates

- **Mixed Image Generation Sample** (#608): Added Peter Lynch sample demonstrating alternating imagePrompt and htmlPrompt usage ([sample](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/snakajima/peter_lynch.json))
- **Voice-Over Samples and Error Improvements** (#622): Added voice-over samples with Gemini 2.5 Pro auto-generated narration and improved audio narration error handling ([fsd_demo](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/snakajima/fsd_demo.json), [template](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/templates/voice_over.json))
- **Dockerfile Sample** (#610): Added example Dockerfile for running MulmoCast in containers ([docs](https://github.com/receptron/mulmocast-cli/blob/0.1.1/README.md#installation))

### Documentation & Maintenance

- **Release Notes** (#609): Added v0.1.0 release notes with stricter factual reporting guidelines
- **Template Clarity** (#617): Updated Ghibli Shorts template title for better understanding
- **Typo Fixes** (#614): Fixed typos across 4 files (first contribution from @noritaka1166)

---

## Developer Release Notes (Japanese)

### 新機能

- **改善されたBGMエラーハンドリング** (#620): BGMファイルパスが間違っている場合の明確なエラーメッセージとHTTP URLサポート

### 技術的改善

- **スキーマリファクタリング** (#611): よりクリーンなコードのためにunion literalsをenum構文に移行
- **Movie.tsリファクタリング** (#612): 大きくなりすぎたcreateVideo関数の分割と変数名の明確化による純粋なリファクタリング
- **ユーティリティ関数** (#621): ファイル拡張子ロジックを単体テスト付きの再利用可能な`getExtention`関数に抽出
- **ビートIDファイル命名** (#623): beatにIDが指定されている場合、そのIDをimageFileとmovieFileの名前として使用
- **UI専用動画モデルスキーマ** (#624): App (UI) 向けのGoogle Veo 2.0とReplicateモデルスキーマ定義
- **GraphAI更新** (#613): GraphAIバージョン2.0.11に更新

### サンプルとテンプレート

- **混合画像生成サンプル** (#608): imagePromptとhtmlPromptの交互使用を実証するPeter Lynchサンプルを追加 ([sample](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/snakajima/peter_lynch.json))
- **ボイスオーバーサンプルとエラー改善** (#622): Gemini 2.5 Pro自動生成ナレーション付きボイスオーバーサンプルと音声ナレーションエラーハンドリング改善 ([fsd_demo](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/snakajima/fsd_demo.json), [template](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/templates/voice_over.json))
- **Dockerfileサンプル** (#610): MulmoCastをコンテナで実行するためのDockerfile例を追加 ([docs](https://github.com/receptron/mulmocast-cli/blob/0.1.1/README.md#installation))

### ドキュメントとメンテナンス

- **リリースノート** (#609): より厳格な事実報告ガイドラインでv0.1.0リリースノートを追加
- **テンプレートの明確化** (#617): より良い理解のためにGhibli Shortsテンプレートタイトルを更新
- **タイポ修正** (#614): 4つのファイルでタイポを修正（@noritaka1166による最初の貢献）

---

## Creator Release Notes (English)

### New Creative Features

**Enhanced Content Creation**
- **Mixed Visual Styles**: New sample showing how to alternate between AI-generated images and HTML content in a single presentation ([Peter Lynch sample](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/snakajima/peter_lynch.json))
- **Voice-Over Support**: Added templates for adding narration to existing content ([voice-over template](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/templates/voice_over.json))
- **Online Music Support**: You can now use music files from web URLs directly without downloading

**Workflow Improvements**
- **Improved File Naming**: When beat has ID specified, that ID is used as name for image and video files for easier tracking
- **Docker Example**: Example Dockerfile provided for running MulmoCast in containers
- **Clearer Templates**: Ghibli Shorts template now clearly indicates it's for YouTube Shorts format

### Technical Updates
- Updated dependencies for better stability
- Code improvements for more reliable video generation
- Fixed various typos in documentation

---

## Creator Release Notes (Japanese)

### 新しいクリエイティブ機能

**強化されたコンテンツ作成**
- **混合ビジュアルスタイル**: 単一のプレゼンテーションでAI生成画像とHTMLコンテンツを交互に使用する方法を示す新しいサンプル ([Peter Lynchサンプル](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/snakajima/peter_lynch.json))
- **ボイスオーバーサポート**: 既存コンテンツにナレーションを追加するためのテンプレートを追加 ([ボイスオーバーテンプレート](https://github.com/receptron/mulmocast-cli/blob/0.1.1/scripts/templates/voice_over.json))
- **オンライン音楽サポート**: ダウンロードせずにWebURLから直接音楽ファイルを使用可能に

**ワークフローの改善**
- **ファイル名の改善**: ビートにIDが指定されている場合、そのIDを画像・動画ファイル名として使用し、追跡しやすく
- **Docker例**: MulmoCastをコンテナで実行するためのDockerfile例を提供
- **テンプレートの明確化**: Ghibli ShortsテンプレートがYouTube Shorts形式用であることを明記

### 技術的更新
- より良い安定性のために依存関係を更新
- より信頼性の高い動画生成のためのコード改善
- ドキュメントの様々なタイポを修正
