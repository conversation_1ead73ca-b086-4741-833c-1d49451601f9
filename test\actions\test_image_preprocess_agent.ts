// this test is generated by claude code.
import test from "node:test";
import assert from "node:assert";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import type { MulmoStudioContext, MulmoBeat } from "../../src/types/index.js";
import { imagePreprocessAgent } from "../../src/actions/image_agents.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper function to create mock context
const createMockContext = (): MulmoStudioContext => ({
  fileDirs: {
    mulmoFilePath: "/test/path/test.yaml",
    mulmoFileDirPath: "/test/path",
    baseDirPath: "/test",
    outDirPath: "/test/output",
    imageDirPath: "/test/images",
    audioDirPath: "/test/audio",
  },
  studio: {
    filename: "test_studio",
    script: {
      title: "Test Script",
      beats: [],
      canvasSize: { width: 1920, height: 1080 },
    },
    beats: [],
    toJSON: () => "{}",
  },
  force: false,
  presentationStyle: {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
  },
  sessionState: {
    inSession: {
      audio: false,
      image: false,
      video: false,
      multiLingual: false,
      caption: false,
      pdf: false,
    },
    inBeatSession: {
      audio: {},
      image: {},
      movie: {},
      multiLingual: {},
      caption: {},
      html: {},
    },
  },
});

// Helper function to create mock beat
const createMockBeat = (overrides: Partial<MulmoBeat> = {}): MulmoBeat => ({
  text: "Test beat text",
  ...overrides,
});

test("imagePreprocessAgent - basic functionality", async () => {
  const context = createMockContext();
  const beat = createMockBeat();

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 0,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/0p.png",
    referenceImageForMovie: "/test/images/test_studio/0p.png",
    prompt: "generate image appropriate for the text. text: Test beat text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - with movie prompt and text", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test beat text",
    moviePrompt: "Generate a movie of this scene",
    // No explicit imagePrompt, so condition moviePrompt && !imagePrompt is true
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 1,
    imageRefs: {},
  });

  // Since moviePrompt exists and imagePrompt does NOT exist,
  // only imageParams, movieFile, and images are returned
  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/1.mov",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imagePath: "/test/images/test_studio/1p.png",
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - movie prompt only (no image prompt)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: undefined,
    moviePrompt: "Generate a movie of this scene",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 2,
    imageRefs: {},
  });

  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/2.mov",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imagePath: "/test/images/test_studio/2p.png",
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test.skip("imagePreprocessAgent - with image plugin (textSlide)", async () => {
  // Skip this test as it requires complex file system setup and Puppeteer
  // The plugin functionality is tested in the actual integration tests
});

test.skip("imagePreprocessAgent - with image plugin (markdown)", async () => {
  // Skip this test as it requires complex file system setup and Puppeteer
});

test.skip("imagePreprocessAgent - with image plugin (chart)", async () => {
  // Skip this test as it requires complex file system setup and Chart.js
});

test.skip("imagePreprocessAgent - with image plugin (mermaid)", async () => {
  // Skip this test as it requires complex file system setup and Mermaid rendering
});

test("imagePreprocessAgent - with imageNames", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    imageNames: ["image1", "image2"],
  });
  const imageRefs = {
    image1: "/path/to/image1.png",
    image2: "/path/to/image2.png",
    image3: "/path/to/image3.png", // Should not be included
  };

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 7,
    imageRefs,
  });

  const expected = {
    imagePath: "/test/images/test_studio/7p.png",
    referenceImageForMovie: "/test/images/test_studio/7p.png",
    prompt: "generate image appropriate for the text. text: Test beat text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: ["/path/to/image1.png", "/path/to/image2.png"],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - without imageNames (uses all imageRefs)", async () => {
  const context = createMockContext();
  const beat = createMockBeat();
  const imageRefs = {
    image1: "/path/to/image1.png",
    image2: "/path/to/image2.png",
    image3: "/path/to/image3.png",
  };

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 8,
    imageRefs,
  });

  const expected = {
    imagePath: "/test/images/test_studio/8p.png",
    referenceImageForMovie: "/test/images/test_studio/8p.png",
    prompt: "generate image appropriate for the text. text: Test beat text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: ["/path/to/image1.png", "/path/to/image2.png", "/path/to/image3.png"],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - filters undefined image references", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    imageNames: ["image1", "nonexistent", "image2"],
  });
  const imageRefs = {
    image1: "/path/to/image1.png",
    image2: "/path/to/image2.png",
  };

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 9,
    imageRefs,
  });

  const expected = {
    imagePath: "/test/images/test_studio/9p.png",
    referenceImageForMovie: "/test/images/test_studio/9p.png",
    prompt: "generate image appropriate for the text. text: Test beat text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: ["/path/to/image1.png", "/path/to/image2.png"],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - merges beat and imageAgentInfo imageParams", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    imageParams: {
      style: "vivid", // Should override imageAgentInfo style
      moderation: "auto", // Should override imageAgentInfo moderation
    },
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 10,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/10p.png",
    referenceImageForMovie: "/test/images/test_studio/10p.png",
    prompt: "generate image appropriate for the text. text: Test beat text\nvivid",
    imageParams: {
      provider: "openai",
      model: "dall-e-3", // From imageAgentInfo
      style: "vivid", // From beat (override)
      moderation: "auto", // From beat (override)
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "vivid",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - empty imageRefs", async () => {
  const context = createMockContext();
  const beat = createMockBeat();

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 12,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/12p.png",
    referenceImageForMovie: "/test/images/test_studio/12p.png",
    prompt: "generate image appropriate for the text. text: Test beat text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - with real sample data", async () => {
  // Load real sample script
  const scriptPath = path.join(__dirname, "../../scripts/test/test.json");
  const scriptData = JSON.parse(fs.readFileSync(scriptPath, "utf8"));

  const context = createMockContext();
  context.studio.filename = "test";

  // Test with the first beat that has imagePrompt
  const beatWithImagePrompt = scriptData.beats.find((beat: MulmoBeat) => beat.imagePrompt);
  if (beatWithImagePrompt) {
    const result = await imagePreprocessAgent({
      context,
      beat: beatWithImagePrompt,
      index: 1,
      imageRefs: {},
    });

    const expected = {
      imagePath: "/test/images/test/1p.png",
      referenceImageForMovie: "/test/images/test/1p.png",
      prompt: "Blue sky, a flock of birds\n<style>sumie-style",
      imageParams: {
        provider: "openai",
        model: "dall-e-3", // From imageAgentInfo
        style: "<style>sumie-style", // From beat override
        moderation: "auto", // From imageAgentInfo
      },
      movieFile: undefined,
      movieAgentInfo: {
        agent: "movieReplicateAgent",
        movieParams: {},
      },
      referenceImages: [],
      beatDuration: undefined,
      imageAgentInfo: {
        agent: "imageOpenaiAgent",
        imageParams: {
          model: "dall-e-3",
          moderation: "auto",
          provider: "openai",
          style: "<style>sumie-style",
        },
      },
    };

    assert.deepStrictEqual(result, expected);
  }
});

// Text, imagePrompt, moviePrompt combination patterns
test("imagePreprocessAgent - text only", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Only text content",
    // No imagePrompt, no moviePrompt
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 13,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/13p.png",
    referenceImageForMovie: "/test/images/test_studio/13p.png",
    prompt: "generate image appropriate for the text. text: Only text content\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - imagePrompt only", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: undefined,
    imagePrompt: "Only image prompt",
    // No moviePrompt
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 14,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/14p.png",
    referenceImageForMovie: "/test/images/test_studio/14p.png",
    prompt: "Only image prompt\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - moviePrompt only", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: undefined,
    // No imagePrompt
    moviePrompt: "Only movie prompt",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 15,
    imageRefs: {},
  });

  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    imagePath: "/test/images/test_studio/15p.png",
    movieFile: "/test/images/test_studio/15.mov",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - text + moviePrompt (no imagePrompt)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Text with movie",
    // No imagePrompt
    moviePrompt: "Movie prompt",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 16,
    imageRefs: {},
  });

  // When moviePrompt is present and imagePrompt is NOT present,
  // the function returns only imageParams, movieFile, and images (no imagePath or prompt)
  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    imagePath: "/test/images/test_studio/16p.png",
    movieFile: "/test/images/test_studio/16.mov",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - imagePrompt + moviePrompt (no text)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: undefined,
    imagePrompt: "Image prompt",
    moviePrompt: "Movie prompt",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 17,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/17p.png",
    referenceImageForMovie: "/test/images/test_studio/17p.png",
    prompt: "Image prompt\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/17.mov",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - text + imagePrompt + moviePrompt (all three)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Text content",
    imagePrompt: "Image prompt",
    moviePrompt: "Movie prompt",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 18,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/18p.png",
    referenceImageForMovie: "/test/images/test_studio/18p.png",
    prompt: "Image prompt\nnatural", // imagePrompt takes precedence over text
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/18.mov",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - no text, no imagePrompt, no moviePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: undefined,
    // No imagePrompt, no moviePrompt
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 19,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/19p.png",
    referenceImageForMovie: "/test/images/test_studio/19p.png",
    prompt: "generate image appropriate for the text. text: undefined\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - with both text and imagePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Beat text content",
    imagePrompt: "Custom image prompt",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 20,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/20p.png",
    referenceImageForMovie: "/test/images/test_studio/20p.png",
    prompt: "Custom image prompt\nnatural", // imagePrompt takes precedence
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - with imageParams override", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    imagePrompt: "A beautiful sunset",
    imageParams: {
      provider: "openai",
      style: "photorealistic",
      model: "dall-e-2",
    },
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 21,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/21p.png",
    referenceImageForMovie: "/test/images/test_studio/21p.png",
    prompt: "A beautiful sunset\nphotorealistic",
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        provider: "openai",
        style: "photorealistic",
        model: "dall-e-2",
        moderation: "auto",
      },
    },
    imageParams: {
      provider: "openai",
      model: "dall-e-2", // From beat override
      style: "photorealistic", // From beat override
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
  };

  assert.deepStrictEqual(result, expected);
});

// soundEffectPrompt parameter tests(no movie)
test("imagePreprocessAgent - with soundEffectPrompt only", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    soundEffectPrompt: "Birds chirping in the background",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 22,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/22p.png",
    referenceImageForMovie: "/test/images/test_studio/22p.png",
    prompt: "generate image appropriate for the text. text: Test text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

// no movie
test("imagePreprocessAgent - soundEffectPrompt + imagePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    imagePrompt: "A forest scene",
    soundEffectPrompt: "Birds chirping and wind blowing",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 23,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/23p.png",
    referenceImageForMovie: "/test/images/test_studio/23p.png",
    prompt: "A forest scene\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - soundEffectPrompt + moviePrompt (no imagePrompt)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    moviePrompt: "A forest scene with movement",
    soundEffectPrompt: "Birds chirping and leaves rustling",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 24,
    imageRefs: {},
  });

  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    imagePath: "/test/images/test_studio/24p.png",
    movieFile: "/test/images/test_studio/24.mov",
    soundEffectAgentInfo: {
      agentName: "soundEffectReplicateAgent",
      defaultModel: "zsxkib/mmaudio",
      models: ["zsxkib/mmaudio"],
      modelParams: {
        "zsxkib/mmaudio": {
          identifier: "zsxkib/mmaudio:62871fb59889b2d7c13777f08deb3b36bdff88f7e1d53a50ad7694548a41b484",
        },
      },
    },
    soundEffectModel: "zsxkib/mmaudio",
    soundEffectFile: "/test/images/test_studio/24_sound.mov",
    soundEffectPrompt: "Birds chirping and leaves rustling",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - soundEffectPrompt + imagePrompt + moviePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    imagePrompt: "A peaceful forest",
    moviePrompt: "Trees swaying gently",
    soundEffectPrompt: "Nature sounds with gentle breeze",
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 25,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/25p.png",
    referenceImageForMovie: "/test/images/test_studio/25p.png",
    prompt: "A peaceful forest\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/25.mov",
    soundEffectAgentInfo: {
      agentName: "soundEffectReplicateAgent",
      defaultModel: "zsxkib/mmaudio",
      models: ["zsxkib/mmaudio"],
      modelParams: {
        "zsxkib/mmaudio": {
          identifier: "zsxkib/mmaudio:62871fb59889b2d7c13777f08deb3b36bdff88f7e1d53a50ad7694548a41b484",
        },
      },
    },
    soundEffectModel: "zsxkib/mmaudio",
    soundEffectFile: "/test/images/test_studio/25_sound.mov",
    soundEffectPrompt: "Nature sounds with gentle breeze",
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

// enableLipSync parameter tests(no movie)
test("imagePreprocessAgent - with enableLipSync true", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 26,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/26p.png",
    referenceImageForMovie: "/test/images/test_studio/26p.png",
    prompt: "generate image appropriate for the text. text: Test text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/26_lipsync.mov",
    audioFile: undefined,
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

// no movie
test("imagePreprocessAgent - enableLipSync + imagePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    imagePrompt: "Portrait of a person speaking",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 27,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/27p.png",
    referenceImageForMovie: "/test/images/test_studio/27p.png",
    prompt: "Portrait of a person speaking\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/27_lipsync.mov",
    audioFile: undefined,
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - enableLipSync + moviePrompt (no imagePrompt)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    moviePrompt: "Person speaking with lip movement",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 28,
    imageRefs: {},
  });

  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    imagePath: "/test/images/test_studio/28p.png",
    movieFile: "/test/images/test_studio/28.mov",
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/28_lipsync.mov",
    audioFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - enableLipSync + imagePrompt + moviePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    imagePrompt: "Close-up of person's face",
    moviePrompt: "Subtle lip movements while speaking",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 29,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/29p.png",
    referenceImageForMovie: "/test/images/test_studio/29p.png",
    prompt: "Close-up of person's face\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/29.mov",
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/29_lipsync.mov",
    audioFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

// Combined soundEffectPrompt + enableLipSync tests ( no movie)
test("imagePreprocessAgent - soundEffectPrompt + enableLipSync", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    soundEffectPrompt: "Background music",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 30,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/30p.png",
    referenceImageForMovie: "/test/images/test_studio/30p.png",
    prompt: "generate image appropriate for the text. text: Test text\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/30_lipsync.mov",
    audioFile: undefined,
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

// no movie
test("imagePreprocessAgent - soundEffectPrompt + enableLipSync + imagePrompt", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    imagePrompt: "Singer performing on stage",
    soundEffectPrompt: "Applause and background music",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 31,
    imageRefs: {},
  });

  const expected = {
    imagePath: "/test/images/test_studio/31p.png",
    referenceImageForMovie: "/test/images/test_studio/31p.png",
    prompt: "Singer performing on stage\nnatural",
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/31_lipsync.mov",
    audioFile: undefined,
    referenceImages: [],
    beatDuration: undefined,
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - soundEffectPrompt + enableLipSync + moviePrompt (no imagePrompt)", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Test text",
    moviePrompt: "Performance with synchronized audio and lip movement",
    soundEffectPrompt: "Live concert atmosphere",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 32,
    imageRefs: {},
  });

  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    imagePath: "/test/images/test_studio/32p.png",
    movieFile: "/test/images/test_studio/32.mov",
    soundEffectAgentInfo: {
      agentName: "soundEffectReplicateAgent",
      defaultModel: "zsxkib/mmaudio",
      models: ["zsxkib/mmaudio"],
      modelParams: {
        "zsxkib/mmaudio": {
          identifier: "zsxkib/mmaudio:62871fb59889b2d7c13777f08deb3b36bdff88f7e1d53a50ad7694548a41b484",
        },
      },
    },
    soundEffectModel: "zsxkib/mmaudio",
    soundEffectFile: "/test/images/test_studio/32_sound.mov",
    soundEffectPrompt: "Live concert atmosphere",
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/32_lipsync.mov",
    audioFile: undefined,
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
    imageFromMovie: true,
    beatDuration: undefined,
  };

  assert.deepStrictEqual(result, expected);
});

test("imagePreprocessAgent - all parameters: soundEffectPrompt + enableLipSync + imagePrompt + moviePrompt + text", async () => {
  const context = createMockContext();
  const beat = createMockBeat({
    text: "Welcome to our presentation",
    imagePrompt: "Professional speaker at podium",
    moviePrompt: "Speaker gesturing while presenting",
    soundEffectPrompt: "Ambient conference room sound",
    enableLipSync: true,
  });

  const result = await imagePreprocessAgent({
    context,
    beat,
    index: 33,
    imageRefs: {},
  });

  const expected = {
    imageParams: {
      provider: "openai",
      model: "dall-e-3",
      style: "natural",
      moderation: "auto",
    },
    movieFile: "/test/images/test_studio/33.mov",
    soundEffectAgentInfo: {
      agentName: "soundEffectReplicateAgent",
      defaultModel: "zsxkib/mmaudio",
      models: ["zsxkib/mmaudio"],
      modelParams: {
        "zsxkib/mmaudio": {
          identifier: "zsxkib/mmaudio:62871fb59889b2d7c13777f08deb3b36bdff88f7e1d53a50ad7694548a41b484",
        },
      },
    },
    soundEffectModel: "zsxkib/mmaudio",
    soundEffectFile: "/test/images/test_studio/33_sound.mov",
    soundEffectPrompt: "Ambient conference room sound",
    lipSyncAgentName: "lipSyncReplicateAgent",
    lipSyncModel: "bytedance/omni-human",
    lipSyncFile: "/test/images/test_studio/33_lipsync.mov",
    audioFile: undefined,
    beatDuration: undefined,
    imagePath: "/test/images/test_studio/33p.png",
    referenceImageForMovie: "/test/images/test_studio/33p.png",
    imageAgentInfo: {
      agent: "imageOpenaiAgent",
      imageParams: {
        model: "dall-e-3",
        moderation: "auto",
        provider: "openai",
        style: "natural",
      },
    },
    prompt: "Professional speaker at podium\nnatural",
    referenceImages: [],
    movieAgentInfo: {
      agent: "movieReplicateAgent",
      movieParams: {},
    },
  };

  assert.deepStrictEqual(result, expected);
});
